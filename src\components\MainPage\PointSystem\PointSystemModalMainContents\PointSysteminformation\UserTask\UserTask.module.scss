@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 90%;
  padding: 0 2rem;
  @include row-center;

  .userTask {
    width: 100%;
    align-self: flex-start;
    margin: 2rem 0 5rem;

    .title {
      font-size: 1.5rem;
      color: $color-primary;
      @include row-center;
      justify-content: flex-start;
      gap: 1rem;

      img {
        width: 200px;
      }
    }

    .taskCardContainer {
      width: 100%;
      margin-top: 1rem;
      @include col-center;
      align-items: flex-start;

      .taskCard {
        width: 95%;
        @include row-between;

        .details {
          width: 100%;
          flex: 1;
          h3 {
            font-size: 1.5rem;
            color: $color-primary;
          }

          p {
            font-size: 1.5rem;
            color: $color-primary-contrast;
          }
        }
      }
    }
  }

  .icon {
    width: 30%;
    align-self: flex-start;
  }
}

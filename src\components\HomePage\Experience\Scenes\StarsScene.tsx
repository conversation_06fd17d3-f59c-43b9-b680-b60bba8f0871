import { Stars } from "@react-three/drei";
import { memo } from "react";

const StarsScene = () => {
  const starsSpeedInitial = 0.1;
  const starsSppedOffset = 0.1;
  return (
    <>
      {[...new Array(5)].map((_, index) => (
        <Stars
          key={index}
          speed={starsSpeedInitial + index * starsSppedOffset}
          // depth={1}
          count={1000}
        />
      ))}
    </>
  );
};

export default memo(StarsScene);

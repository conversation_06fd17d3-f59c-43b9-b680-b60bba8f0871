import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>_<PERSON>, Orbitron } from "next/font/google";
import localFont from "next/font/local";

export const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  style: ["normal"],
  variable: "--font-poppins",
});

export const saira = Saira({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
  style: ["normal"],
  variable: "--font-saira",
});

export const mavenPro = Maven_Pro({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
  display: "swap",
  style: ["normal"],
  variable: "--font-maven-pro",
});

export const garalama = localFont({
  src: [
    {
      path: "../assets/fonts/garalama.ttf",
      weight: "400, 500, 600, 700, 800, 900",
      style: "normal",
    },
  ],
  display: "swap",
  variable: "--font-garalama",
});

export const orbitron = Orbitron({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
  display: "swap",
  style: ["normal"],
  variable: "--font-orbitron",
});
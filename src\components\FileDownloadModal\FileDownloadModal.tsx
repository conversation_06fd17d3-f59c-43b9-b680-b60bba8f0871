import React, { useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useSnapshot } from "valtio";
import styles from "./FileDownloadModal.module.scss";
import { downloadModalStore } from "@/stores/downloadModal";
import { mineFiles, DownloadableFile } from "@/constants/mineDetails";
import { fadeIn, buttonEffect } from "@/animations/animations";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import FileItem from "./FileItem/FileItem";
import DownloadProgress from "./DownloadProgress/DownloadProgress";
import { downloadAsArchive } from "@/utils/zipDownload";
import { showSuccess, showError } from "@/lib/notification";

const FileDownloadModal: React.FC = () => {
  const downloadModalSnapshot = useSnapshot(downloadModalStore);

  const availableFiles = useMemo(() => {
    return mineFiles[downloadModalSnapshot.selectedMine] || [];
  }, [downloadModalSnapshot.selectedMine]);

  const groupedFiles = useMemo(() => {
    const groups: { [category: string]: DownloadableFile[] } = {};
    availableFiles.forEach((file) => {
      if (!groups[file.category]) {
        groups[file.category] = [];
      }
      groups[file.category].push(file);
    });
    return groups;
  }, [availableFiles]);

  const selectedCount = downloadModalSnapshot.selectedFiles.size;
  const totalFiles = availableFiles.length;
  const allSelected = totalFiles > 0 && selectedCount === totalFiles;

  const handleSelectAll = () => {
    const allFileIds = availableFiles.map((file) => file.id);
    downloadModalStore.selectAllFiles(allFileIds);
  };

  const handleDownload = async () => {
    if (selectedCount === 0) return;

    downloadModalStore.setIsDownloading(true);

    try {
      const selectedFilesList = availableFiles.filter((file) =>
        downloadModalSnapshot.selectedFiles.has(file.id),
      );

      // Convert to the format expected by the utility function
      const filesToDownload = selectedFilesList.map((file) => ({
        name: file.name,
        url: file.url,
      }));

      const archiveName = `${downloadModalSnapshot.selectedMine}-documents`;

      // Use the utility function for downloading
      await downloadAsArchive(filesToDownload, archiveName, (progress) => {
        downloadModalStore.setDownloadProgress(progress);
      });

      // Show success notification
      showSuccess(
        selectedFilesList.length === 1
          ? `Downloaded: ${selectedFilesList[0].name}`
          : `Downloaded ${selectedFilesList.length} files from ${downloadModalSnapshot.selectedMine}`,
      );
    } catch (error) {
      console.error("Download failed:", error);
      showError("Download failed. Please try again.");
    } finally {
      setTimeout(() => {
        downloadModalStore.setIsDownloading(false);
        downloadModalStore.setIsOpen(false);
      }, 1000);
    }
  };

  const handleClose = () => {
    if (!downloadModalSnapshot.isDownloading) {
      downloadModalStore.setIsOpen(false);
    }
  };

  if (!downloadModalSnapshot.isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className={styles.overlay}
        variants={fadeIn(0.3)}
        initial="hidden"
        animate="visible"
        exit="hidden"
        onClick={handleClose}
      >
        <motion.div
          className={styles.modal}
          variants={fadeIn(0.5)}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={styles.header}>
            <div className={styles.titleSection}>
              <h2>Download Documents</h2>
              <p>{downloadModalSnapshot.selectedMine}</p>
            </div>
            <motion.button
              className={styles.closeButton}
              whileTap={buttonEffect.tap}
              onClick={handleClose}
              disabled={downloadModalSnapshot.isDownloading}
            >
              <img src={crossIcon.src} alt="Close" />
            </motion.button>
          </div>

          {/* Content */}
          <div className={styles.content}>
            {downloadModalSnapshot.isDownloading ? (
              <DownloadProgress />
            ) : (
              <>
                {/* Selection Controls */}
                <div className={styles.controls}>
                  <motion.button
                    className={styles.selectAllButton}
                    whileTap={buttonEffect.tap}
                    onClick={handleSelectAll}
                  >
                    {allSelected ? "Deselect All" : "Select All"}
                  </motion.button>
                  <span className={styles.selectionCount}>
                    {selectedCount} of {totalFiles} files selected
                  </span>
                </div>

                {/* File Groups */}
                <div className={styles.fileGroups}>
                  {Object.entries(groupedFiles).map(([category, files]) => (
                    <div key={category} className={styles.fileGroup}>
                      <h3 className={styles.categoryTitle}>{category}</h3>
                      <div className={styles.fileList}>
                        {files.map((file) => (
                          <FileItem
                            key={file.id}
                            file={file}
                            isSelected={downloadModalSnapshot.selectedFiles.has(
                              file.id,
                            )}
                            onToggle={() =>
                              downloadModalStore.toggleFileSelection(file.id)
                            }
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>

          {/* Footer */}
          {!downloadModalSnapshot.isDownloading && (
            <div className={styles.footer}>
              <motion.button
                className={styles.cancelButton}
                whileTap={buttonEffect.tap}
                onClick={handleClose}
              >
                Cancel
              </motion.button>
              <motion.button
                className={`${styles.downloadButton} ${
                  selectedCount === 0 ? styles.disabled : ""
                }`}
                whileTap={selectedCount > 0 ? buttonEffect.tap : {}}
                onClick={handleDownload}
                disabled={selectedCount === 0}
              >
                {selectedCount > 1
                  ? `Download ${selectedCount} Files Individually`
                  : selectedCount === 1
                  ? "Download File"
                  : "Select Files to Download"}
              </motion.button>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default FileDownloadModal;

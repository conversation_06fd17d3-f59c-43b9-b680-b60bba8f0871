@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.wrapper {
  width: 100%;
  height: 100%;
  background: $color-black-transparent-light;
  backdrop-filter: blur(10px);
  mask-image: linear-gradient(to bottom, black 65%, transparent);
  padding: $padding-lg;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    background: linear-gradient(
      to bottom,
      $color-primary,
      $color-black-transparent
    );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 2rem) 0,
      100% 2rem,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      0.3rem calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 0.2rem) calc(2rem + 0.83px),
      calc(100% - 2rem - 0.83px) 1px,
      1px 1px
    );
  }
}
.headerWrapper {
  @include row-between;
  margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.filter {
  width: 100%;
  // border: 1px solid yellow;
  @include row-between;

  margin-top: $margin-sm;
  margin-bottom: $margin-sm;
  .list {
    font-size: $font-size-sm;
    color: rgba(#848e9c, 0.7);
    cursor: pointer;
    text-transform: uppercase;
  }
}

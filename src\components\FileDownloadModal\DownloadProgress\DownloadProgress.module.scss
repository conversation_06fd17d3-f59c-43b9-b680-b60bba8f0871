@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.progressContainer {
  @include col-center;
  padding: $padding-5xl;
  text-align: center;

  @media (max-width: 768px) {
    padding: $padding-3xl;
  }
}

.progressHeader {
  margin-bottom: $margin-lg;

  h3 {
    font-family: $font-family-orbitron;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $color-primary;
    margin: 0 0 $margin-md 0;
    text-transform: uppercase;
    letter-spacing: 1px;

    @media (max-width: 768px) {
      font-size: $font-size-xl;
    }
  }

  p {
    font-family: $font-family-saira;
    font-size: $font-size-md;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;

    @media (max-width: 768px) {
      font-size: $font-size-sm;
    }
  }
}

.progressBar {
  width: 100%;
  max-width: 400px;
  height: 8px;
  background: rgba(0, 196, 208, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: $margin-lg;
  position: relative;

  .progressFill {
    height: 100%;
    background: linear-gradient(90deg, $color-primary, $color-primary-contrast);
    border-radius: 4px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
      );
      animation: shimmer 2s infinite;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progressText {
  font-family: $font-family-orbitron;
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: white;
  margin-bottom: $margin-lg;

  @media (max-width: 768px) {
    font-size: $font-size-lg;
  }
}

.progressDetails {
  .downloadingFiles {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-md;
    font-family: $font-family-saira;
    font-size: $font-size-md;
    color: rgba(255, 255, 255, 0.8);

    span:first-child {
      font-size: $font-size-xl;
      animation: bounce 2s infinite;
    }

    @media (max-width: 768px) {
      font-size: $font-size-sm;
      flex-direction: column;
      gap: $spacing-sm;
    }
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

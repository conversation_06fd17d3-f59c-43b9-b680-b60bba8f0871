import { Trail } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";
import { Ref, RefObject, useRef } from "react";
import { BufferGeometry, Material, Mesh, NormalBufferAttributes } from "three";
import { useMediaQuery } from "usehooks-ts";

type TrailRef = THREE.Mesh;

const ElectronsScene = () => {
  const isMobile = useMediaQuery("(max-width: 1279px)");
  return (
    <>
      <group>
        {/* <Electron radius={[2, 0, 2]} rotation={[0.5, 0, 1]} speed={6} />
        <Electron radius={[2, 0, 2]} rotation={[-0.4, 0, -1]} speed={6} /> */}
        {isMobile ? (
          <Electron radius={[1.8, 0, 2]} rotation={[0.1, 0, 0]} speed={1.5} />
        ) : (
          <Electron radius={[2.5, 0, 2.5]} rotation={[0.1, 0, 0]} speed={1.5} />
        )}
      </group>
    </>
  );
};

const Electron = ({
  radius = [2.75, 2.75, 2.75],
  rotation = [0, 0, 0],
  speed = 6,
  scale = 5,
}) => {
  const ref = useRef<TrailRef>();
  useFrame(({ clock }) => {
    if (!ref.current) return;
    ref.current.position.x =
      Math.sin(clock.getElapsedTime() * speed) * radius[0];
    ref.current.position.y =
      Math.sin(clock.getElapsedTime() * speed) * radius[1];
    ref.current.position.z =
      Math.cos(clock.getElapsedTime() * speed) * radius[2];
  });
  return (
    <group
      position={[0, 0, 0]}
      scale={[scale, scale, scale]}
      rotation={[rotation[0], rotation[1], rotation[2]]}
    >
      <Trail
        // local
        width={2}
        length={15}
        color={"#00C4D0"}
        attenuation={(t) => t * t * t}
      >
        <mesh ref={ref as Ref<TrailRef>}>
          <sphereGeometry args={[0.025]} />
          <meshBasicMaterial color={"#00656B"} toneMapped={false} />
        </mesh>
      </Trail>
    </group>
  );
};

export default ElectronsScene;

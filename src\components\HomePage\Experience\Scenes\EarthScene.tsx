import { useTexture } from "@react-three/drei";
import { useThree } from "@react-three/fiber";
import { memo } from "react";
import { useMediaQuery } from "usehooks-ts";

const EarthScene = () => {
  const { scene } = useThree();
  const [earthNight] = useTexture(["/textures/earthNightV2.jpeg"]);
  const isMobileTablet = useMediaQuery("(max-width: 1279px)");
  earthNight.offset.set(0.625, -0.125);

  //   scene.background = galaxy;
  return (
    <>
      <ambientLight position={[0, 0, -200]} intensity={0.5} />
      <mesh position={[0, 0, -200]}>
        <sphereGeometry
          attach="geometry"
          args={[isMobileTablet ? 4 : 5, 32, 32]}
        />
        <meshPhongMaterial attach="material" map={earthNight} />
      </mesh>
    </>
  );
};

export default memo(EarthScene);

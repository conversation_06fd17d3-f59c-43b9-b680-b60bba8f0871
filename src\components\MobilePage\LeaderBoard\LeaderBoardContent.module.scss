@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  // @include row-between;
  @include row-center;
  margin-bottom: $margin-md;
  position: relative;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-family: poppins;
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      letter-spacing: 0.1em;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 90%;
  height: 90%;
  overflow: scroll;
  padding-right: $padding-md;
  padding-bottom: $padding-lg;
  gap: 2rem;

  &::-webkit-scrollbar {
    display: none;
  }

  .loginNote {
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    color: $color-primary; // Updated to cyan color from image
    font-size: $font-size-md;
    margin: $margin-md 0;
  }

  .loginButton {
    position: relative;
    text-align: center;
    background: transparent;
    padding: $padding-md;
    color: $color-primary;
    font-size: $font-size-md;
    border: none;
    cursor: pointer;
    z-index: 1;
  }

  .loginButton::before {
    content: "";
    position: absolute;
    top: -0.5px;
    left: -0.5px;
    right: -0.5px;
    bottom: -0.5px;
    background: linear-gradient(90deg, #000000 0%, $color-primary 100%);
    border-radius: 6px;
    z-index: -1;
  }
  .loginButton::after {
    content: "";
    position: absolute;
    top: 0.5px;
    left: 0.5px;
    right: 0.5px;
    bottom: 0.5px;
    background: #000;
    border-radius: 4px;
    z-index: -1;
  }

  .loginButton:hover::before {
    box-shadow: 0 0 15px $color-primary-transparent-contrast;
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  @include row-between;
  margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 90%;
  height: 90%;
  overflow: scroll;
  padding-right: $padding-md;
  padding-bottom: $padding-lg;
  // padding-right: $padding-md;

  &::-webkit-scrollbar {
    display: none;
  }

  .netWork {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .blockChainInfo {
    color: $color-primary;
    font-size: 0.9rem;
    // font-family: "Garalama", sans-serif;
    margin-bottom: 0.1rem;
    // font-weight: $font-weight-extralight;
  }

  .value {
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    color: yellow;
    letter-spacing: 0.1rem;
  }

  .bodyWrapper {
    position: relative;
    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5); // 半透明黑色遮罩
      z-index: 10;
      opacity: 0; // 默认不显示
      pointer-events: none; // 确保不会影响底层元素的点击
      transition: opacity 0.3s ease; // 平滑过渡效果
    }

    &.unsupported {
      cursor: not-allowed;
      pointer-events: none; // 禁用点击

      &:before {
        opacity: 1; // 显示遮罩
      }
    }
    .titleContainer {
      width: 100%;
      @include row-center;
      justify-content: flex-start;
      padding: $padding-sm $padding-sm 0 0;
      gap: $spacing-sm;
      // border: 1px solid yellow;

      .subtitle {
        @include row-center;
        justify-content: flex-start;
        gap: $spacing-sm;

        .mineral {
          background: rgba(255, 255, 255, 0.07);
          padding: 0 $padding-xs;
          border-radius: $border-radius-sm;
          font-size: $font-size-2xs;
          color: yellow;
          font-weight: $font-weight-medium;
        }

        .location {
          font-size: $font-size-2xs;
          font-weight: $font-weight-bold;
          color: $color-primary;
        }
      }

      .title {
        font-size: $font-size-md;
        font-weight: $font-weight-semibold;
        color: $color-primary;
      }
    }

    .imgContainer {
      width: 100%;
      @include row-center;
      // justify-content: flex-start;
      padding-bottom: $padding-md;
      padding-top: $padding-sm;
      padding-left: $padding-sm;
      gap: $spacing-sm;

      .imageFrame {
        width: 100%;
        height: 4.5rem;
        position: relative;
        background: $color-primary-transparent;
        backdrop-filter: blur(20px);
        // padding: px;
        clip-path: polygon(
          0 0,
          calc(100% - 32px) 0,
          100% 32px,
          100% 100%,
          0 100%
        );
        // border: 1px solid yellow;

        &::before {
          content: "";
          position: absolute;
          inset: 0;
          background: $color-primary;
          clip-path: polygon(
            0 0,
            calc(100% - 32px) 0,
            100% 32px,
            100% 100%,
            0 100%,
            0 0,
            1px 1px,
            1px calc(100% - 1px),
            calc(100% - 1px) calc(100% - 1px),
            calc(100% - 1px) calc(32px + 0.41px),
            calc(100% - 32px - 0.41px) 1px,
            1px 1px
          );
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .information {
      width: 100%;
      // border-bottom: $border-width-2xs solid $color-primary-transparent;
      padding-bottom: $spacing-sm;

      .logoWrapper {
        // width: 100%;
        @include row-between;

        .title {
          font-size: $font-size-xs;
          color: $color-primary-transparent-contrast;
          font-weight: $font-weight-light;
          text-transform: uppercase;
        }

        .subtitle {
          font-size: $font-size-xs;
          color: $color-warning;
          font-weight: $font-weight-semibold;
        }
      }
    }
  }

  .bodyWrapper:not(:last-child) {
    border-bottom: 1px solid $color-primary;
  }
}

.filterContainer {
  width: 100%;
  @include row-between;
  font-family: "Garalama", sans-serif;
  letter-spacing: 1px;
  margin: $margin-sm 0;
  padding: 0 $padding-sm;
}

.filterButtons {
  display: flex;
  // border:2px solid $color-primary;
}

.filterButton {
  // background-color: $color-black-transparent;
  display: inline-block;
  cursor: pointer;
  color: $color-primary-transparent-contrast;
  font-size: $font-size-xs;
  font-weight: $font-weight-extralight;
  text-decoration: none;

  .filterButton:focus {
    font-size: $font-size-md;
  }
}

.active {
  transform: translateY(-2px);
  color: $color-primary;
  font-weight: $font-weight-light;
  font-size: $font-size-sm;
}

.switchButton {
  position: relative;
  width: 90%;
  text-align: center;
  background: transparent;
  padding: $padding-md;
  color: $color-primary;
  font-size: $font-size-md;
  border: none;
  cursor: pointer;
  z-index: 0;
  margin: $margin-md;
}

.switchButton::before {
  content: "";
  position: absolute;
  top: -0.5px;
  left: -0.5px;
  right: -0.5px;
  bottom: -0.5px;
  background: linear-gradient(90deg, #000000 0%, $color-primary 100%);
  border-radius: 6px;
  z-index: -2;
}
.switchButton::after {
  content: "";
  position: absolute;
  top: 0.5px;
  left: 0.5px;
  right: 0.5px;
  bottom: 0.5px;
  background: #000;
  border-radius: 6px;
  z-index: -2;
}

.switchButton:hover::before {
  box-shadow: 0 0 15px $color-primary-transparent-contrast;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000; // 增加 z-index 确保在最上层
}

.modalContent {
  position: fixed;
  width: 85%;
  padding: 1.5rem; // 增大间距
  margin: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) !important;
  background: #001a1a;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1001;

  &::-webkit-scrollbar {
    display: none;
  }

  h3 {
    color: $color-primary;
    margin-bottom: 1.5rem; // 增大标题与内容的间距
    line-height: 1.6;
  }

  .divider {
    width: 100%;
    height: 1px;
    background: rgba(255, 255, 255, 0.2); // 添加分割线
    margin: 1.5rem 0; // 上下间距
  }

  .networkMenuItem {
    margin-bottom: 1rem; // 增大内容项之间的间距
    color: white;
    font-size: $font-size-sm;
  }

  .activeNetwork {
    color: #00ffff;
    font-size: $font-size-md;
  }
}
.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #00ffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;

  &:hover {
    color: white;
  }
}
// .overlay {
//   background: black;
//   opacity: 0.5;
//   position: absolute;
//   width: 100%;
//   height: 0%;
//   // padding: $padding-md;
//   margin: $margin-md, 0;
//   z-index: 10;
// }

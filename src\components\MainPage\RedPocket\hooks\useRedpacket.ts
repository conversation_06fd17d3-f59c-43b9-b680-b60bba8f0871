import { networkConfigs } from "@/constants/networkConfigs";
import React, { useLayoutEffect, useState } from "react";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import { redPocketDetails } from "../RedPocket";
import { redpacketABI } from "@/constants/abis/RedpacketABI";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError, formatEther } from "viem";

type TopParticipant = {
  rank: number;
  address: string;
  amount: number;
  nftAmount: number;
  claimTime: string;
};

const PAGE_SIZE = 10n;

const trimWalletAddress = (address: string) =>
  `${address.slice(0, 6)}...${address.slice(-6)}`;

const formatTimestamp = (timestamp: bigint | undefined): string => {
  if (!timestamp) return "-";
  const date = new Date(Number(timestamp) * 1000);
  return date
    .toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(",", "");
};

const useRedpacket = () => {
  const [redpacketAddress, setRedpacketAddress] = useState<`0x${string}`>("0x");
  const [participated, setParticipated] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [isClaimAvaliable, setIsClaimAvaliable] = useState(false);
  const [avaliableHskBalance, setAvaliableHskBalance] = useState(0n);
  const [avaliableNftBalance, setAvaliableNftBalance] = useState(0n);
  const [userClaimedHskAmount, setUserClaimedHskAmount] = useState(0n);
  const [userClaimedNftAmount, setUserClaimedNftAmount] = useState(0n);
  const [topParticipants, setTopParticipants] = useState<TopParticipant[]>([]);
  const [totalParticipants, setTotalParticipants] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [startTime, setStartTime] = useState(0n);
  const [endTime, setEndTime] = useState(0n);
  const [errorMessage, setErrorMessage] = useState("");
  const [activityId, setActivityId] = useState(0n);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();

  useLayoutEffect(() => {
    if (chain?.id && redPocketDetails.supportedNetwork.includes(chain.id)) {
      setRedpacketAddress(networkConfigs[chain.id].redpacketAddress ?? "0x");
    } else {
      setRedpacketAddress("0x");
    }
  }, [chain]);

  // Get current activity id
  useContractRead({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "currentActivityId",
    enabled: isConnected && redpacketAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setActivityId(data);
    },
  });

  // Get event config including start and end time
  useContractRead({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "config",
    enabled: isConnected && redpacketAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      // data is a tuple, access by index: [minAmount, maxAmount, nftContract, nftTokenId, startTime, endTime]
      setStartTime(data[4]); // startTime is at index 4
      setEndTime(data[5]); // endTime is at index 5
    },
  });

  useContractRead({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "getUserRecord",
    args: [address ?? "0x", activityId],
    enabled: isConnected && address !== "0x" && redpacketAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setParticipated(data[0]);
      setUserClaimedHskAmount(data[1]);
      setUserClaimedNftAmount(data[2]);
    },
  });

  useContractRead({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "getContractBalances",
    enabled: isConnected && redpacketAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setIsClaimAvaliable(data[1] > 0n);
      setAvaliableHskBalance(data[0]);
      setAvaliableNftBalance(data[1]);
    },
  });

  useContractRead({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "getTotalClaimedAmount",
    args: [activityId],
    enabled: isConnected && redpacketAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setTotalParticipants(Number(data));
    },
  });

  useContractRead({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "getTopParticipants",
    args: [activityId, BigInt(currentPage) * PAGE_SIZE, PAGE_SIZE],
    enabled: isConnected && redpacketAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setTopParticipants(
        data[0].map((item, index) => ({
          rank: Number(BigInt(currentPage) * PAGE_SIZE) + index + 1,
          address: trimWalletAddress(item.userAddress),
          amount: Number(formatEther(item.claimedAmount)),
          nftAmount: Number(item.claimedNFTs),
          claimTime: formatTimestamp(item.claimTime),
        })),
      );
    },
  });

  const { config: claimConfig } = usePrepareContractWrite({
    address: redpacketAddress,
    abi: redpacketABI,
    functionName: "claimReward",
    args: [activityId],
    enabled: isConnected && redpacketAddress !== "0x",
    onError: (error: any) => {
      if (error.cause?.reason) {
        setErrorMessage(error.cause.reason);
      } else {
        setErrorMessage(error.shortMessage || error.message);
      }
    },
  });

  const { write: claimWrite, data: claimData } = useContractWrite({
    ...claimConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const { isLoading: isWaitingForClaim } = useWaitForTransaction({
    hash: claimData?.hash,
    onSuccess: () => {
      showSuccess("Successfully claimed Red Packet rewards!");
      setIsClaiming(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const handleClaim = async () => {
    if (
      !isConnected ||
      (chain?.id && !redPocketDetails.supportedNetwork.includes(chain.id))
    ) {
      return;
    }

    // Check if event has started
    const currentTimestamp = BigInt(Math.floor(Date.now() / 1000));
    if (currentTimestamp < startTime) {
      showError("HSK Reward event has not started yet");
      return;
    }

    // Check if event has ended
    if (currentTimestamp > endTime) {
      showError("HSK Reward event has ended");
      return;
    }

    // if (errorMessage) {
    //   showError(errorMessage);
    //   return;
    // }

    if (participated) {
      showError("You have already claimed your HSK Reward");
      return;
    }

    if (!isClaimAvaliable) {
      showError("No more HSK Reward available");
      return;
    }

    if (!claimWrite) {
      showError("Unable to claim HSK rewards at this time");
      return;
    }

    setIsClaiming(true);
    try {
      claimWrite?.();
    } catch (error) {
      setIsClaiming(false);
      showError("Failed to claim HSK rewards");
    }
  };

  return {
    handleClaim,
    isClaiming,
    isWaitingForClaim,
    participated,
    isClaimAvaliable,
    avaliableHskBalance,
    avaliableNftBalance,
    userClaimedHskAmount,
    userClaimedNftAmount,
    topParticipants,
    totalParticipants,
    currentPage,
    setCurrentPage,
    pageSize: Number(PAGE_SIZE),
    startTime,
    endTime,
  };
};

export default useRedpacket;

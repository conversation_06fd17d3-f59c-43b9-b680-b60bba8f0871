import styles from "./MinePreviewHeader.module.scss";
import headerIcon from "@/assets/icons/minesNavigation/mineHeaderIcon.png";
import locationIcon from "@/assets/icons/minesNavigation/locationIcon.png";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import Image from "next/image";
// import { MineCardProps } from "../MineCard";
// type Unpacked<T> = T extends (infer Item)[] ? Item : T;
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { MineCardProps } from "../MineCard";

const MinePreviewHeader = ({ onBack }: MineCardProps) => {

  const mineCardSnapshot = useSnapshot(mineCardStore);

  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  return (
    <div className={styles.container}>
      <div className={styles.leftContainer}>
        <Image src={headerIcon.src} alt="header icon" width={50} height={50}/>
        <div>
          <div className={styles.subtitle}>
            <h1 className={styles.mineral}>{information.mineMineral}</h1>
            <img src={locationIcon.src} alt="location icon" />
            <h1 className={styles.location}>{information.mineLocation}</h1>
          </div>
          <h1 className={styles.title}>{information.name}</h1>
        </div>
      </div>
      <div>
        <img
          className={styles.rightImage}
          src={crossIcon.src}
          alt="dotdotdot icon"
          onClick={onBack}
        />
      </div>
    </div>
  );
};

export default MinePreviewHeader;

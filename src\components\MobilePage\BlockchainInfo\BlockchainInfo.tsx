import { useEffect, useState } from "react";
import styles from "./BlockchainInfo.module.scss";
import { useNetwork, useBlockNumber, useFeeData } from "wagmi";

const BlockchainInfo = () => {
  const { chain } = useNetwork();

  // 使用 wagmi hooks 监听区块和 gas 价格
  const { data: blockNumber } = useBlockNumber({
    watch: true,
    chainId: chain?.id,
  });

  const { data: feeData } = useFeeData({
    watch: true,
    chainId: chain?.id,
    formatUnits: "gwei",
  });

  return (
    <div className={styles.container}>
      <div className={styles.infoWrapper}>
      <span className={styles.value}>{chain?.name || "Not Connected"}</span>

        <div className={styles.infoItem}>
          {/* <span className={styles.separator}>|</span> */}
          
          <span className={styles.label}>Block:</span>
          <span className={styles.value}>{blockNumber?.toString() || "-"}</span>
          <span className={styles.separator}>|</span>
          <span className={styles.label}>Gas:</span>
          <span className={styles.value}>
            {feeData?.formatted?.gasPrice
              ? `${Number(feeData.formatted.gasPrice).toFixed(2)} Gwei`
              : "-"}
          </span>
        </div>
      </div>
    </div>
  );
};

export default BlockchainInfo;

/* eslint-disable react/no-unescaped-entities */
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import styles from "./ConnectWalletModal.module.scss";
import { motion } from "framer-motion";
import { useSnapshot } from "valtio";
import {
  bottomUpFadeIn,
  buttonEffect,
  fadeIn,
  scaleUp,
} from "@/animations/animations";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import coinbaseIcon from "@/assets/icons/connectWallet/coinbaseIcon.png";
import walletConnectIcon from "@/assets/icons/connectWallet/walletConnectIcon.svg";
import metamaskIcon from "@/assets/icons/connectWallet/metamaskIcon.png";
import okxIcon from "@/assets/icons/connectWallet/okxWalletIcon.svg";
import trustWalletIcon from "@/assets/icons/connectWallet/trustWalletIcon.svg";
import rabbyWalletIcon from "@/assets/icons/connectWallet/rabbyWalletIcon.svg";
import binanceWalletIcon from "@/assets/icons/connectWallet/binanceWalletIcon.svg";
import bitgetWalletIcon from "@/assets/icons/connectWallet/bitgetWalletIcon.svg";

import { useAccount, useConnect } from "wagmi";
import { MouseEventHandler, useState, useEffect } from "react";
import { BaseError } from "viem";
import { showError, showSuccess } from "@/lib/notification";
import TermsAndConditions from "./TermsAndConditions/TermsAndConditions";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { config } from "@/wagmi/wagmiConfigs";
import { WagmiConfig } from "wagmi";
import {
  getDirectMetaMaskProvider,
  diagnoseWalletProviders,
  registerGlobalDiagnostics,
  forceMetaMaskOnly,
} from "@/lib/walletUtils";
import { useNetwork } from "wagmi";
import { useSearchParams } from "next/navigation";

interface WalletSelectProps {
  walletName: string;
  icon: string;
  isPopular?: boolean;
  onClick: MouseEventHandler<HTMLDivElement> | undefined;
}

const ConnectWalletModalContent = () => {
  const { connector, isConnected } = useAccount();
  const { connect, connectors, error, isLoading, pendingConnector } =
    useConnect({
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
      },
      onSuccess: () => {
        connectWalletModalSnapshot.setIsOpenConnectWalletModal(false);
        toast.success("Wallet Connected!");
      },
    });

  const getConnector = (id: string) =>
    connectors.filter((connector) => connector.id === id)[0];

  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);

  const [hasUserReadTandC, setHasUserReadTandC] = useState(false);
  const [showMoreWallets, setShowMoreWallets] = useState(false);

  const WalletSelect = ({
    walletName,
    icon,
    isPopular,
    onClick,
  }: WalletSelectProps) => (
    <div className={styles.walletSelect} onClick={onClick}>
      <div>
        <img src={icon} alt={walletName} />
        {walletName}
      </div>
      {isPopular ? <h1>popular</h1> : null}
    </div>
  );

  const { chain } = useNetwork();
  const searchParams = useSearchParams();
  const shortCode = searchParams.get("invitation");
  const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";

  const getDappUrl = () => {
    const baseUrl = isTestnet
      ? "https://testnet.asteroidx.io/main"
      : "https://asteroidx.io/main";

    return shortCode ? `${baseUrl}?invitation=${shortCode}` : baseUrl;
  };

  const handleCoinbaseWallet = async (isMobile: boolean) => {
    try {
      const connector = getConnector("coinbaseWallet");

      // 直接连接，Coinbase会自动判断是否需要显示QR码
      await connect({ connector });
    } catch (error) {
      console.error("Coinbase connection error:", error);
      showError(
        (error as BaseError).shortMessage ??
          "Failed to connect with Coinbase Wallet",
      );
    }
  };

  const handleMetaMask = async (isMobile: boolean) => {
    const isMetaMaskInstalled =
      typeof window.ethereum !== "undefined" && window.ethereum.isMetaMask;

    if (isMobile) {
      if (isMetaMaskInstalled) {
        console.log("Mobile MetaMask detected");
        const connector = getConnector("metaMask");
        connect({ connector });
      } else {
        console.log("Redirecting to MetaMask mobile app");
        const dappURL = getDappUrl();
        window.location.href = `https://metamask.app.link/dapp/${dappURL}`;
      }
    } else {
      console.log("Connecting MetaMask on desktop");
      const connector = getConnector("metaMask");
      connect({ connector });
    }
  };

  const handleOK = async (isMobile: boolean) => {
    try {
      const isOKXInstalled = typeof window !== "undefined" && window.okxwallet;
      const isInOKXBrowser = /OKExWallet/i.test(navigator.userAgent);

      // 使用更严格的移动设备检测
      const mobilePattern =
        /iPhone|iPad|iPod|Android|webOS|BlackBerry|IEMobile|Opera Mini/i;
      const isMobileBrowser = mobilePattern.test(navigator.userAgent);
      const isPCBrowser = !isMobileBrowser;

      // 如果是PC浏览器访问移动端，直接提示并返回
      if (isMobile && isPCBrowser) {
        console.log("Detected PC browser accessing mobile version");
        const downloadMessage = `Please install OKX wallet extention to continue\nLink: https://www.okx.com/web3/extension`;
        alert(downloadMessage);
        return;
      }

      if (isMobile) {
        if (isInOKXBrowser || isOKXInstalled) {
          console.log("OKX Wallet detected, connecting...");
          const connector = connectors.find((c) => c.name === "OKX Wallet");
          if (connector) {
            await connect({ connector });
          }
        } else if (isMobileBrowser) {
          console.log("Redirecting to OKX mobile app");
          const dappURL = getDappUrl();

          if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
            const okxUniversalLink = `https://www.okx.com/web3/connect/dapp/${encodeURIComponent(
              dappURL,
            )}`;
            window.location.href = okxUniversalLink;

            setTimeout(() => {
              if (document.hasFocus()) {
                const okxProtocolLink = `okx://wallet/dapp/details?dappUrl=${encodeURIComponent(
                  dappURL,
                )}`;
                window.location.href = okxProtocolLink;
              }
            }, 3000);
          } else if (/Android/i.test(navigator.userAgent)) {
            const okxProtocolLink = `okx://wallet/dapp/details?dappUrl=${encodeURIComponent(
              dappURL,
            )}`;
            window.location.href = okxProtocolLink;

            setTimeout(() => {
              if (document.hasFocus()) {
                const okxUniversalLink = `https://www.okx.com/web3/connect/dapp/${encodeURIComponent(
                  dappURL,
                )}`;
                window.location.href = okxUniversalLink;
              }
            }, 2000);
          }
        } else {
          console.log(
            "Desktop browser detected, showing wallet installation prompt",
          );
          const downloadMessage = `Please install OKX wallet extention to continue\nLink: https://www.okx.com/web3/extension`;
          alert(downloadMessage);
        }
      } else {
        // 桌面端检查钱包插件是否安装
        if (isOKXInstalled) {
          console.log("Connecting OKX on desktop");
          const connector = connectors.find((c) => c.name === "OKX Wallet");
          if (connector) {
            await connect({ connector });
          }
        } else {
          console.log("OKX Wallet not installed on desktop");
          const downloadMessage = `Please install OKX wallet extention to continue\nLink: https://www.okx.com/web3/extension`;
          alert(downloadMessage);
        }
      }
    } catch (error) {
      console.error("OKX connection error:", error);
      showError("Failed to connect to OKX Wallet");
    }
  };

  // 处理新增钱包的连接逻辑
  const handleWalletConnect = async (isMobile: boolean) => {
    try {
      const connector = connectors.find((c) => c.id === "walletConnect");
      if (connector) {
        // 直接connect，WalletConnect会自动显示QR码
        await connect({ connector });

        // 移动端处理
        if (isMobile) {
          // 在某些移动浏览器，可能需要打开WalletConnect页面
          setTimeout(() => {
            // 如果用户还在当前页面，可能是没有钱包应用，尝试打开通用链接
            if (document.hasFocus()) {
              window.location.href = "https://walletconnect.com/wallets";
            }
          }, 3000);
        }
      } else {
        showError("WalletConnect connector not found");
      }
    } catch (error) {
      console.error("WalletConnect error:", error);
      showError("Failed to connect with WalletConnect");
    }
  };

  const handleTrustWallet = async (isMobile: boolean) => {
    try {
      const isTrustWalletInstalled =
        typeof window !== "undefined" && window.trustwallet;

      if (isMobile) {
        if (isTrustWalletInstalled) {
          console.log("Trust Wallet detected on mobile, connecting...");
          const connector = connectors.find((c) => c.name === "Trust Wallet");
          if (connector) {
            await connect({ connector });
          }
        } else {
          console.log("Redirecting to Trust Wallet mobile app");
          const dappURL = getDappUrl();
          window.location.href = `https://link.trustwallet.com/open_url?url=${encodeURIComponent(
            dappURL,
          )}`;
        }
      } else {
        console.log("Connecting Trust Wallet on desktop");
        const connector = connectors.find((c) => c.name === "Trust Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Trust Wallet connector not found");
        }
      }
    } catch (error) {
      console.error("Trust Wallet error:", error);
      showError("Failed to connect to Trust Wallet");
    }
  };

  const handleRabbyWallet = async (isMobile: boolean) => {
    if (isMobile) {
      // Rabby目前主要是桌面扩展，移动端提示安装
      showError(
        "Rabby Wallet is primarily a desktop extension. Please use a desktop browser.",
      );
      return;
    }

    try {
      const connector = connectors.find((c) => c.name === "Rabby Wallet");
      if (connector) {
        await connect({ connector });
      } else {
        showError("Rabby Wallet connector not found");
      }
    } catch (error) {
      console.error("Rabby Wallet error:", error);
      showError("Failed to connect to Rabby Wallet");
    }
  };

  const handleBinanceWallet = async (isMobile: boolean) => {
    try {
      const isBinanceWalletInstalled =
        typeof window !== "undefined" && window.BinanceChain;

      if (isMobile) {
        if (isBinanceWalletInstalled) {
          console.log("Binance Wallet detected on mobile, connecting...");
          const connector = connectors.find((c) => c.name === "Binance Wallet");
          if (connector) {
            await connect({ connector });
          }
        } else {
          console.log("Redirecting to Binance Wallet mobile app");
          // 如果移动端没有Binance钱包，跳转到官方页面
          window.location.href = "https://www.bnbchain.org/en/binance-wallet";
        }
      } else {
        console.log("Connecting Binance Wallet on desktop");
        const connector = connectors.find((c) => c.name === "Binance Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Binance Wallet connector not found");
        }
      }
    } catch (error) {
      console.error("Binance Wallet error:", error);
      showError("Failed to connect to Binance Wallet");
    }
  };

  const handleBitgetWallet = async (isMobile: boolean) => {
    try {
      const isBitgetWalletInstalled =
        typeof window !== "undefined" && window.bitkeep?.ethereum;

      if (isMobile) {
        if (isBitgetWalletInstalled) {
          console.log("Bitget Wallet detected on mobile, connecting...");
          const connector = connectors.find((c) => c.name === "Bitget Wallet");
          if (connector) {
            await connect({ connector });
          }
        } else {
          console.log("Redirecting to Bitget Wallet mobile app");
          const dappURL = getDappUrl();
          window.location.href = `https://bkcode.vip?action=dapp&url=${encodeURIComponent(
            dappURL,
          )}`;
        }
      } else {
        console.log("Connecting Bitget Wallet on desktop");
        const connector = connectors.find((c) => c.name === "Bitget Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Bitget Wallet connector not found");
        }
      }
    } catch (error) {
      console.error("Bitget Wallet error:", error);
      showError("Failed to connect to Bitget Wallet");
    }
  };

  const handleConnectClick = async (connectorId: string) => {
    const userAgent = navigator.userAgent;
    const isMobile =
      /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        userAgent,
      );

    switch (connectorId) {
      case "metaMask":
        await handleMetaMask(isMobile);
        break;
      case "coinbaseWallet":
        await handleCoinbaseWallet(isMobile);
        break;
      case "OKX":
        await handleOK(isMobile);
        break;
      case "walletConnect":
        await handleWalletConnect(isMobile);
        break;
      case "trustWallet":
        await handleTrustWallet(isMobile);
        break;
      case "rabbyWallet":
        await handleRabbyWallet(isMobile);
        break;
      case "binanceWallet":
        await handleBinanceWallet(isMobile);
        break;
      case "bitgetWallet":
        await handleBitgetWallet(isMobile);
        break;
      default:
        console.error("Unsupported connector");
    }
  };

  return (
    <motion.div
      className={styles.container}
      initial={{
        opacity: 0,
        transform: "translate(-50%, -50%)",
        top: "50%",
        left: "50%",
      }}
      animate={{ opacity: 1, transform: "translate(-50%, -50%)" }}
      exit={{ opacity: 0, transform: "translate(25%, 25%)" }}
      transition={{ duration: 0.3 }}
    >
      <div className={styles.modalFrame}>
        <div className={styles.connectWallet}>
          <div className={styles.titleWrapper}>
            <h1>connect your wallet</h1>
            <div
              className={styles.closeButton}
              onClick={() =>
                connectWalletModalSnapshot.setIsOpenConnectWalletModal(false)
              }
            >
              <img src={crossIcon.src} alt="cross icon" />
            </div>
          </div>
          {hasUserReadTandC ? (
            <>
              <WalletSelect
                walletName="Okx wallet"
                icon={okxIcon.src}
                isPopular
                onClick={() => handleConnectClick("OKX")}
              />
              <WalletSelect
                walletName="Metamask"
                icon={metamaskIcon.src}
                onClick={() => handleConnectClick("metaMask")}
              />
              <WalletSelect
                walletName="Coinbase"
                icon={coinbaseIcon.src}
                onClick={() => handleConnectClick("coinbaseWallet")}
              />
              <WalletSelect
                walletName="WalletConnect"
                icon={walletConnectIcon.src}
                onClick={() => handleConnectClick("walletConnect")}
              />

              {showMoreWallets ? (
                <>
                  <WalletSelect
                    walletName="Trust Wallet"
                    icon={trustWalletIcon.src}
                    onClick={() => handleConnectClick("trustWallet")}
                  />
                  <WalletSelect
                    walletName="Rabby Wallet"
                    icon={rabbyWalletIcon.src}
                    onClick={() => handleConnectClick("rabbyWallet")}
                  />
                  <WalletSelect
                    walletName="Binance Wallet"
                    icon={binanceWalletIcon.src}
                    onClick={() => handleConnectClick("binanceWallet")}
                  />
                  <WalletSelect
                    walletName="Bitget Wallet"
                    icon={bitgetWalletIcon.src}
                    onClick={() => handleConnectClick("bitgetWallet")}
                  />
                  <div
                    className={styles.showMoreWallets}
                    onClick={() => setShowMoreWallets(false)}
                  >
                    Show less wallets
                  </div>
                </>
              ) : (
                <div
                  className={styles.showMoreWallets}
                  onClick={() => setShowMoreWallets(true)}
                >
                  Show more wallets
                </div>
              )}
            </>
          ) : (
            <TermsAndConditions setHasUserReadTandC={setHasUserReadTandC} />
          )}
        </div>
      </div>
      {/* <ToastContainer
        position="bottom-left"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        theme="dark"
      /> */}
    </motion.div>
  );
};

const ConnectWalletModal = () => {
  return (
    <WagmiConfig config={config}>
      <ConnectWalletModalContent />
    </WagmiConfig>
  );
};

export default ConnectWalletModal;

import * as Tooltip from "@radix-ui/react-tooltip";
import styles from "./Buyer.module.scss";
import { buyerInformation } from "@/sampleAssets/constants/buyerInformation";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import locationLightIcon from "@/assets/icons/minesNavigation/locationLightIcon.png";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import {
  useAccount,
  useContractEvent,
  useContractRead,
  useContractReads,
  useNetwork,
  useSwitchNetwork,
} from "wagmi";
import { BaseError, Log } from "viem";
import { launchPadAbi } from "@/constants/abis/launchPadAbi";
import { creator<PERSON><PERSON><PERSON> } from "@/constants/abis/creator<PERSON><PERSON>bi";
import { bigint } from "zod";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { useMediaQuery } from "usehooks-ts";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { showError, showSuccess } from "@/lib/notification";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";
import { toast } from "react-toastify";

type ContractList = {
  address: `0x${string}`;
  abi: typeof asteroidAddressABI;
  functionName: string;
  args: BigInt[];
};

interface ComponentProps {
  setSelectedComponent: (component: string) => void;
}

const Buyer: React.FC<ComponentProps> = ({ setSelectedComponent }) => {
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [contractList, setContractList] = useState<ContractList[]>([]);
  const [asteroidId, setAsteroidId] = useState<number>(0);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const { handleClaim, isClaiming, participated, isWaitingForClaim } =
    useRedpacket();

  const { chain } = useNetwork();
  const { switchNetwork, chains } = useSwitchNetwork({
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      toast.success("Switched to supported network");
    },
  });

  const { isConnected } = useAccount();
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const supportedChain = chains.filter((x) =>
    information.supportedNetwork.includes(x.id),
  )[0]?.name;

  // const isShowingToolTips = useMediaQuery("(min-width: 1920px)");

  // const trimWalletAddress = (address: string) =>
  //   address.slice(0, 12) + "..." + address.slice(-14);

  // const { data: nftSold } = useContractRead({
  //   address: asteroidAddress,
  //   abi: asteroidAddressABI,
  //   functionName: "metaAsteroid",
  //   args: [BigInt(asteroidId)],
  //   watch: true,
  //   enabled:
  //     !chain?.unsupported &&
  //     isConnected &&
  //     launchPadAddress !== "0x" &&
  //     asteroidAddress !== "0x" &&
  //     asteroidId !== 0,
  // });

  // const { data: buyers } = useContractReads({
  //   contracts: contractList,
  //   watch: true,
  //   enabled:
  //     !chain?.unsupported &&
  //     isConnected &&
  //     asteroidAddress !== "0x" &&
  //     contractList.length > 0,
  // });

  // console.log(buyers);
  // const getSelectedMineAddress = (chainId: number) => {
  //   return networkConfigs[chainId].asteroidAddress;
  // };

  // const getAsteroidId = (chainId: number) => {
  //   switch (information.name) {
  //     case minesDetails[0].name:
  //       return networkConfigs[chainId].assetIds.mt;
  //     case minesDetails[1].name:
  //       return networkConfigs[chainId].assetIds.matsa;
  //     case minesDetails[2].name:
  //       return networkConfigs[chainId].assetIds.zephyr;
  //     default:
  //       break;
  //   }
  // };

  // useEffect(() => {
  //   if (chain?.unsupported) {
  //     setLaunchPadAddress("0x");
  //     setAsteroidAddress("0x");
  //     setAsteroidId(0);
  //   } else {
  //     if (chain) {
  //       setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
  //       setAsteroidAddress(getSelectedMineAddress(chain.id));
  //       setAsteroidId(getAsteroidId(chain.id) ?? 0);
  //     }
  //   }
  // }, [chain]);

  // useEffect(() => {
  //   if (nftSold) {
  //     const tempContractList: ContractList[] = [];
  //     [...Array(Number(nftSold.soldAmount))].map((_, index) => {
  //       tempContractList.push({
  //         address: asteroidAddress,
  //         abi: asteroidAddressABI,
  //         functionName: "creators",
  //         args: [BigInt(index)],
  //       });
  //     });
  //     setContractList(tempContractList);
  //   }
  // }, [nftSold]);

  return (
    <div className={styles.container}>
      {/* <h1 className={styles.title}>
        {isConnected ? "Buy This Mine" : "Log in or Register Now"}
      </h1> */}
      <div className={styles.loginButtonWrapper}>
        {isConnected ? (
          <motion.button
            className={styles.loginButton}
            whileTap={buttonEffect.tap}
            disabled={isClaiming || isWaitingForClaim || participated}
            // disabled={!write}
            onClick={() => {
              // showConfetti();
              // purchaseNft();
              if (chain && chain.unsupported) {
                toast.error("Please Switch Network");
                return;
              }
              if (chain && selectedMine.supportedNetwork.includes(chain?.id)) {
                // setSelectedComponent("component2");
                handleClaim();
                // Promise.resolve().then(() => {
                //   purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                //     true,
                //   );
                // });
              } else {
                switchNetwork?.(
                  chain?.testnet
                    ? selectedMine.supportedNetwork[0]
                    : selectedMine.supportedNetwork[1],
                );
              }
            }}
          >
            {chain && selectedMine.supportedNetwork.includes(chain?.id)
              ? "Claim Now"
              : `Switch to ${supportedChain}`}
          </motion.button>
        ) : (
          <motion.div
            className={styles.loginButton}
            whileTap={buttonEffect.tap}
            onClick={() => {
              connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
            }}
          >
            Log in <span>&nbsp;or&nbsp;</span> Register Now
          </motion.div>
        )}
      </div>
      {/* <div className={styles.informationContainer}>
        <div className={styles.informationWrapper}>
          {!chain?.unsupported && isConnected
            ? buyers?.slice(0, 5).map((buyer, index) => (
                <div key={index}>
                  {buyer.status === "success" && isShowingToolTips ? (
                    <Tooltip.Provider delayDuration={300}>
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild>
                          <div>
                            <span className={styles.avatar}>
                              <h1 className={styles.letter}>B</h1>
                            </span>
                          </div>
                        </Tooltip.Trigger>
                        <Tooltip.Portal>
                          <Tooltip.Content
                            avoidCollisions
                            side="top"
                            sideOffset={10}
                            className={styles.tooltipContent}
                          >
                            <div className={styles.tooltipDetails}>
                              <div className={styles.buyerCardContainer}>
                                <div className={styles.headerWrapper}>
                                  <div className={styles.header}>
                                    <h1 className={styles.headerTitle}>
                                      user profile
                                    </h1>
                                    <h2 className={styles.headerSubTitle}>
                                      purchase summary
                                    </h2>
                                  </div>
                                  <img
                                    src={dotDotDot.src}
                                    alt="dot dot dot icon"
                                  />
                                </div>
                                <div className={styles.userDetails}>
                                  <span className={styles.avatar}>
                                    <h1 className={styles.letter}>B</h1>
                                  </span>
                                  <div>
                                    <h1 className={styles.name}>
                                      {information.name}
                                    </h1>
                                    <div className={styles.purchasedWrapper}>
                                      <img
                                        src={locationLightIcon.src}
                                        alt="location icon lighter"
                                      />
                                      <h2 className={styles.purchased}>
                                        Bought The Area
                                      </h2>
                                    </div>
                                  </div>
                                </div>
                                <div className={styles.connect}>
                                  <h1 className={styles.connectText}>
                                    connect
                                  </h1>
                                  <h2 className={styles.contact}>
                                    {trimWalletAddress(buyer?.result as string)}
                                  </h2>
                                </div>
                              </div>
                            </div>
                            <Tooltip.Arrow
                              width={10}
                              height={10}
                              className={styles.tooltipArrow}
                            />
                          </Tooltip.Content>
                        </Tooltip.Portal>
                      </Tooltip.Root>
                    </Tooltip.Provider>
                  ) : (
                    <div>
                      <span className={styles.avatar}>
                        <h1 className={styles.letter}>B</h1>
                      </span>
                    </div>
                  )}
                </div>
              ))
            : null}
        </div>
      </div> */}
    </div>
  );
};

export default Buyer;

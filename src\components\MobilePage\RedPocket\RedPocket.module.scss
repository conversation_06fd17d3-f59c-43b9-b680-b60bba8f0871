@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.rippleContainer {
  // position: relative;
}
.container {
  // position: absolute;
  cursor: pointer;
  @include row-center;
  gap: 1rem;

  div > h5 > span {
    font-size: 1.5rem;
    font-weight: 600;
    color: $color-warning;
  }
}

.overlay {
  // background: black;
  // margin-bottom: $margin-md;
  opacity: 1;
  // position: absolute;
  width: 100%;
  margin: $margin-md 0;
  // height: 100%;
  z-index: 1;
  @include row-center;
}

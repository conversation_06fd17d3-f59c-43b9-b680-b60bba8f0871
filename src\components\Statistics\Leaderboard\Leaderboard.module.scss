@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.leaderboardContainer {
  max-width: 100%;
  padding: 2rem 0;
}

.homeButton {
  @include row-center;
  text-align: center;
  font-size: 1.2rem;
  color: blue;
}

.title {
  text-align: center;
  font-size: 3rem;
  color: $color-primary;
}

.lastUpdate {
  font-size: 1.5rem;
  text-align: center;
  color: gray;
}

.totalUsers {
  font-size: 1.5rem;
  text-align: center;
  color: white;

  span {
    color: $color-warning;
    font-size: 2rem;
  }
}

.leaderboard {
  width: 100%;
  border: 1px solid $color-primary-contrast;
  margin-top: 2rem;
  border-radius: 1rem;
  padding: 1rem 1rem 2rem;
  background: $color-black-transparent;

  th,
  td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid $color-primary-contrast;
  }

  th {
    color: $color-warning;
    font-size: 1.2rem;
    font-weight: bold;
  }

  .avatar {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ccc;
    margin-right: 10px;
  }
}

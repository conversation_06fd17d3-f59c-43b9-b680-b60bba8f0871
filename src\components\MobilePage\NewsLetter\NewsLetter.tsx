import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./NewsLetter.module.scss";
import { AnimatePresence, MotionStyle, motion } from "framer-motion";
import { slideIn } from "@/animations/animations";
import variables from "@/styles/variables.module.scss";
import NewsContents from "./NewsContents";
import TransactionDetails from "../Transaction/TransactionDetails";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";

const filterList: string[] = ["News", "Commodities"];

interface NewsletterProps {
  delay?: number;
  isButtonClicked: boolean;
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
}

type FilterList = (typeof filterList)[keyof typeof filterList];

const Newsletter = ({ isButtonClicked, delay }: NewsletterProps) => {
  const [hasInitialAnimationDone, setHasInitialAnimationDone] = useState(false);
  const [isShowMoreButtonClicked, setIsShowMoreButtonClicked] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<FilterList>(
    filterList[0],
  );
  const [selectedTitle, setSelectedTitle] = useState("NewsLetter");

  const newsContentsConfigs = {
    isShowMoreButtonClicked,
  };

  useEffect(() => {
    setTimeout(
      () => {
        setHasInitialAnimationDone(true);
      },
      delay && (delay + 2) * 1000,
    );
  }, []);

  const getSelectedFilter = () => {
    switch (selectedFilter) {
      case "News":
        return <NewsContents {...newsContentsConfigs} />;
      case "Commodities":
        return <TransactionDetails isShowMoreButtonClicked={false} />;
      default:
        return <NewsContents {...newsContentsConfigs} />;
    }
  };

  const handleFilterChange = (filter: string) => {
    setSelectedFilter(filter);
    setSelectedTitle(filter.toUpperCase());
  };

  return (
    <>
      <div className={styles.wrapper}>
        <div className={styles.headerWrapper}>
          <div className={styles.header}>
            <h1 className={styles.title}>{selectedTitle}</h1>
            <h2 className={styles.subTitle}>list of updated {selectedTitle}</h2>
          </div>
          {/* <img src={dotDotDot.src} alt="dot dot dot icon" /> */}
        </div>
        <div className={styles.filter}>
          {filterList.map((item, index) => {
            const isSelected = selectedFilter === item;
            return (
              <motion.h1
                key={index}
                className={styles.list}
                style={{
                  color: isSelected ? variables.colorPrimary : undefined,
                }}
                onClick={() => handleFilterChange(item)}
              >
                {item}
              </motion.h1>
            );
          })}
        </div>
        {getSelectedFilter()}
      </div>
    </>
  );
};

export default Newsletter;

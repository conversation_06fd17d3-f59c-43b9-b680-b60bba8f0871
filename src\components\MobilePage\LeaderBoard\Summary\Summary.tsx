import React, { useEffect, useState } from "react";
import styles from "./Summary.module.scss";
import Link from "next/link";
import { pointSystemData } from "@/constants/pointSystem/final";
import { useAccount } from "wagmi";
import { PointsData } from "@/stores/pointsData";
import moment from "moment";
import table from "@/assets/images/table.jpg";
import Image from "next/image";

interface SummaryProps {
  pointsData: PointsData;
}

const Summary: React.FC<SummaryProps> = ({ pointsData }) => {
  const { address, isConnected } = useAccount();
  const [userPoints, setUserPoints] = useState(0);
  const [totalCounts, setTotalCounts] = useState(0);
  const [rank, setRank] = useState("");
  const [percent, setPercent] = useState(0);

  useEffect(() => {
    setUserPoints(pointsData.userPoints);
    setTotalCounts(pointsData.total);
    setRank(pointsData.rank);
    setPercent(pointsData.percent);
  }, [address, pointsData]);

  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <Image src={table} alt="table" className={styles.table} />
        <div>Summary</div>
      </div>
      <div className={styles.wrapper}>
        <div className={styles.leftContainer}>
          <div className={styles.pointSection}>
            <div className={styles.pointTitle}>
              <h2>Current Total Participants</h2>
            </div>
            <h1 className={styles.priceTag}>
              {totalCounts}
              <span> User</span>
            </h1>
          </div>
        </div>

        <div className={styles.rightContainer}>
          <div className={styles.pointSection}>
            <div className={styles.pointTitle}>
              <h2>Your Asteroid Points</h2>
            </div>
            <h1 className={styles.priceTag}>
              {userPoints}
              <span> points</span>
            </h1>
          </div>
        </div>
      </div>

      <div className={styles.wrapper}>
        <div className={styles.leftContainer}>
          <div className={styles.pointSection}>
            <div className={styles.pointTitle}>
              <h2>Your Current Rank</h2>
            </div>
            <h1 className={styles.priceTag}>
              {rank}
              <span></span>
            </h1>
          </div>
        </div>
        <div className={styles.rightContainer}>
          <div className={styles.pointSection}>
            <div className={styles.pointTitle}>
              <h2>Your $Asteroid</h2>
            </div>
            <h1 className={styles.priceTag}>
              {percent > 0 ? `${percent}%` : "< 0.01%"}
              {/* <span> $ASTEROID</span> */}
            </h1>
          </div>
        </div>
      </div>
      <div className={styles.timeSection}>
        <div className={styles.pointTitle}>
          <h2>Last Update:</h2>
          <h2>{moment().subtract(4, "hours").calendar()} (UTC + 8)</h2>
        </div>
      </div>
    </div>
  );
};

export default Summary;

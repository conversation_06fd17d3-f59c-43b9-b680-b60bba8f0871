import { mineCardStore } from "@/stores/mineCard";
import styles from "./Rewards.module.scss";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import locationIconGrey from "@/assets/icons/plusModal/locationIconGrey.png";
import { plusModalStore } from "@/stores/plusModal";
import {
  memo,
  useRef,
  useEffect,
  useLayoutEffect,
  Suspense,
  useState,
} from "react";
import {
  useAccount,
  useNetwork,
  useContractWrite,
  usePrepareContractWrite,
  useWaitForTransaction,
  useContractRead,
} from "wagmi";
import { useRouter } from "next/navigation";
import { navbarButtonStore } from "@/stores/navbarButton";
import { PointsData, pointsDataStore, TUserTask } from "@/stores/pointsData";
import { navbarButtonDetails } from "@/components/MainPage/TopNavigation/NavbarContent/NavbarContent";
import { showError, showSuccess } from "@/lib/notification";
import { rewardAsteroidXAbi } from "@/constants/abis/RewardAsteroidXABI";
import { BaseError } from "viem";
import { networkConfigs } from "@/constants/networkConfigs";
import useSpecialEvent from "@/components/MainPage/MinesNavigation/SpecialEventMineCard/MineBody/hooks/useSpecialEvent";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";

const formatNumber = (value: bigint | undefined): string => {
  if (!value) return "0";
  return value.toString();
};

const formatTimestamp = (timestamp: bigint | undefined): string => {
  if (!timestamp) return "N/A";
  const date = new Date(Number(timestamp) * 1000);
  return date
    .toLocaleString("en-AU", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(",", "");
};

const weiToEth = (wei: bigint | undefined): string => {
  if (!wei) return "0";
  const eth = Number(wei) / 1e18;
  return eth.toFixed(4); // Display 4 decimal places
};

const Rewards = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);

  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [rewardAddress, setRewardAddress] = useState<`0x${string}`>("0x");

  const {
    userReward,
    claimStartTime,
  } = useSpecialEvent();

  // Get user reward info
  const { data: userRewardInfo } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "userRewardInfo",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && !!address,
    watch: true,
  });

  // Calculate user reward
  const { data: claimableReward } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "calculateUserReward",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && !!address,
    watch: true,
  });

  const { config: claimConfig } = usePrepareContractWrite({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "claim",
    enabled:
      isConnected &&
      !chain?.unsupported &&
      !!userRewardInfo &&
      !userRewardInfo.hasClaimed &&
      (claimableReward ?? 0n) > 0n,
    onError: (error) => {
      console.error("Prepare claim error:", error);
    },
  });

  const { write: claimWrite, data: claimData } = useContractWrite({
    ...claimConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const { isLoading: isWaitingForClaim } = useWaitForTransaction({
    hash: claimData?.hash,
    onSuccess: () => {
      showSuccess("Successfully claimed HSK rewards!");
      setIsClaiming(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const handleClaim = async () => {
    if (!isConnected) {
      showError("Please connect your wallet first");
      return;
    }

    if (chain?.unsupported) {
      showError("Please switch to a supported network");
      return;
    }

    const currentTimestamp = BigInt(Math.floor(Date.now() / 1000));
    if (currentTimestamp < claimStartTime) {
      showError("Claim has not started yet");
      return;
    }

    if (!claimWrite) {
      if (userRewardInfo?.hasClaimed) {
        showError("You have already claimed your rewards");
        return;
      }

      if ((claimableReward ?? 0n) <= 0n) {
        showError("No rewards available to claim");
        return;
      }

      showError("Unable to claim rewards at this time");
      return;
    }

    setIsClaiming(true);
    try {
      await claimWrite();
    } catch (error) {
      setIsClaiming(false);
      showError("Failed to claim rewards");
    }
  };
  const switchToMainPage = () => {
    navbarButtonSnapshot.setSelectedButton(navbarButtonDetails[0].title);
  };
  useLayoutEffect(() => {
    if (!isConnected) {
      showError("Please Connect Wallet");
      switchToMainPage();
      return;
    }
    if (chain?.unsupported) {
      // showError("Please Switch Network");
      switchToMainPage();
      return;
    }
    if (chain) {
      setRewardAddress(networkConfigs[chain.id].rewardAddress);
    }
    const fetchData = async () => {
      if (isConnected && address) {
        setIsLoading(true);
        await Promise.all([
          pointsDataStore.fetchPointsData(address),
          pointsDataStore.fetchUserTasks(address),
        ]);
      }
      setIsLoading(false);
    };

    fetchData();
  }, [isConnected, chain, address]);

  if (isLoading) return <p className={styles.loadingText}>Loading...</p>;

  return (
    <>
      {isConnected && !chain?.unsupported ? (
        <div className={styles.container}>
          {/* <div className={styles.title}>
						<Image src={table} alt="table" className={styles.table} />
						<div>Claim Your Rewards</div>
					</div>
					<div className={styles.wrapper}>
						<div className={styles.leftContainer}>
							<div className={styles.pointSection}>
								<div className={styles.pointTitle}>
									<h2>Total Purchase Amount</h2>
								</div>
								<h1 className={styles.priceTag}>
									{userRewardInfo
										? formatNumber(userRewardInfo.purchaseAmount)
										: "0"}{" "}
									HSK
									<span> User</span>
								</h1>
							</div>
						</div>

						<div className={styles.rightContainer}>
							<div className={styles.pointSection}>
								<div className={styles.pointTitle}>
									<h2>Purchase Time</h2>
								</div>
								<h1 className={styles.priceTag}>
									{formatTimestamp(userRewardInfo?.timestamp)}

								</h1>
							</div>
						</div>
					</div>

					<div className={styles.wrapper}>
						<div className={styles.leftContainer}>
							<div className={styles.pointSection}>
								<div className={styles.pointTitle}>
									<h2>Claimable Reward</h2>
								</div>
								<h1 className={styles.priceTag}>
									{userRewardInfo?.hasClaimed
										? "0"
										: weiToEth(claimableReward)}{" "}
									HSK
								</h1>
							</div>
						</div>
						<div className={styles.rightContainer}>
							<div className={styles.pointSection}>
								<div className={styles.pointTitle}>
									<h2>Claimed Amount</h2>
								</div>
								<h1 className={styles.priceTag}>
									{userRewardInfo?.hasClaimed
										? weiToEth(userRewardInfo.claimedReward)
										: "0"}{" "}
									HSK
								</h1>
							</div>
						</div>
					</div> */}
          {/* {BigInt(Math.floor(Date.now() / 1000)) >= claimStartTime ? ( */}
          {/* <button
            className={styles.claimButton}
            onClick={handleClaim}
            disabled={
              isClaiming ||
              isWaitingForClaim ||
              userRewardInfo?.hasClaimed ||
              (claimableReward ?? 0n) <= 0n
            }
          >
            {isClaiming || isWaitingForClaim
              ? "Claiming..."
              : userRewardInfo?.hasClaimed
              ? "Already Claimed"
              : (claimableReward ?? 0n) <= 0n
              ? "Event Start in " + {formatTimestamp(userRewardInfo?.timestamp)}
              : "Claim Rewards"}
          </button> */}
          {/* ) : null} */}
          {BigInt(Math.floor(Date.now() / 1000)) >= claimStartTime ? (
            <motion.button
              className={styles.claimButton}
              whileTap={
                userRewardInfo?.hasClaimed || (userReward ?? 0n) <= 0n
                  ? undefined
                  : buttonEffect.tap
              }
              style={{
                // marginTop: "1rem",
                cursor:
                  userRewardInfo?.hasClaimed || (userReward ?? 0n) <= 0n
                    ? "not-allowed"
                    : "pointer",
              }}
              onClick={handleClaim}
              disabled={
                isClaiming ||
                isWaitingForClaim ||
                userRewardInfo?.hasClaimed ||
                (userReward ?? 0n) <= 0n
              }
            >
              {isClaiming || isWaitingForClaim
                ? "Claiming..."
                : userRewardInfo?.hasClaimed
                ? "Already Claimed"
                : (userReward ?? 0n) <= 0n
                ? "No Rewards"
                : "Claim Rewards"}
            </motion.button>
          ) : (
            <button
              className={styles.claimButton}
              style={{
                // marginTop: "1rem",
                cursor: "not-allowed",
                color: "gray",
              }}
              disabled
            >
              Claim start at {formatTimestamp(claimStartTime)}
            </button>
          )}
        </div>
      ) : null}
    </>
  );
};

export default Rewards;

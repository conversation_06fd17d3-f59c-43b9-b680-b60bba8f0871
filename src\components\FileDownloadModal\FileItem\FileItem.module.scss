@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.fileItem {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $padding-lg;
  background: rgba(0, 196, 208, 0.05);
  border: 1px solid rgba(0, 196, 208, 0.2);
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 196, 208, 0.1);
    border-color: rgba(0, 196, 208, 0.4);
    transform: translateY(-1px);
  }

  &.selected {
    background: rgba(0, 196, 208, 0.15);
    border-color: $color-primary;
    box-shadow: 0 0 0 1px $color-primary;
  }

  @media (max-width: 768px) {
    padding: $padding-md;
    gap: $spacing-sm;
  }
}

.checkbox {
  position: relative;
  display: flex;
  align-items: center;

  input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    width: 20px;
    height: 20px;
    cursor: pointer;

    &:checked + .checkmark {
      background: $color-primary;
      border-color: $color-primary;

      &::after {
        opacity: 1;
        transform: rotate(45deg) scale(1);
      }
    }
  }

  .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: $border-radius-xs;
    background: transparent;
    position: relative;
    transition: all 0.3s ease;

    &::after {
      content: "";
      position: absolute;
      left: 5px;
      top: 1px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      opacity: 0;
      transform: rotate(45deg) scale(0.8);
      transition: all 0.2s ease;
    }

    &:hover {
      border-color: rgba(255, 255, 255, 0.6);
    }
  }
}

.fileIcon {
  font-size: $font-size-2xl;
  min-width: 40px;
  text-align: center;

  @media (max-width: 768px) {
    font-size: $font-size-xl;
    min-width: 32px;
  }
}

.fileInfo {
  flex: 1;
  min-width: 0;

  .fileName {
    font-family: $font-family-saira;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: white;
    margin-bottom: $margin-sm;
    word-break: break-word;

    @media (max-width: 768px) {
      font-size: $font-size-md;
    }
  }

  .fileDetails {
    display: flex;
    gap: $spacing-md;
    margin-bottom: $margin-sm;

    .fileType {
      background: rgba(0, 196, 208, 0.2);
      color: $color-primary;
      padding: $padding-xs $padding-sm;
      border-radius: $border-radius-xs;
      font-family: $font-family-saira;
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .fileSize {
      font-family: $font-family-saira;
      font-size: $font-size-sm;
      color: rgba(255, 255, 255, 0.6);
      font-weight: $font-weight-medium;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: $spacing-xs;
    }
  }

  .fileDescription {
    font-family: $font-family-saira;
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;

    @media (max-width: 768px) {
      font-size: $font-size-xs;
    }
  }
}

.downloadSingle {
  background: transparent;
  border: 1px solid rgba(0, 196, 208, 0.3);
  border-radius: $border-radius-sm;
  padding: $padding-sm;
  cursor: pointer;
  font-size: $font-size-lg;
  transition: all 0.3s ease;
  min-width: 40px;
  height: 40px;
  @include col-center;

  &:hover {
    background: rgba(0, 196, 208, 0.1);
    border-color: $color-primary;
    transform: scale(1.1);
  }

  @media (max-width: 768px) {
    min-width: 36px;
    height: 36px;
    font-size: $font-size-md;
  }
}

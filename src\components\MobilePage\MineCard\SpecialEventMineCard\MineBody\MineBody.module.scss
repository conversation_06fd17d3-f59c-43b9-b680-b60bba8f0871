@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.information {
  width: 100%;
  border-bottom: $border-width-2xs solid $color-primary-transparent;
  padding-bottom: $spacing-sm;

  .logoWrapper {
    width: 100%;
    @include row-between;

    .title {
      font-size: $font-size-xs;
      color: white;
      font-weight: $font-weight-light;
      text-transform: uppercase;
    }

    .subtitle {
      font-size: $font-size-xs;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
  }
}

.container {
  position: absolute;
  width: 87%;
  height: 82%;
  overflow: scroll;
  @include col-center;
  justify-content: flex-start;
  align-items: flex-start;
  gap: $spacing-sm;
  // margin-top: $spacing-sm;
  // position: absolute;
  overflow-y: auto;
  padding-left: $padding-sm;
  padding-bottom: $padding-lg;

  .titleInformation {
    width: 100%;
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-sm;

    .imageFrame {
      width: 100%;
      @include row-center;
      justify-content: flex-start;
      // gap: $spacing-sm;
      height: 100%;
      // border: 3px solid yellow;
      position: relative;
      background: $color-primary-transparent;
      backdrop-filter: blur(20px);
      // padding: px;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%
      );
      // border: 1px solid yellow;

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        clip-path: polygon(
          0 0,
          calc(100% - 32px) 0,
          100% 32px,
          100% 100%,
          0 100%,
          0 0,
          1px 1px,
          1px calc(100% - 1px),
          calc(100% - 1px) calc(100% - 1px),
          calc(100% - 1px) calc(32px + 0.41px),
          calc(100% - 32px - 0.41px) 1px,
          1px 1px
        );
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  &::-webkit-scrollbar {
    display: none;
  }

  .auditArea {
    width: 100%;
    @include row-center;
    gap: $spacing-md;

    .auditText {
      font-size: $font-size-sm;
      // color: $color-primary;
      color: gray;
      font-style: italic;
      @include row-center;
      gap: 0.5rem;
      // border: 1px solid yellow;
    }
  }
}

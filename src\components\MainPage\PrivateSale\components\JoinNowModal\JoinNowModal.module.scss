@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #0a1a1f;
  border: 1px solid #00b8d4;
  border-radius: 4px;
  width: 100%;
  max-width: 1200px;
  color: white;
  overflow: hidden;
}

.modalHeader {
  background-color: #00b8d4;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 32px;
    font-weight: 500;
    text-transform: uppercase;
  }
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 32px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.modalBody {
  padding: 20px;
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.buttonWrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  // transform: translate(-50%);
  width: 100%;
  // border: 1px solid yellow;
  // margin-top: 200px;
  @include col-center;
  transition: all 1s;
  opacity: 0;
  gap: 40px;

  .animatedTypedText {
    color: $color-primary;
    font-size: $font-size-xl;
    font-weight: $font-weight-extrabold;
    text-align: center;
  }

  p {
    @include row-center;
    justify-content: flex-start;
    // gap: $spacing-md;
    color: $color-primary;
    font-size: $font-size-xl;
    font-weight: $font-weight-extrabold;
    margin-bottom: $margin-md;
    // width: 40%;
    // text-align: center;
    .endingTextTitle {
      font-weight: $font-weight-extralight;
      flex: 1;
    }
    .endingTextContent {
      flex: 4;
    }
  }

  h2 {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $color-primary;
    margin-top: $margin-md;
  }

  .exploreButton {
    // width: 400px;
    font-family: $font-family-poppins;
    font-size: $font-size-2xl;
    font-weight: $font-weight-light;
    text-align: center;
    cursor: pointer;
    padding: $padding-lg;
    background: $color-black-transparent-medium;
    color: $color-primary-contrast;
    // backdrop-filter: blur(30px);
    border: $border-width-sm solid $color-primary;
    // box-shadow: none;
    box-shadow: 0 0 200px $color-primary;
    // background: $color-black-transparent-dark;
    // transition: all 0.5s;

    &:hover {
      // box-shadow: 0 0 40px $color-primary;
      // background: $color-primary-contrast;
      color: $color-primary;
      text-shadow: 0 0 50px $color-primary;
    }
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.container {
  width: $displayWidth;
  height: $displayHeight;
  background-image: url("../../assets/images/westernAustralia.webp");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .mineNavigationOverlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: $color-black-transparent-medium;
    z-index: $z-index-1;
  }

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
    zoom: $zoom-level-1280;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
    zoom: $zoom-level-1920;
  }

  .confetti {
    width: 100%;
    height: 100%;
  }
}

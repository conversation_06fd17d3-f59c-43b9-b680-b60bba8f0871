import { Dispatch, SetStateAction } from "react";
import styles from "./ClaimSummary.module.scss";
import redpacketIcon from "@/components/MainPage/RedPocket/assets/redpacketIcon.png";
import LeaderboardTable from "./LeaderboardTable/LeaderboardTable";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";
import { formatEther } from "viem";

type ClaimSummaryProps = {
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
};

const ClaimSummary = ({ setIsButtonClicked }: ClaimSummaryProps) => {
  const { userClaimedHskAmount, userClaimedNftAmount } = useRedpacket();

  return (
    <div className={styles.container}>
      <div className={styles.summaryContainer}>
        <div className={styles.redpacketClaimed}>
          <img
            src={redpacketIcon.src}
            alt="red packet icon"
            width={72}
            height={72}
          />
          <div className={styles.claimDetails}>
            <h5>Congrat! You Have Claimed:</h5>
            <h5>
              <span>{formatEther(userClaimedHskAmount)}</span> HSK +{" "}
              <span>{userClaimedNftAmount.toString()}</span> NFT
            </h5>
          </div>
        </div>
        <div className={styles.divider} />
        {/* leaderboard */}
        <LeaderboardTable />
      </div>
      <button
        className={styles.closeButton}
        onClick={() => setIsButtonClicked(false)}
      >
        Close
      </button>
    </div>
  );
};

export default ClaimSummary;

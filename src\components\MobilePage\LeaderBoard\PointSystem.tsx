// import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./PointSystem.module.scss";
import LeaderBoardContent from "./LeaderBoardContent";

interface PointSystemProps {
  fullscreen?: boolean;
}

const PointSystem = ({ fullscreen = false }: PointSystemProps) => {
  return (
    <>
      <div className={fullscreen ? styles.fullscreenWrapper : styles.wrapper}>
        <LeaderBoardContent />
      </div>
    </>
  );
};

export default PointSystem;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.checkinSection {
  width: 100%;

  margin: 1rem 0;
  text-align: left;

  h1 {
    font-size: $font-size-xl;
    text-transform: uppercase;
    color: $color-primary;
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    letter-spacing: 1px;
  }

  h3 {
    font-size: $font-size-sm;
    font-weight: $font-weight-light;
    color: $color-primary-transparent-contrast;
    font-family: $font-family-poppins;
    margin-bottom: $margin-sm;
  }

  .checkinButton {
    position: relative;
    width: 100%;
    text-align: center;
    background: transparent;
    padding: $padding-md;
    color: $color-primary;
    font-size: $font-size-md;
    border: none;
    cursor: pointer;
    z-index: 1;
  }

  .checkinButton::before {
    content: "";
    position: absolute;
    top: -0.5px;
    left: -0.5px;
    right: -0.5px;
    bottom: -0.5px;
    background: linear-gradient(90deg, #000000 0%, $color-primary 100%);
    border-radius: 6px;
    z-index: -1;
  }
  .checkinButton::after {
    content: "";
    position: absolute;
    top: 0.5px;
    left: 0.5px;
    right: 0.5px;
    bottom: 0.5px;
    background: #000;
    border-radius: 6px;
    z-index: -1;
  }

  .checkinButton:hover::before {
    box-shadow: 0 0 15px $color-primary-transparent-contrast;
  }

  @keyframes background-pan {
    from {
      background-position: 0% center;
    }

    to {
      background-position: -200% center;
    }
  }
}

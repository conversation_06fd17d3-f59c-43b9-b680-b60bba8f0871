import CustomRadialBarChart from "@/components/UI/Charts/RadialBarChart/RadialBarChart";
import { MinePurchased } from "../hooks/usePurchaseHistory";
import styles from "./PurchasedPortion.module.scss";

const PurchasedPortion = ({
  tokenDetails,
  mineDetails,
}: {
  tokenDetails: MinePurchased["tokenDetails"];
  mineDetails: MinePurchased["mineDetails"];
}) => {
  const formatBlockchainData = () => {
    const userBalance = Number(tokenDetails.currentUserBalance);
    const othersBalance =
      Number(tokenDetails.soldAmount) / Number(tokenDetails.minAmount) -
      userBalance;
    const avaliableBalance =
      // Number(tokenDetails.totalSupply) - othersBalance - userBalance;
      mineDetails.manualTotalSupply - othersBalance - userBalance;

    return { userBalance, othersBalance, avaliableBalance };
  };

  const { userBalance, othersBalance, avaliableBalance } =
    formatBlockchainData();

  const data = [
    {
      name: `Yours`,
      value: userBalance,
      fill: "#8884d8",
    },
    {
      name: "Others",
      value: othersBalance,
      fill: "#82ca9d",
    },
    {
      name: "Available",
      value: avaliableBalance,
      fill: "#ffc658",
    },
  ];

  return (
    <div className={styles.container}>
      <CustomRadialBarChart data={data} height={300} />
    </div>
  );
};

export default PurchasedPortion;

import goldData from "@/sampleAssets/constants/commodities/XAUUSD.json";
import silverData from "@/sampleAssets/constants/commodities/XAGUSD.json";
import copperData from "@/sampleAssets/constants/commodities/Copper.json";
import platinumData from "@/sampleAssets/constants/commodities/XPTUSD.json";
import { MetalsData } from "@/stores/metalsData";
import { TMetalsResponse } from "@/actions/getMetalsDataFromOwnServer";

type TSupportedMetals = keyof TMetalsResponse["data"];

const supportedMetals: TSupportedMetals[] = [
  "gold",
  "silver",
  "palladium",
  "platinum",
  "copper",
  "aluminum",
  "rhodium",
  "lead",
  "nickel",
  "zinc",
] as const;

const getMetalColor = (metal: string) => {
  switch (metal) {
    case "gold":
      return "#FFD700	";
    case "silver":
      return "#C0C0C0";
    case "palladium":
      return "#b1b1b1";
    case "platinum":
      return "#E5E4E2";
    default:
      return "white";
  }
};
const convertGoogleLinechartDataFormat = (
  timeSeries: string[],
  price: number[],
) => {
  const convertedData: (string | number)[][] = [];
  if (timeSeries.length !== price.length) {
    return convertedData;
  }

  timeSeries.map((time, index) => {
    convertedData.push([time, price[index]]);
  });

  return [["Time", "Price"], ...convertedData];
};

export const getMetalsDetails = (metals: MetalsData) => {
  let metalsDetails: typeof commoditiesDetails = [];
  if (metals) {
    supportedMetals.map((metal) => {
      metalsDetails.push({
        name: metal,
        color: getMetalColor(metal),
        price: metals[metal].currentPrice,
        priceChangePercentage24h: metals[metal].changePercent,
        historicalData: convertGoogleLinechartDataFormat(
          metals[metal].dateItems,
          metals[metal].priceItems,
        ),
      });
    });
  }
  return metalsDetails;
};

export const commoditiesDetails = [
  {
    name: "gold",
    color: "#FFD700	",
    price: 2052.3,
    priceChangePercentage24h: 0.35,
    historicalData: convertGoogleLinechartDataFormat(
      goldData.DATE,
      goldData.CLOSE,
    ),
  },
  {
    name: "silver",
    color: "#C0C0C0",
    price: 24.41,
    priceChangePercentage24h: 0.13,
    historicalData: convertGoogleLinechartDataFormat(
      silverData.DATE,
      silverData.CLOSE,
    ),
  },
  {
    name: "copper",
    color: "#b87333",
    price: 3.898,
    priceChangePercentage24h: 0.12,
    historicalData: convertGoogleLinechartDataFormat(
      copperData.DATE,
      copperData.CLOSE,
    ),
  },
  {
    name: "platinum",
    color: "#E5E4E2",
    price: 964.3,
    priceChangePercentage24h: -0.35,
    historicalData: convertGoogleLinechartDataFormat(
      platinumData.DATE,
      platinumData.CLOSE,
    ),
  },
];

// 专门的钱包类型定义
declare interface EthereumProvider {
  isMetaMask?: boolean;
  isOKXWallet?: boolean;
  isOkxWallet?: boolean;
  isOKX?: boolean;
  isOKExWallet?: boolean;
  isCoinbaseWallet?: boolean;
  isRabby?: boolean;
  isTrustWallet?: boolean;
  isBitKeep?: boolean;
  isBitgetWallet?: boolean;
  isBinanceWallet?: boolean;
  providers?: any[];
  _metamask?: any;
  _events?: any;
  chainId?: string;
  selectedAddress?: string;
  on?: (...args: any[]) => void;
  removeListener?: (...args: any[]) => void;
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  autoRefreshOnNetworkChange?: boolean;
}

// 钱包提供者扩展
declare global {
  interface Window {
    ethereum?: EthereumProvider;
    okxwallet?: EthereumProvider;
    trustwallet?: EthereumProvider;
    BinanceChain?: EthereumProvider;
    bitkeep?: {
      ethereum?: EthereumProvider;
    };
    _FORCE_METAMASK_ONLY?: boolean;
    diagnoseWalletProviders?: () => void;
  }
}

// 确保文件被当作一个模块
export {};

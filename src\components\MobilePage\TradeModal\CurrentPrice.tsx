import { useState, useEffect } from "react";
import styles from "./CurrentPrice.module.scss";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import useApprove from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useApprove";
import useMaxAmount from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useMaxAmount";
import useCreateListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCreateListing";
import useCancelListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCancelListing";
import useCancelOffer from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCancelOffer";
import TokenPrice from "@/components/common/TokenPrice";
import useActiveListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useActiveListing";
import usePurchaseListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/usePurchaseListing";
import MakeOfferModal from "./Modals/MakeOfferModal";
import QuickListModal from "./Modals/QuickListModal";
import CancelListingModal from "./Modals/CancelListingModal";
import CancelOfferModal from "./Modals/CancelOfferModal";
import SelectMineModal from "./Modals/SelectMineModal";
import { useNetwork } from "wagmi";
import useApproveUsdt from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useApproveUsdt";

const CurrentPrice = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const [selectedOption, setSelectedOption] = useState(selectedMine.name);
  const [isMakeOfferModalOpen, setIsMakeOfferModalOpen] = useState(false);
  const [isQuickListModalOpen, setIsQuickListModalOpen] = useState(false);
  const [isCancelListingModalOpen, setIsCancelListingModalOpen] =
    useState(false);
  const [isCancelOfferModalOpen, setIsCancelOfferModalOpen] = useState(false);
  const [isSelectMineModalOpen, setIsSelectMineModalOpen] = useState(false);
  const [purchaseListingQuantity, setPurchaseListingQuantity] = useState(1);
  const { chain } = useNetwork();
  const [isExpanded, setIsExpanded] = useState(true);

  const handleToggle = () => {
    setIsExpanded((prevState) => !prevState);
  };

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedOption(event.target.value);
    mineCardSnapshot.setSelectedMine(event.target.value);
  };

  const { isApprovedForAll, handleApprove, isApproving, isWaitingForApprove } =
    useApprove();
  const { avaliableNfts } = useCreateListing();
  const { maxPricePerToken } = useMaxAmount();
  const [isHandlingPurchasing, setIsHandlingPurchasing] = useState(false);

  const {
    allActiveListingSortedByPrice,
    expirationTime,
    isPurchasable,
    maxPurchasableAmount,
  } = useActiveListing();
  const {
    isPurchasingListing,
    isWaitingForPurchaseListing,
    purchaseListing,
    value: usdtValue,
    refresh,
    setRefresh,
  } = usePurchaseListing({
    amount: BigInt(purchaseListingQuantity),
  });
  const {
    isApprovingUsdt,
    isWaitingForApproveUsdt,
    usdtAllowance,
    handleApproveUsdt,
  } = useApproveUsdt({ usdtValue, purchaseListing, refresh });
  const { allActiveOfferSortedByPrice } = useCancelOffer();
  const { allActiveListingSortedBySeller } = useCancelListing();

  useEffect(() => {
    if (
      purchaseListingQuantity > maxPurchasableAmount &&
      maxPurchasableAmount > 0
    ) {
      setPurchaseListingQuantity(maxPurchasableAmount);
    }
  }, [maxPurchasableAmount]);

  useEffect(() => {
    setIsHandlingPurchasing(
      isPurchasingListing ||
        isWaitingForPurchaseListing ||
        isApprovingUsdt ||
        isWaitingForApproveUsdt,
    );
  }, [
    isPurchasingListing,
    isWaitingForPurchaseListing,
    isApprovingUsdt,
    isWaitingForApproveUsdt,
  ]);

  useEffect(() => {
    const hasChanged =
      isHandlingPurchasing && usdtAllowance >= usdtValue && usdtValue > 0;

    if (hasChanged) {
      setRefresh((prev) => prev + 1);
    }
  }, [isHandlingPurchasing, usdtAllowance, usdtValue, setRefresh]);

  const handlePurchaseListing = () => {
    if (usdtAllowance < usdtValue) {
      handleApproveUsdt();
    } else {
      purchaseListing();
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.currentPrice}>
        <div
          // className={`${styles.mainHeader} ${
          //   isExpanded ? styles.expanded : styles.collapsed
          // }`}
          className={styles.priceHeader}
          // onClick={handleToggle}
        >
          CURRENT PRICE
          {/* <span className={styles.glowText}>CURRENT PRICE</span> */}
          {/* <span className={styles.buttonHighlight}></span> */}
        </div>
        {isExpanded && (
          <div className={styles.priceActions}>
            <div className={styles.buttons}>
              {allActiveListingSortedBySeller &&
              allActiveListingSortedBySeller.length !== 0 ? (
                <button
                  className={styles.sellButton}
                  onClick={() => setIsCancelListingModalOpen(true)}
                  disabled={!isApprovedForAll}
                >
                  CANCEL LISTING
                </button>
              ) : null}
              {allActiveOfferSortedByPrice.length !== 0 ? (
                <button
                  className={styles.sellButton}
                  onClick={() => setIsCancelOfferModalOpen(true)}
                  disabled={!isApprovedForAll}
                >
                  CANCEL OFFER
                </button>
              ) : null}
              {!isApprovedForAll &&
              chain &&
              selectedMine.supportedNetwork.includes(chain?.id) ? (
                <button
                  className={styles.buyButton}
                  onClick={handleApprove}
                  disabled={isApproving || isWaitingForApprove}
                >
                  {isApproving ? (
                    <span className={styles.loader} />
                  ) : isWaitingForApprove ? (
                    <span className={styles.loader} />
                  ) : (
                    "Approve All NFTs"
                  )}
                </button>
              ) : null}
            </div>
            <div className={styles.buyWrapper}>
              {/* <button
              className={`${styles.sellButton} ${styles.pulseButton}`}
              onClick={() => setIsSelectMineModalOpen(true)}
              disabled={!isApprovedForAll}
            >
              <span className={styles.glowText}>Select Mine</span>
              <span className={styles.buttonHighlight}></span>
            </button> */}
              <button
                className={styles.makeOfferButton}
                onClick={() => setIsSelectMineModalOpen(true)}
                // disabled={!isApprovedForAll}
              >
                SELECT MINE
              </button>
            </div>
            <div className={styles.detailsWrapper}>
              <div className={styles.priceInfo}>
                <div className={styles.timer}>
                  <span className={styles.timerIcon}>⏱</span>
                  SALE ENDS{" "}
                  {expirationTime.length > 0 ? expirationTime : "--/--/--"}
                </div>
                <div className={styles.price}>
                  <span className={styles.amount}>
                    {allActiveListingSortedByPrice &&
                    allActiveListingSortedByPrice.length !== 0 ? (
                      <TokenPrice
                        amount={allActiveListingSortedByPrice[0].pricePerToken}
                        tokenAddress={
                          allActiveListingSortedByPrice[0].paymentToken
                        }
                        currencySymbol={selectedMine.currency}
                        showCurrency={true}
                      />
                    ) : (
                      "0"
                    )}
                  </span>
                  <span className={styles.usdPrice}>
                    {maxPurchasableAmount} avaliable
                  </span>
                </div>
              </div>
            </div>
            <div className={styles.actions}>
              {/* <span>MAX PRICE PER LISTING</span> */}
              <div className={styles.maxPrice}>
                {/* <input
                type="number"
                value={formatEther(maxPricePerToken)}
                readOnly
              />{" "} */}
                {/* <span> ETH</span> */}
                <div className={styles.quantity}>
                  <button
                    disabled={!isPurchasable}
                    onClick={() =>
                      setPurchaseListingQuantity((prev) =>
                        prev > 1 ? prev - 1 : prev,
                      )
                    }
                  >
                    -
                  </button>
                  <span>{purchaseListingQuantity}</span>
                  <button
                    disabled={!isPurchasable}
                    onClick={() =>
                      setPurchaseListingQuantity((prev) =>
                        prev < maxPurchasableAmount ? prev + 1 : prev,
                      )
                    }
                  >
                    +
                  </button>
                </div>
              </div>
              <div className={styles.buttonWrapper}>
                <button
                  className={styles.buyNowButton}
                  disabled={
                    !isApprovedForAll || !isPurchasable || isHandlingPurchasing
                  }
                  onClick={handlePurchaseListing}
                >
                  {isHandlingPurchasing ? (
                    <span className={styles.loader} />
                  ) : (
                    `BUY ${purchaseListingQuantity} NOW`
                  )}
                </button>
                <button
                  className={styles.makeOfferButton}
                  onClick={() => setIsMakeOfferModalOpen(true)}
                  disabled={!isApprovedForAll}
                >
                  MAKE OFFER
                </button>
                {avaliableNfts !== 0n ? (
                  <button
                    className={styles.makeOfferButton}
                    onClick={() => setIsQuickListModalOpen(true)}
                    disabled={!isApprovedForAll}
                  >
                    LIST
                  </button>
                ) : null}
              </div>
            </div>
          </div>
        )}
      </div>
      <MakeOfferModal
        isOpen={isMakeOfferModalOpen}
        onClose={() => setIsMakeOfferModalOpen(false)}
      />
      <QuickListModal
        isOpen={isQuickListModalOpen}
        onClose={() => setIsQuickListModalOpen(false)}
      />
      <CancelListingModal
        isOpen={isCancelListingModalOpen}
        onClose={() => setIsCancelListingModalOpen(false)}
      />
      <CancelOfferModal
        isOpen={isCancelOfferModalOpen}
        onClose={() => setIsCancelOfferModalOpen(false)}
      />
      <SelectMineModal
        isOpen={isSelectMineModalOpen}
        onClose={() => setIsSelectMineModalOpen(false)}
      />
      {/* <ToastContainer
        style={{ fontSize: "0.875rem", marginTop: "50px" }}
        position="top-left"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        theme="dark"
      /> */}
    </div>
  );
};

export default CurrentPrice;

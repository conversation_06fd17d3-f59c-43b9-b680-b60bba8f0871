"use client";
import React, { useState } from "react";
import styles from "./RegisterMine.module.scss";
import { SubmitHandler, useForm } from "react-hook-form";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";

type RegisterMineFormData = {
  contactName: string;
  abn: string;
  companyName: string;
  website: string;
  email: string;
  contactNumber: string;
  tenement: number;
  tenementSelection: string;
  tenementFile: string;
  ppt: string;
  pptFile: string;
  technicalReport: string;
  technicalReportFile: string;
  financialReport: string;
  financialReportFile: string;
  valuation: number;
};

// const Divider = () => <div className={styles.divider} />;
// const getBase64 = (file) => {
//   var reader = new FileReader();
//   reader.readAsDataURL(file);
//   reader.onload = () => {
//     console.log(reader.result);
//   };
//   reader.onerror = (error) => {
//     console.log("Error: ", error);
//   };
// };

const RegisterMine = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<RegisterMineFormData>();
  const onSubmit: SubmitHandler<RegisterMineFormData> = (data) =>
    alert("subbmitted");

  const [hasPpt, setHasPpt] = useState(false);
  const [hasTechnicalReport, setHasTechnicalReport] = useState(false);
  const [hasFinancialreport, setHasFinancialreport] = useState(false);

  return (
    <div className={styles.wrapper}>
      <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>Register Mine</h1>
          <h2 className={styles.subTitle}>Express Your Interest</h2>
        </div>
        {/* <img src={dotDotDot.src} alt="dot dot dot icon" /> */}
      </div>
      <div className={styles.scrollArea} style={{ height: "84%" }}>
        <div className={styles.formDataContainer}>
          <form onSubmit={handleSubmit(onSubmit)} autoComplete="off">
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Company Name</label>
                <input
                  placeholder="Enter Company Name"
                  {...register("companyName", {
                    required: "Please provide a company name",
                  })}
                />
                <p>{errors.companyName?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Company Website</label>
                <input
                  placeholder="Enter Company Website"
                  {...register("website", {
                    required: "Please provide a comapny website",
                    pattern: {
                      value:
                        /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi,
                      message: "Please provide a valid website",
                    },
                  })}
                />
                <p>{errors.website?.message}</p>
              </div>
            </div>
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Company ABN</label>
                <input
                  placeholder="Enter Company ABN"
                  {...register("abn", {
                    required: "Please provide a ABN",
                    pattern: {
                      value: /^(\d *?){11}$/,
                      message: "Please provide a valid ABN",
                    },
                  })}
                />
                <p>{errors.abn?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Contact Name</label>
                <input
                  placeholder="Enter Contact Name"
                  {...register("contactName", {
                    required: "Please provide contact name",
                  })}
                />
                <p>{errors.contactName?.message}</p>
              </div>
            </div>
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Contact Email</label>
                <input
                  placeholder="Enter Contact Email"
                  {...register("email", {
                    required: "Please provide contact email",
                    pattern: {
                      value:
                        /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                      message: "Please provide a valid email",
                    },
                  })}
                />
                <p>{errors.email?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Contact Number</label>
                <input
                  placeholder="Enter Contact Number"
                  {...register("contactNumber", {
                    required: "Please provide contact number",
                    pattern: {
                      value:
                        /^(?:\+?(61))? ?(?:\((?=.*\)))?(0?[2-57-8])\)? ?(\d\d(?:[- ](?=\d{3})|(?!\d\d[- ]?\d[- ]))\d\d[- ]?\d[- ]?\d{3})$/gm,
                      message: "Please provide a valid contact number",
                    },
                  })}
                />
                <p>{errors.contactNumber?.message}</p>
              </div>
            </div>
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Tenement</label>
                <input
                  placeholder="Number of tenements"
                  {...register("tenement", {
                    required: "Please provide number of tenements",
                    min: {
                      value: 1,
                      message: "It should be at least 1 tenement",
                    },
                  })}
                  type="number"
                  defaultValue={1}
                  min={1}
                />
                <p>{errors.tenement?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Tenement type</label>
                <select
                  {...register("tenementSelection", { required: true })}
                  defaultValue={"exploring"}
                >
                  <option value="exploring">Exploring</option>
                  <option value="developed" disabled>
                    Developed
                  </option>
                </select>
                <p>{errors.tenementSelection?.message}</p>
              </div>
            </div>
            <div className={styles.formSection}>
              <label>Submit tenement report</label>
              <input
                {...register("tenementFile", {
                  required: "Please upload tenement report",
                })}
                type="file"
              />
              <p>{errors.tenementFile?.message}</p>
            </div>

            <div className={styles.formSection}>
              <label>Do you have a PPT?</label>
              <select
                {...register("ppt", { required: true })}
                defaultValue={"no"}
                onChange={() => {
                  setHasPpt(!hasPpt);
                }}
              >
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
              <p>{errors.ppt?.message}</p>
            </div>
            {hasPpt ? (
              <div className={styles.formSection}>
                <label>Submit PPT</label>
                <input
                  {...register("pptFile", {
                    required: "Please upload ppt",
                  })}
                  type="file"
                />
                <p>{errors.pptFile?.message}</p>
              </div>
            ) : null}
            <div className={styles.formSection}>
              <label>Do you have a technical report?</label>
              <select
                {...register("technicalReport", { required: true })}
                defaultValue={"no"}
                onChange={() => {
                  setHasTechnicalReport(!hasTechnicalReport);
                }}
              >
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
              <p>{errors.technicalReport?.message}</p>
            </div>
            {hasTechnicalReport ? (
              <div className={styles.formSection}>
                <label>Submit technical report</label>
                <input
                  {...register("technicalReportFile", {
                    required: "Please upload technical report",
                  })}
                  type="file"
                />
                <p>{errors.technicalReportFile?.message}</p>
              </div>
            ) : null}
            <div className={styles.formSection}>
              <label>Do you have a financial report?</label>
              <select
                {...register("financialReport", { required: true })}
                defaultValue={"no"}
                onChange={() => {
                  setHasFinancialreport(!hasFinancialreport);
                }}
              >
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
              <p>{errors.financialReport?.message}</p>
            </div>
            {hasFinancialreport ? (
              <div className={styles.formSection}>
                <label>Submit financial report</label>
                <input
                  {...register("financialReportFile", {
                    required: "Please upload financial report",
                  })}
                  type="file"
                />
                <p>{errors.financialReportFile?.message}</p>
              </div>
            ) : null}
            <div className={styles.formSection}>
              <label>Do you have current valuation?</label>
              <div className={styles.currentValuation}>
                <span>$</span>
                <input
                  {...register("valuation", {
                    required: "Please provide current valuation",
                    min: {
                      value: 1,
                      message: "It should be at least $1 valuation",
                    },
                  })}
                  type="number"
                  defaultValue={1}
                  min={1}
                />
                <p>{errors.valuation?.message}</p>
              </div>
            </div>
            <button type="submit" className={styles.submitButton}>
              Submit
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RegisterMine;

"use server";

import * as ethers from "ethers";

type SignContributionParams = {
  userAddress: string;
  amount: string;
  referralCode: string;
  nonce: number;
  chainId: number;
  contractAddress: string;
};

export async function signContribution({
  userAddress,
  amount,
  referralCode,
  nonce,
  chainId,
  contractAddress,
}: SignContributionParams): Promise<string> {
  try {
    // 验证所有必需的字段
    if (
      !userAddress ||
      !amount ||
      !referralCode ||
      nonce === undefined ||
      !chainId ||
      !contractAddress
    ) {
      throw new Error("Missing required fields");
    }

    // 从环境变量获取签名私钥(不带NEXT_PUBLIC_前缀，确保只在服务端可用)
    const signingPrivateKey = process.env.SIGNING_PRIVATE_KEY;
    if (!signingPrivateKey) {
      console.error("Signing key not configured");
      throw new Error("Server configuration error");
    }

    // 创建签名器
    const signer = new ethers.Wallet(signingPrivateKey);

    // 创建要签名的消息
    const message = ethers.solidityPackedKeccak256(
      ["address", "uint256", "bytes32", "uint256", "uint256", "address"],
      [
        userAddress, // 用户地址
        BigInt(amount), // 认购金额
        ethers.encodeBytes32String(referralCode), // 推荐码
        BigInt(nonce), // nonce
        BigInt(chainId), // 链ID
        contractAddress, // 合约地址
      ],
    );

    // 与客户端相同的签名方式
    const messageBytes = ethers.getBytes(message);
    const signature = await signer.signMessage(messageBytes);

    return signature;
  } catch (error) {
    console.error("Error during signing:", error);
    throw error;
  }
}

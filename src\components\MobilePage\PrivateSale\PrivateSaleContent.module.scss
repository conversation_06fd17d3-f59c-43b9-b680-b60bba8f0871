// components/TokenPromoCard.module.scss
@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  // background: linear-gradient(to bottom, #01141a, #000);
  color: white;
  font-family: $font-family-orbitron;
  position: absolute;
  width: 90%;
  height: 100%;
  overflow: scroll;
  // padding-right: $padding-md;
  padding-bottom: 3rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  // border: 1px solid yellow;
  &::-webkit-scrollbar {
    display: none;
  }
}

.header {
  width: 100%;
  // padding: 1rem 0 0.5rem;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  display: flex;
  align-items: center;
}

.titleGroup {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;

  .perk {
    font-size: 0.85rem;
    font-weight: bold;
    text-transform: uppercase;
    color: #00ffff;
    margin: 0;
  }

  .subperk {
    font-size: 0.7rem;
    color: $color-primary-contrast;
    margin: 0;
  }
}

.deadlineInfo {
  width: 100%;
  // padding: 0 0 1.5rem;

  .deadline {
    font-size: 1rem;
    color: #888;
    letter-spacing: 1px;
    margin: 0;
    font-family: $font-family-orbitron;
  }
}

.exchangeRate {
  font-size: 1.2rem;
  margin-top: 1rem;
  color: #00ffff;
}

.tokenEquation {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin: 1rem 0;
  padding: 1rem;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), transparent);
  border-radius: 12px;
  border-left: 2px solid rgba(0, 255, 255, 0.3);

  .priceGroup {
    @include col-center;
    position: relative;

    .priceToken {
      font-size: 2rem;
      font-weight: 700;
      background: linear-gradient(
        to right,
        #00ffff 20%,
        #ffffff 50%,
        #00ffff 80%
      );
      background-size: 200% 100%;
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation:
        textPulse 2s infinite,
        colorFlow 2s linear infinite;
    }

    @keyframes textPulse {
      0% {
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
      }
      50% {
        text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
      }
      100% {
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
      }
    }

    @keyframes colorFlow {
      0% {
        background-position: 100% 0;
      }
      100% {
        background-position: -100% 0;
      }
    }

    .priceUnit {
      position: absolute;
      bottom: -1rem;
      font-size: 1rem;
      font-weight: 300;
      letter-spacing: 1px;
      color: rgba(0, 255, 255, 0.8);
      text-transform: uppercase;
    }
  }
}

.progressBar {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  height: 8px;
  margin: 1rem 0;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 255, 255, 0.1);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }
}

.progress {
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 255, 255, 1),
    rgba(77, 255, 255, 1) 50%,
    rgba(0, 255, 255, 1)
  );
  border-radius: inherit;
  transition: width 0.3s ease-in-out;
  position: relative;
  box-shadow:
    0 0 10px rgba(0, 255, 255, 0.5),
    0 0 20px rgba(0, 255, 255, 0.3),
    0 0 30px rgba(0, 255, 255, 0.2),
    inset 0 0 15px rgba(255, 255, 255, 0.4);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.8),
      transparent
    );
    animation: shine 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(255, 255, 255, 0.1) 10px,
      rgba(255, 255, 255, 0.1) 20px
    );
    animation: move 20s linear infinite;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 50px 50px;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.progressInfo {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;

  strong {
    color: #00ffff;
  }
}

.eth {
  color: #00ffff;
  font-size: 0.85rem;
}

.description {
  font-size: 0.9rem;
  margin: 1rem 0;
  line-height: 1.4;
  strong {
    color: #00ffff;
  }
}

.section {
  margin-top: 1.5rem;
}

.sectionTitle {
  font-weight: bold;
  color: #00ffff;
  font-size: 0.95rem;
  margin: 0;
}

.sectionText {
  font-size: 0.9rem;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.detailsBtn {
  @include row-center;
  width: 100%;
  text-align: center;
  font-size: $font-size-sm;
  font-family: $font-family-poppins;
  color: #00ffff;
  text-transform: uppercase;
  font-weight: $font-weight-semibold;
  border: $border-width-xs solid $color-primary;
  padding: $padding-sm 0;
  margin: 1rem 0;
  background: $color-primary-transparent;
  border-radius: $border-radius-sm;
  cursor: pointer;

  span {
    color: $color-primary-transparent-contrast;
  }

  &:hover {
    border: $border-width-xs solid $color-primary;
  }
}

.footer {
  background: #00ffff;
  color: black;
  padding: 1rem;
  text-align: center;
  // margin-top: 1rem;
  border-radius: 12px;
}

.footerJoin {
  font-size: 1.1rem;
  font-weight: bold;
}

.footerRate {
  font-size: 0.9rem;

  .highlightedNumber {
    font-weight: bold;
    font-size: 1rem;
    color: #f7931a;
  }
}

import { bottomUpFadeIn, buttonEffect, fadeIn } from "@/animations/animations";
import styles from "./PlusModal.module.scss";
import { motion } from "framer-motion";
import { plusModalStore } from "@/stores/plusModal";
import { useSnapshot } from "valtio";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import PlusModalHeader, {
  headerDetails,
} from "./PlusModalHeader/PlusModalHeader";
import PlusModalMainContents from "./PlusModalMainContents/PlusModalMainContents";
import { mineCardStore } from "@/stores/mineCard";
import { downloadModalStore } from "@/stores/downloadModal";
import { minesDetails } from "@/constants/mineDetails";

const PlusModal = () => {
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const mineCardSnapshot = useSnapshot(mineCardStore);

  const downloadPdf = () => {
    const selectedMine = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0];

    // Check if there's a legacy detailsLink for backward compatibility
    if (selectedMine.detailsLink !== "") {
      window.open(selectedMine.detailsLink, "_blank");
    } else {
      // Use the new file download modal
      downloadModalStore.setSelectedMine(selectedMine.name);
      downloadModalStore.setIsOpen(true);
    }
  };

  return (
    <motion.div
      className={styles.container}
      variants={fadeIn(1)}
      initial="hidden"
      animate="visible"
      exit="hidden"
      transition={{ duration: 1 }}
    >
      <motion.div className={styles.modalFrame} variants={fadeIn(0.5)}>
        <div
          className={styles.closeButton}
          onClick={() => {
            plusModalSnapshot.setIsOpenPlusModal(false);
            plusModalSnapshot.setSelectedTitle(headerDetails[0].title);
          }}
        >
          <img src={crossIcon.src} alt="cross icon" />
        </div>
        <div className={styles.sellMineOnline}>
          <h1>sell mine online</h1>
          <h1>2023</h1>
        </div>
        <div className={styles.downloadButton} onClick={downloadPdf}>
          <h1>documents download</h1>
        </div>

        <div className={styles.contentWrapper}>
          <PlusModalHeader />
          <PlusModalMainContents />
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PlusModal;

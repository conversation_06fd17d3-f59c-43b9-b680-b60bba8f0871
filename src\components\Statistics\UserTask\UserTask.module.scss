@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  max-width: 100%;
  padding: 2rem 0;

  .title {
    text-align: center;
    font-size: 3rem;
    color: $color-primary;
  }

  .completedTasks {
    width: 100%;
    border: 1px solid $color-primary-contrast;
    margin-top: 2rem;
    border-radius: 1rem;
    padding: 1rem 1rem 2rem;
    background: $color-black-transparent;

    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid $color-primary-contrast;
    }

    th {
      color: $color-warning;
      font-size: 2rem;
      font-weight: bold;
    }

    td {
      font-size: 1.6rem;
    }

    tbody {
      display: block;
      max-height: 500px;
      overflow: auto;
    }
    thead,
    tbody tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  }
}

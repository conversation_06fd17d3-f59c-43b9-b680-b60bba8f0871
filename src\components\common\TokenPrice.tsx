import React from "react";
import { useFormatTokenPrice } from "@/hooks/useTokenPrice";

interface TokenPriceProps {
  amount: bigint | undefined;
  tokenAddress?: `0x${string}`;
  className?: string;
  showCurrency?: boolean;
  currencySymbol?: string;
}

/**
 * Universal token price display component
 * Automatically select the correct decimals to format price based on token address
 */
// TokenPrice now uses useFormatTokenPrice to ensure on-chain decimals are always used if available
export const TokenPrice: React.FC<TokenPriceProps> = ({
  amount,
  tokenAddress,
  className,
  showCurrency = false,
  currencySymbol = "",
}) => {
  const formattedPrice = useFormatTokenPrice(amount, tokenAddress);

  return (
    <span className={className}>
      {formattedPrice}
      {showCurrency && currencySymbol ? ` ${currencySymbol}` : ""}
    </span>
  );
};

export default TokenPrice;

import { getMetalsDataFromOwnServer } from "@/actions/getMetalsDataFromOwnServer";
import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export type MetalsData = Awaited<ReturnType<typeof getMetalsDataFromOwnServer>>;

export const metalsDataStore = proxy({
  metalsData: null as MetalsData | null,
  setMetalsData: (newMetalsData: MetalsData) => {
    metalsDataStore.metalsData = newMetalsData;
  },
  fetchMetalsData: async () => {
    const newData = await getMetalsDataFromOwnServer();
    metalsDataStore.setMetalsData(newData);
  },
});

devtools(metalsDataStore, {
  name: "metalsDataStore",
  enabled: false,
});

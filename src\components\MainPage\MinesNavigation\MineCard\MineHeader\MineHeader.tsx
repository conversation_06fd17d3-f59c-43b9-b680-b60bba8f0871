import styles from "./MineHeader.module.scss";
import headerIcon from "@/assets/icons/minesNavigation/mineHeaderIcon.png";
import locationIcon from "@/assets/icons/minesNavigation/locationIcon.png";
import { MineCardProps } from "../MineCard";
const MineHeader = ({ information }: Pick<MineCardProps, "information">) => {
  return (
    <div className={styles.container}>
      <img src={headerIcon.src} alt="header icon" />
      <div>
        <div className={styles.subtitle}>
          <h1 className={styles.mineral}>{information.mineMineral}</h1>
          <img src={locationIcon.src} alt="location icon" />
          <h1 className={styles.location}>{information.mineLocation}</h1>
        </div>
        <h1 className={styles.title}>{information.name}</h1>
      </div>
    </div>
  );
};

export default MineHeader;

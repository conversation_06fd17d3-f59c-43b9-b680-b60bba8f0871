import Image from "next/image";
import { motion } from "framer-motion";
import CountUp from "react-countup";
import styles from "./PrivateSaleContent.module.scss";
import asteroidXIcon from "@/components/MainPage/PrivateSale/assets/asteroidXIcon.png";
import useProgressBar from "@/components/MainPage/PrivateSale/components/MainContent/HeroSection/Navigation/hooks/useProgressBar";
import { useState } from "react";
import DetailsModal from "./Modals/DetailsModal/DetailsModal";
import ShareModal from "./Modals/ShareModal/ShareModal";
import JoinNowModal from "./Modals/JoinNowModal/JoinNowModal";
import { useSearchParams } from "next/navigation";
import { useAccount } from "wagmi";
import useTokenInfo from "@/components/MainPage/PrivateSale/components/MainContent/CtaSection/SaleDetails/hooks/useTokenInfo";
import { formatEther } from "viem";
import { showError } from "@/lib/notification";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 },
};

const footerVariants = {
  hidden: { y: 100, opacity: 0 },
  visible: { y: 0, opacity: 1 },
};

// Sub-components
const Icon = ({ src, alt }: { src: string; alt: string }) => (
  <div className={styles.iconWrapper}>
    <Image src={src} alt={alt} width={40} height={40} />
  </div>
);

const Header = () => (
  <header className={styles.header}>
    <div className={styles.headerContent}>
      <div className={styles.logo}>
        <Icon src={asteroidXIcon.src} alt="asteroidXIcon" />
      </div>
      <div className={styles.titleGroup}>
        <p className={styles.perk}>JOIN NOW TO GET</p>
        <p className={styles.subperk}>Be Among the First to Own $DrillX</p>
      </div>
    </div>
  </header>
);

const DeadlineInfo = () => (
  <div className={styles.deadlineInfo}>
    <p className={styles.deadline}>UNTIL DECEMBER 3, 2025</p>
  </div>
);

const TokenExchangeRate = () => (
  <div className={styles.tokenEquation}>
    <span className={styles.tokenValue}>1 HSK</span>
    <span className={styles.tokenSymbol}>≈</span>
    <div className={styles.priceGroup}>
      <span className={styles.priceToken}>
        <CountUp end={3.33} duration={3} decimals={2} />
      </span>
      <span className={styles.priceUnit}>DrillX</span>
    </div>
  </div>
);

const ProgressSection = ({
  progressPercentage,
  avaliableNativeToken,
}: {
  progressPercentage: number;
  avaliableNativeToken: string;
}) => (
  <>
    <span className={styles.exchangeRate}>{progressPercentage}%</span>
    <div className={styles.progressBar}>
      <div
        className={styles.progress}
        style={{ width: `${progressPercentage}%` }}
      />
    </div>
    <div className={styles.progressInfo}>
      <span>
        Only <strong>{avaliableNativeToken}</strong> HSK left until the next
        price increase.
      </span>
    </div>
  </>
);

const Footer = ({ onJoinNowClick }: { onJoinNowClick: () => void }) => {
  const searchParams = useSearchParams();
  const search = searchParams.get("invitation");
  const { address } = useAccount();
  const { userTotalPurchased } = useTokenInfo("");
  const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";

  const handleClick = () => {
    if (!isTestnet) {
      showError("Coming soon! This feature is not available yet.");
      return;
    }
    if (!address) {
      showError("User is not connected");
      return;
    }
    onJoinNowClick();
  };

  return (
    <motion.footer
      className={styles.footer}
      variants={footerVariants}
      initial="hidden"
      animate="visible"
      transition={{ delay: 0.4, duration: 0.5 }}
      onClick={handleClick}
    >
      <h3 className={styles.footerJoin}>Join Now</h3>
      {userTotalPurchased === 0 ? (
        <p className={styles.footerRate}>You have no $DrillX yet</p>
      ) : (
        <p className={styles.footerRate}>
          You own{" "}
          <span className={styles.highlightedNumber}>
            {formatEther(BigInt(userTotalPurchased))}
          </span>
          $DrillX{" "}
        </p>
      )}
    </motion.footer>
  );

  // <motion.footer
  //   className={styles.footer}
  //   variants={footerVariants}
  //   initial="hidden"
  //   animate="visible"
  //   transition={{ delay: 0.4, duration: 0.5 }}
  //   onClick={() => onJoinNowClick()}
  // >
  //   <p className={styles.footerJoin}>JOIN NOW</p>
  //   <p className={styles.footerRate}>Limited supply: 1 HSK = 3.33 DrillX</p>
  // </motion.footer>
};

export default function PrivateSaleContent() {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isJoinNowModalOpen, setIsJoinNowModalOpen] = useState(false);
  const { progressPercentage, avaliableNativeToken } = useProgressBar();
  const openRewardsModal = () => {
    setIsShareModalOpen(true);
  };
  return (
    <div className={styles.container}>
      <Header />
      <DeadlineInfo />

      <motion.div
        className={styles.content}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.6 }}
      >
        <TokenExchangeRate />
        <ProgressSection
          progressPercentage={progressPercentage}
          avaliableNativeToken={avaliableNativeToken}
        />

        <motion.button
          className={styles.detailsBtn}
          onClick={() => setIsDetailsModalOpen(true)}
          whileTap={{ scale: 0.95 }}
        >
          $DrillX Details
        </motion.button>

        <motion.button
          className={styles.detailsBtn}
          onClick={() => setIsShareModalOpen(true)}
          whileTap={{ scale: 0.95 }}
        >
          SHARE
        </motion.button>
      </motion.div>

      <Footer onJoinNowClick={() => setIsJoinNowModalOpen(true)} />

      <DetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
      />
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
      />
      <JoinNowModal
        isOpen={isJoinNowModalOpen}
        onClose={() => setIsJoinNowModalOpen(false)}
        openRewardsModal={openRewardsModal}
      />
    </div>
  );
}

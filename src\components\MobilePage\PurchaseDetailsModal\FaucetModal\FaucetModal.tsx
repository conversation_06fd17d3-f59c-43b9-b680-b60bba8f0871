import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./FaucetModal.module.scss";
import {
  useAccount,
  useNetwork,
  usePrepareContractWrite,
  useContractWrite,
} from "wagmi";
import { airdropABI } from "@/constants/abis/AirdropABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError, parseEther, formatEther } from "viem";

type FaucetModalProps = {
  setIsOpenFaucetModal: Dispatch<SetStateAction<boolean>>;
};
const FaucetModal = ({ setIsOpenFaucetModal }: FaucetModalProps) => {
  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();
  const [airdropAddress, setAirdropAddress] = useState<`0x${string}`>("0x");

  const { config } = usePrepareContractWrite({
    address: airdropAddress,
    abi: airdropABI,
    functionName: "claim",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && airdropAddress !== "0x",
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
  });

  const {
    write,
    data,
    isLoading: isMintingNft,
  } = useContractWrite({
    ...config,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      setTimeout(() => {
        showSuccess("Congratulations! usdt was successfully claimed!");
        console.log("tx: ", data?.hash);
      }, 3000);
    },
  });

  const getAirdropAddress = (chainId: number) => {
    return networkConfigs[chainId].airdropAddress;
  };

  useEffect(() => {
    if (chain) {
      setAirdropAddress(getAirdropAddress(chain.id) ?? "0x");
    }
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.faucetModal}>
        <h1 className={styles.title}>Claim Test USDT</h1>
        <h3 className={styles.supportedNetwork}>{chain?.name}</h3>
        <div className={styles.walletAddressWrapper}>
          <input className={styles.walletAddress} readOnly value={address} />
        </div>
        <div className={styles.actionButtonWrapper}>
          <button
            className={styles.cancelButton}
            onClick={() => setIsOpenFaucetModal(false)}
          >
            Cancel
          </button>
          <button
            className={styles.confirmButton}
            onClick={() => {
              write?.();
            }}
          >
            Claim
          </button>
        </div>
      </div>
    </div>
  );
};

export default FaucetModal;

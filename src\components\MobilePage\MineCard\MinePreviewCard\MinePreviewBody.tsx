import styles from "./MinePreviewBody.module.scss";
import snakeIcon from "@/assets/icons/minesNavigation/snakeIcon.png";
import Image from "next/image";

import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import PlusModal from "../../PlusModal/PlusModal";
import { plusModalStore } from "@/stores/plusModal";
import { downloadModalStore } from "@/stores/downloadModal";
import { useNetwork } from "wagmi";
import peckShieldIcon from "@/assets/icons/peckShieldIcon.png";

interface InformationProps {
  title: string;
  subtitle: React.ReactNode;
  borderBottom?: boolean;
  showLogo?: boolean;
}

const MinePreviewBody = () => {
  const Information = ({
    title,
    subtitle,
    borderBottom,
    showLogo,
  }: InformationProps) => (
    <>
      <div
        className={styles.information}
        style={{ borderBottom: borderBottom ? undefined : "none" }}
      >
        <div className={styles.logoWrapper}>
          <div>
            <h1 className={styles.title}>{title}</h1>
            <h1 className={styles.subtitle}>{subtitle}</h1>
          </div>
          {showLogo ? (
            <Image
              src={snakeIcon.src}
              alt={"snake icon"}
              width={20}
              height={35}
            />
          ) : null}
        </div>
      </div>
    </>
  );
  const plusModalSnapshot = useSnapshot(plusModalStore);

  const mineCardSnapshot = useSnapshot(mineCardStore);

  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const downloadPdf = () => {
    const selectedMine = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0];

    // Check if there's a legacy detailsLink for backward compatibility
    if (selectedMine.detailsLink !== "") {
      window.open(selectedMine.detailsLink, "_blank");
    } else {
      // Use the new file download modal
      downloadModalStore.setSelectedMine(selectedMine.name);
      downloadModalStore.setIsOpen(true);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.titleInformation}>
        <div className={styles.imageFrame}>
          <Image
            src={information.mineImages[0]}
            alt={information.name}
            width={500}
            height={300}
          />
        </div>
      </div>

      <div className={styles.horizontalContainer}>
        <div className={styles.leftSection}>
          <div className={styles.title}>Price</div>
          <div className={styles.subtitle}>${information.minePrice}</div>
        </div>
        <div className={styles.rightSection}>
          <div className={styles.title}>Highlight</div>
          <div className={styles.subtitle}>{information.mineStorage}</div>
        </div>
      </div>

      <Information
        title={"Mine Location"}
        subtitle={information.location[0]}
        borderBottom
      />
      <Information
        title={"TENEMENT AREA"}
        subtitle={information.area === "" ? "N/A" : information.area}
        borderBottom
        // showLogo
      />
      <Information
        title={"RESOURCE TYPE"}
        subtitle={information.mineType}
        borderBottom
      />
      <Information
        title={"LICENSE VALIDITY"}
        subtitle={information.year}
        borderBottom
      />
      <Information
        title={"RESOURCE ESTIMATE"}
        subtitle={
          Array.isArray(information.resource)
            ? information.resource.map((line, idx) => (
                <div key={idx} style={{ marginBottom: 4 }}>
                  {line}
                </div>
              ))
            : information.resource === ""
            ? "N/A"
            : information.resource
        }
        borderBottom
      />
      <Information
        title={"GEOLOGY"}
        subtitle={information.storage === "" ? "N/A" : information.storage}
        borderBottom
      />
      <motion.div
        className={styles.detailsButton}
        whileTap={buttonEffect.tap}
        onClick={downloadPdf}
      >
        Documents Download
      </motion.div>
      <motion.div
        className={styles.detailsButton}
        whileTap={buttonEffect.tap}
        onClick={() => {
          plusModalSnapshot.setIsOpenPlusModal(true);
        }}
      >
        Check Plus Modal
      </motion.div>
      <div className={styles.auditArea}>
        <h5 className={styles.auditedText}>
          *Audited by
          <a
            href="https://github.com/peckshield/publications/tree/master/audit_reports/PeckShield-Audit-Report-AsteroidX-v1.0.1.pdf"
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: "inline-block",
              verticalAlign: "middle",
              marginLeft: 4,
            }}
          >
            <img
              src={peckShieldIcon.src}
              alt="peckShieldIcon"
              height={14}
              style={{ verticalAlign: "middle" }}
            />
          </a>
        </h5>
      </div>
      <br />
    </div>
  );
};

export default MinePreviewBody;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  @include row-center;
  justify-content: flex-start;
  gap: $spacing-sm;

  .subtitle {
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-sm;

    .mineral {
      // background: rgba(255, 255, 255, 0.07);
      background: $color-primary-transparent;
      padding: 0 $padding-xs;
      border-radius: $border-radius-sm;
      font-size: $font-size-sm;
      // color: $color-primary-transparent-contrast;
      color: $color-warning;
      font-weight: $font-weight-medium;
    }

    .location {
      font-size: $font-size-2xs;
      font-weight: $font-weight-bold;
      color: $color-primary;
    }
  }

  .title {
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    // color: $color-primary;
    color: white;
  }
}

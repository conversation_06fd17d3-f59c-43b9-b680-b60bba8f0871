import { useState } from "react";
import styles from "./RightNavigation.module.scss";
import NavigationButton from "./NavigationButton/NavigationButton";
import TransactionData from "./TransactionData/TransactionData";

const LeftNavigation = ({ delay = 2 }) => {
  const [isButtonClicked, setIsButtonClicked] = useState(false);

  const navigationButtonProps = {
    delay,
    isButtonClicked,
    setIsButtonClicked,
  };

  const transactionDataProps = {
    delay: (delay += 2),
    isButtonClicked,
    setIsButtonClicked,
  };

  return (
    <div className={styles.container}>
      <TransactionData {...transactionDataProps} />
      <NavigationButton {...navigationButtonProps} />
    </div>
  );
};

export default LeftNavigation;

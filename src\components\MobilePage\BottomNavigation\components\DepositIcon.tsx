import variables from "@/styles/variables.module.scss";
const DepositIcon = ({ color = variables.colorPrimaryContrast }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Frame">
        <path
          id="Vector"
          d="M7 14L49 18.6667V46.6667L7 42V14Z"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          id="Vector_2"
          d="M44.3333 18.148V9.33334L7.00056 13.9999"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export default DepositIcon;

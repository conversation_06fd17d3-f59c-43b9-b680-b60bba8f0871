@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

// .container {
//   width: 100%;
//   padding: 1rem 0;
//   // border: 1px solid $color-primary-contrast;
//   .title {
//     text-align: center;
//     font-size: 1.5rem;
//     color: $color-primary;
//   }

// .completedTasks {
//   width: 100%;
//   border: 1px solid $color-primary-contrast;
//   margin-top: 2rem;
//   border-radius: 1rem;
//   padding: 1rem 1rem 2rem;
//   background: $color-black-transparent;

//   th,
//   td {
//     padding: 1rem;
//     text-align: left;
//     border-bottom: 1px solid $color-primary-contrast;
//   }

//   th {
//     color: $color-warning;
//     font-size: 2rem;
//     font-weight: bold;
//   }

//   td {
//     font-size: 1.6rem;
//   }
// }
// .completedTasks {
//   width: 100%;
//   border-radius: 1rem;
//   border-collapse: separate;
//   // border-spacing: 0.3rem 8px;
//   border: 1px solid $color-primary;

//   tr {
//     &:hover {
//       background-color: #2a2a2a;
//     }
//   }

//   th {
//     text-align: center;
//     padding: $padding-sm;
//     border-bottom: 1px solid $color-primary-contrast;
//     color: $color-warning;
//   }

//   td {
//     text-align: center;
//     padding: $padding-md 0;
//   }
// }
// .rankCell {
//   font-weight: bold;
//   color: $color-primary;
// }
// }

.container {
  width: 100%;
  color: $color-primary;

  padding-top: $margin-md;
  background: rgba(0, 0, 0, 0.3);
  // border-radius: 8px;
}

.title {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: $margin-md;
  font-size: $font-size-md;
  text-transform: uppercase;
  color: $color-primary;
  font-family: "Garalama", sans-serif;
  font-weight: $font-weight-extralight;
  letter-spacing: 1px;
}

.taskList {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.taskRow {
  display: grid;
  grid-template-columns: 25% 60% 10px;
  // gap: 20px;
  padding: $padding-sm;
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 1px solid rgba(0, 188, 212, 0.5);
}

.sourceCell,
.descriptionCell,
.pointsCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  font-weight: bold;
  // opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.value {
  font-size: $font-size-xs;

  color: white;
}

/* 为最后一行移除底部边框 */
.taskRow:last-child {
  border-bottom: none;
  margin-bottom: $margin-md;
}

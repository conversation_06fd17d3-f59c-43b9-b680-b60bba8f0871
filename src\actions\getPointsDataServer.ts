"use server";
import * as z from "zod";

export const getPointsDataServer = async (address: string) => {
  let pointsData: TPointsResponse["data"] = {
    pointsItems: [],
    total: 0,
    userPoints: 0,
    percent: 0,
    rank: "",
  };
  const url = `https://asteroidx.io/api/points/list?userAddress=${address}&pageNum=1&pageSize=20`;

  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TPointsResponse;
    pointsData = data.data;
  }

  return pointsData;
};

export const getTotalUsersServer = async () => {
  let totalUsers: TPointsResponse["data"]["total"] = 0;
  const url = `https://asteroidx.io/api/points/list?pageNum=1&pageSize=1`;
  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TPointsResponse;
    totalUsers = data.data.total;
  }

  return totalUsers;
};

export const getUserTaskServer = async (address: string) => {
  let userTasks: TUserTask["data"]["pointsItems"] = [];
  const url = `https://asteroidx.io/api/points/${address}`;

  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TUserTask;
    userTasks = data.data.pointsItems;
  }

  return userTasks;
};

export const getUserCheckinStatusServer = async (address: string) => {
  let userCheckinStatus: TUserCheckinStatus["data"] = {
    canCheckin: false,
    nextCheckinSeconds: 0,
  };
  const url = `https://asteroidx.io/api/points/checkin/status/${address}`;

  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TUserCheckinStatus;
    userCheckinStatus = data.data;
  }

  return userCheckinStatus;
};

export const checkinUserServer = async (address: string) => {
  let responseCode: TUserCheckin = {
    code: -1,
    data: { nextCheckinSeconds: 0 },
  };

  const url = "https://asteroidx.io/api/points/checkin";

  const response = await fetch(url, {
    cache: "no-cache",
    method: "POST",
    body: JSON.stringify({ userAddress: address }),
  });

  if (response.ok) {
    const data = (await response.json()) as TUserCheckin;
    responseCode = data;
  }

  return responseCode;
};

// types
const PointsItemSchema = z.object({
  userAddress: z.string(),
  totalPoints: z.number(),
});

const DataSchema = z.object({
  pointsItems: z.array(PointsItemSchema),
  total: z.number(),
  userPoints: z.number(),
  percent: z.number(),
  rank: z.string(),
});

const TPointsResponseSchema = z.object({
  code: z.number(),
  info: z.string(),
  data: DataSchema,
});
type TPointsResponse = z.infer<typeof TPointsResponseSchema>;

// user task types
const userTaskPointsItemSchema = z.object({
  sourcePlatform: z.string(),
  taskName: z.string(),
  points: z.number(),
});

const userTaskDataSchema = z.object({
  pointsItems: z.array(userTaskPointsItemSchema),
});

const TUserTaskSchema = z.object({
  code: z.number(),
  info: z.string(),
  data: userTaskDataSchema,
});
type TUserTask = z.infer<typeof TUserTaskSchema>;

// user checkin status types
const TUserCheckinStatusSchema = z.object({
  code: z.number(),
  data: z.object({
    canCheckin: z.boolean(),
    nextCheckinSeconds: z.number(),
  }),
});

// user checkin types
const TUserCheckinSchema = z.object({
  code: z.number(),
  data: z.object({
    nextCheckinSeconds: z.number(),
  }),
});

type TUserCheckin = z.infer<typeof TUserCheckinSchema>;

type TUserCheckinStatus = z.infer<typeof TUserCheckinStatusSchema>;

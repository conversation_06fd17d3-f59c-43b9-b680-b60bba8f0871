import { teamDetails } from "@/constants/teamDetails";
import styles from "./TeamsInformation.module.scss";
import Tilt from "react-parallax-tilt";
import variables from "@/styles/variables.module.scss";
import { minesDetails } from "@/constants/mineDetails";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;
type TeamsInformationCardProps = {
  mine: Unpacked<typeof minesDetails>;
};

const TeamsInformation = () => {
  return (
    <>
      <h1 className={styles.title}>Mines</h1>
      <div className={styles.teamCardsContainer}>
        {minesDetails
          .filter((mine) => mine.shortName !== "hskreward")
          .map((mine) => (
            <TeamsInformationCard key={mine.name} mine={mine} />
          ))}
      </div>
    </>
  );
};

const TeamsInformationCard = ({ mine }: TeamsInformationCardProps) => {
  return (
    <Tilt
      className={styles.parallaxEffect}
      perspective={500}
      glareEnable={true}
      glareMaxOpacity={0.4}
      glareColor={variables.colorPrimary}
      // glareBorderRadius="20px"
      // scale={1.05}
      tiltMaxAngleX={10}
      tiltMaxAngleY={5}
    >
      <div className={styles.innerElement}>
        <img src={mine.mineImages[0]} alt={mine.name} />
        {/* <h1>{mine.name}</h1> */}
        <h2>{mine.name}</h2>
        {mine.description.map((desc, index) => (
          <h3 key={index}>- {desc}</h3>
        ))}
      </div>
    </Tilt>
  );
};

export default TeamsInformation;

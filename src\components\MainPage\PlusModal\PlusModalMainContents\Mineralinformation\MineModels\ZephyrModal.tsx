import { Center, OrbitControls, Html, Environment } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";
import { GLTF } from "three-stdlib";
import * as THREE from "three";
import { Suspense, memo, useState, useEffect } from "react";
import { useModelLoader } from "@/hooks/useModelLoader";

type GLTFResult = GLTF & {
  nodes: {
    ["Material_001-material"]: THREE.Mesh;
    ["Material_001-material_1"]: THREE.Mesh;
    ["Object_3"]: THREE.Mesh;
  };
  materials: {
    material_0: THREE.LineBasicMaterial;
    Material_001: THREE.MeshBasicMaterial;
  };
};

const ZephyrModal = () => {
  const Loader = () => <Html center>loading modal</Html>;

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Canvas
        shadows
        camera={{ position: [0, 0, 170], fov: 25 }}
        gl={{ preserveDrawingBuffer: true }}
      >
        <ambientLight intensity={1} color="white" />
        <directionalLight color="white" position={[0, 0, -20]} intensity={1} />
        <directionalLight color="white" position={[0, 0, 20]} intensity={1} />
        <directionalLight color="white" position={[20, 0, 0]} intensity={1} />
        <directionalLight color="white" position={[-20, 0, 0]} intensity={1} />
        <directionalLight color="white" position={[0, 20, 0]} intensity={1} />
        <directionalLight color="white" position={[0, -20, 0]} intensity={1} />
        <Center>
          <Suspense fallback={<Loader />}>
            <Zephyr />
          </Suspense>
        </Center>
        <OrbitControls
          autoRotate
          autoRotateSpeed={0.5}
          makeDefault
          enableZoom={false}
          minPolarAngle={Math.PI / 2.2}
          maxPolarAngle={Math.PI / 2.2}
          // minAzimuthAngle={-Math.PI / 2.1}
          // maxAzimuthAngle={Math.PI / 2.3}
        />
      </Canvas>
    </div>
  );
};

function Zephyr(props: JSX.IntrinsicElements["group"]) {
  const { model } = useModelLoader("ZEPHYR");
  const { nodes, materials } = model;
  return (
    <group {...props} dispose={null}>
      <group rotation={[-Math.PI / 2, 0, 0]}>
        <group position={[-0.141, 6.274, 1.08]}>
          <lineSegments
            geometry={nodes.Object_3.geometry}
            material={materials.material_0}
          />
          <mesh
            castShadow
            receiveShadow
            geometry={nodes["Material_001-material"].geometry}
            material={materials.Material_001}
          />
          <mesh
            castShadow
            receiveShadow
            geometry={nodes["Material_001-material_1"].geometry}
            material={materials.Material_001}
          />
        </group>
      </group>
    </group>
  );
}

export default memo(ZephyrModal);

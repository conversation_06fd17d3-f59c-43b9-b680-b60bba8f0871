import CommoditiesStat from "./CommoditiesStat/CommoditiesStat";
import styles from "./PurchaseHistory.module.scss";
import PurchasedMine from "./PurchasedMine/PurchasedMine";
import PurchasedPortion from "./PurchasedPortion/PurchasedPortion";
import usePurchaseHistory from "@/components/MainPage/LaunchSection/PurchaseHistory/hooks/usePurchaseHistory";
// import HskPriceChart from "@/components/UI/Charts/HskPriceChart/HskPriceChart";

const PurchaseHistory = () => {
  const { areaBought } = usePurchaseHistory();

  return (
    <div className={styles.container}>
      <div className={styles.historyWrapper}>
        {areaBought.map((area, index) => {
          if (area.isPurchased) {
            return (
              <div key={index} className={styles.historyContainer}>
                <PurchasedMine mineDetails={area.mineDetails} />
                {/* <PurchasedPortion tokenDetails={area.tokenDetails} /> */}
                <PurchasedPortion
                  tokenDetails={area.tokenDetails}
                  mineDetails={area.mineDetails}
                />
                <CommoditiesStat mineDetails={area.mineDetails} />
              </div>
            );
          }
        })}
      </div>
    </div>
  );
};

export default PurchaseHistory;

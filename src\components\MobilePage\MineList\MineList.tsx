import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./MineList.module.scss";
import { MotionStyle } from "framer-motion";
import MineListContent from "./MineListContent";

interface MineListProps {
  delay?: number;
  isButtonClicked: boolean;
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
  showMineNavigation: boolean;
  handleMineClick: any;
}

const MineList = ({
  delay,
  showMineNavigation,
  handleMineClick,
}: MineListProps) => {
  const [hasInitialAnimationDone, setHasInitialAnimationDone] = useState(false);
  const [isShowMoreButtonClicked, setIsShowMoreButtonClicked] = useState(false);

  const newsContentsConfigs = {
    isShowMoreButtonClicked,
    showMineNavigation,
    // setShowMineNavigation,
    handleMineClick,
  };

  useEffect(() => {
    setTimeout(
      () => {
        setHasInitialAnimationDone(true);
      },
      delay && (delay + 2) * 1000,
    );
  }, []);

  return (
    <>
      <div className={styles.wrapper}>
        <MineListContent {...newsContentsConfigs} />
      </div>
    </>
  );
};

export default MineList;

import { mineCardStore } from "@/stores/mineCard";
import styles from "./MineralInformation.module.scss";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import locationIconGrey from "@/assets/icons/plusModal/locationIconGrey.png";
import { memo, useEffect, useState } from "react";
import PurchaseMineCard from "./PurchaseMineCard/PurchaseMineCard";
import { tradeModalStore } from "@/stores/tradeModal";
import TradeDashboard from "./TradeDashboard/TradeDashboard";

const Mineralinformation = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const tradeModalSnapshot = useSnapshot(tradeModalStore);
  const filterMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const [selectedMine, setSelectedMine] = useState(filterMine);

  useEffect(() => {
    setSelectedMine(filterMine);
  }, [mineCardSnapshot.selectedMine]);

  return (
    <div className={styles.container}>
      <div className={styles.scrollArea}>
        <div className={styles.tradeDashboardScrollArea}>
          <TradeDashboard />
        </div>
      </div>

      <div className={styles.mainTitle}>
        <div className={styles.topTitle}>
          <h1 className={styles.mineral}>{selectedMine.mineMineral}</h1>
          <img src={locationIconGrey.src} alt="location icon grey" />
          <h1 className={styles.location}>{selectedMine.mineLocation}</h1>
        </div>
        <h1 className={styles.bottomTitle}>{selectedMine.name}</h1>
      </div>
    </div>
  );
};

export default memo(Mineralinformation);

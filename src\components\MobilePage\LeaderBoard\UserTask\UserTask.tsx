import React, { useEffect, useState } from "react";
import styles from "./UserTask.module.scss";
import { TUserTask } from "@/stores/pointsData";
import table from "@/assets/images/table.jpg";
import Image from "next/image";

const UserTask = ({ userTasks }: { userTasks: TUserTask }) => {
  // const [tasks, setTasks] = useState<TUserTask[]>([]);

  // useEffect(() => {
  //   setTasks([...tasks]);
  //   console.log("UserTasks updated:", userTasks);

  // }, [tasks, userTasks]);

  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <Image src={table} alt="table" className={styles.table} />
        <div>Completed tasks</div>
      </div>

      <div className={styles.taskList}>
        {userTasks.map((task, index) => (
          <div key={index} className={styles.taskRow}>
            <div className={styles.sourceCell}>
              <div className={styles.label}>Source</div>
              <div className={styles.value}>{task.sourcePlatform}</div>
            </div>
            <div className={styles.descriptionCell}>
              <div className={styles.label}>Description</div>
              <div className={styles.value}>{task.taskName}</div>
            </div>
            <div className={styles.pointsCell}>
              <div className={styles.label}>Points</div>
              <div className={styles.value}>{task.points}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UserTask;

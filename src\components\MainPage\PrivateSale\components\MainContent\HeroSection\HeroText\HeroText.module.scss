@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  color: white;
  padding: 2rem 5rem 5rem 2rem;
}

.content {
  max-width: 1200px;
}

.joinText {
  font-size: 2rem;
  font-weight: 400;
}

.mainTitle {
  margin-top: -1rem;
  font-size: 3rem;
  font-weight: 700;
}

.deadline {
  font-size: 1.5rem;
  font-weight: 100;
  margin: 0;
  margin-bottom: 2rem;
  letter-spacing: 1px;
}

.tokenInfo {
  margin-top: 2rem;
}

.tokenEquation {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 0.5rem;

  .priceGroup {
    @include col-center;

    .priceToken {
      font-size: 6rem;
      font-weight: 700;
    }

    .priceUnit {
      margin-top: -2rem;
      font-size: 2rem;
      font-weight: 100;
      opacity: 0.7;
    }
  }
}

.tokenValue {
  font-size: 5rem;
  font-weight: 700;
}

.tokenSymbol {
  font-size: 3rem;
  opacity: 0.7;
}

.tokenEquivalent {
  position: relative;
  height: 5rem;
}

.tokenNumber {
  font-size: 5rem;
  font-weight: 700;
  position: absolute;

  &:first-child {
    color: white;
    z-index: 2;
  }

  &:last-child {
    color: rgba(255, 255, 255, 0.3);
    transform: translateY(20px);
    z-index: 1;
  }
}

.tokenName {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: right;
  margin: 0;
  letter-spacing: 1px;
}

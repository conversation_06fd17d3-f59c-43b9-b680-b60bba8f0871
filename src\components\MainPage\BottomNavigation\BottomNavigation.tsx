import { bottomUp } from "@/animations/animations";
import styles from "./BottomNavigation.module.scss";
import { AnimatePresence, motion } from "framer-motion";
import BottomContents from "./BottomContents/BottomContents";

const BottomNavigation = ({ delay = 1 }) => {
  return (
    <div className={styles.container}>
      <motion.div
        className={styles.trapezoid}
        variants={bottomUp(2, delay, 200)}
        initial="hidden"
        animate="visible"
      >
        <BottomContents />
      </motion.div>
    </div>
  );
};

export default BottomNavigation;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  flex: 1;
  background: linear-gradient(to bottom, #00a2a9, #007a80);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  &:hover {
    background: linear-gradient(to bottom, #00b2b9, #008a90);
    // transform: translateY(-2px);
  }

  &:active {
    // transform: translateY(1px);
  }
}

.content {
  text-align: center;
  color: white;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
}

.subtitle {
  font-size: 1rem;
  margin: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
}

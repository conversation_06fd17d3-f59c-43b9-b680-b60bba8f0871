import { creator<PERSON><PERSON>bi } from "@/constants/abis/creatorNF<PERSON>bi";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError } from "@/lib/notification";
import { SetStateAction, useEffect, useState } from "react";
import { useAccount, useContractReads, useNetwork } from "wagmi";

type ContractList = {
  address: `0x${string}`;
  abi: typeof creatorNFTAbi;
  functionName: string;
  args: BigInt[];
};

export const useAreaBought = () => {
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [creatorNFTAddress, setCreatorNFTAddress] = useState<`0x${string}`[]>(
    [],
  );
  const [contractListMt, setContractListMt] = useState<ContractList[]>([]);
  const [contractListMatsa, setContractListMatsa] = useState<ContractList[]>(
    [],
  );
  const [contractListZephyr, setContractListZephyr] = useState<ContractList[]>(
    [],
  );

  const [mineOwnedStatus, setMineOwnedStatus] = useState<boolean[]>([]);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();

  const { data: nftSold } = useContractReads({
    contracts:
      chain && creatorNFTAddress.length > 0
        ? [
            {
              address: creatorNFTAddress[0],
              abi: asteroidAddressABI,
              functionName: "tokenSoldAmounts",
              args: [BigInt(networkConfigs[chain.id].assetIds.mt)],
            } as const,
            {
              address: creatorNFTAddress[0],
              abi: asteroidAddressABI,
              functionName: "tokenSoldAmounts",
              args: [BigInt(networkConfigs[chain.id].assetIds.matsa)],
            } as const,
            {
              address: creatorNFTAddress[0],
              abi: asteroidAddressABI,
              functionName: "tokenSoldAmounts",
              args: [BigInt(networkConfigs[chain.id].assetIds.zephyr)],
            } as const,
          ]
        : undefined,
    watch: true,
    enabled: !chain?.unsupported && isConnected && creatorNFTAddress.length > 0,
  });

  const { data: buyersMt } = useContractReads({
    contracts: contractListMt,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress[0] &&
      contractListMt.length > 0,
  });
  const { data: buyersMatsa } = useContractReads({
    contracts: contractListMatsa,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress[1] &&
      contractListMatsa.length > 0,
  });
  const { data: buyersZephyr } = useContractReads({
    contracts: contractListZephyr,
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      creatorNFTAddress[2] &&
      contractListZephyr.length > 0,
  });

  const getAllMinesAddress = (chainId: number) => [
    networkConfigs[chainId].asteroidAddress,
  ];

  const createContractList = (
    nftSold: bigint,
    address: `0x${string}`,
    setContractList: (value: SetStateAction<ContractList[]>) => void,
  ) => {
    const tempContractList: ContractList[] = [];
    [...Array(Number(nftSold))].map((_, index) => {
      tempContractList.push({
        address,
        abi: creatorNFTAbi,
        functionName: "ownerOf",
        args: [BigInt(index)],
      });
    });
    setContractList(tempContractList);
  };

  useEffect(() => {
    if (chain?.unsupported) {
      setLaunchPadAddress("0x");
      setCreatorNFTAddress([]);
    } else {
      if (chain) {
        setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
        setCreatorNFTAddress(getAllMinesAddress(chain.id));
      }
    }
  }, [chain]);

  useEffect(() => {
    if (nftSold && creatorNFTAddress.length > 0) {
      nftSold.forEach((nft, index) => {
        if (nft.status === "success") {
          const tokenAmount = nft.result as bigint;
          switch (index) {
            case 0: {
              createContractList(
                tokenAmount,
                creatorNFTAddress[0],
                setContractListMt,
              );
              break;
            }
            case 1: {
              createContractList(
                tokenAmount,
                creatorNFTAddress[0],
                setContractListMatsa,
              );
              break;
            }
            case 2: {
              createContractList(
                tokenAmount,
                creatorNFTAddress[0],
                setContractListZephyr,
              );
              break;
            }
          }
        }
      });
    }
  }, [nftSold, creatorNFTAddress]);

  useEffect(() => {
    if (buyersMt && buyersMatsa && buyersZephyr && address) {
      const tempBuyersMt = buyersMt.map((buyer) => {
        if (buyer.status === "success") {
          return buyer.result;
        }
      });
      const tempBuyersMatsa = buyersMatsa.map((buyer) => {
        if (buyer.status === "success") {
          return buyer.result;
        }
      });
      const tempBuyersZephyr = buyersZephyr.map((buyer) => {
        if (buyer.status === "success") {
          return buyer.result;
        }
      });
      const tempStatus = [
        tempBuyersMt.includes(address),
        tempBuyersMatsa.includes(address),
        tempBuyersZephyr.includes(address),
      ];

      setMineOwnedStatus(tempStatus);
    }
  }, [address, buyersMt, buyersMatsa, buyersZephyr]);

  return { mineOwnedStatus };
};

"use client";
import React, { useEffect, useState } from "react";
import styles from "./Statistics.module.scss";
import Header from "./Header/Header";
import background from "./assets/pointSystemBg.webp";
import Leaderboard from "./Leaderboard/Leaderboard";
import Summary from "./Summary/Summary";
import Link from "next/link";
import backIcon from "./assets/backIcon.png";
import { useAccount, useNetwork } from "wagmi";
import { showError } from "@/lib/notification";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import { navbarButtonDetails } from "../MainPage/TopNavigation/NavbarContent/NavbarContent";
import { useRouter } from "next/navigation";
import { pointsDataStore, PointsData, TUserTask } from "@/stores/pointsData";
import UserTask from "./UserTask/UserTask";
import DailyCheckin from "./DailyCheckin/DailyCheckin";

const Statistics = () => {
  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();
  const router = useRouter();
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const { pointsData, userTasks } = useSnapshot(pointsDataStore);

  const switchToMainPage = () => {
    navbarButtonSnapshot.setSelectedButton(navbarButtonDetails[0].title);
  };
  useEffect(() => {
    if (!isConnected) {
      showError("Please Connect Wallet");
      switchToMainPage();
      return;
    }
    if (chain?.unsupported) {
      // showError("Please Switch Network");
      switchToMainPage();
      return;
    }
    const fetchData = async () => {
      if (isConnected && address) {
        await Promise.all([
          pointsDataStore.fetchPointsData(address),
          pointsDataStore.fetchUserTasks(address),
        ]);
      }
    };

    fetchData();
  }, [isConnected, chain, address]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     if (
  //       !pointsDataStore.pointsData ||
  //       pointsDataStore.pointsData.length === 0
  //     ) {
  //       await pointsDataStore.fetchPointsData();
  //     }
  //     setPointsData(pointsDataStore.pointsData);
  //   };

  //   fetchData();
  // }, []);

  return (
    <>
      {isConnected && !chain?.unsupported ? (
        <div className={styles.container}>
          <div className={styles.mainContent}>
            <img
              src={background.src}
              alt="background image"
              className={styles.backgroundImage}
            />
            <div className={styles.mainContentContainer}>
              {/* <span onClick={switchToMainPage}>
                <img
                  src={backIcon.src}
                  alt="back icon"
                  width={44}
                  height={44}
                  style={{ marginTop: "2rem" }}
                />
              </span> */}
              <Header />
              <DailyCheckin />
              <Summary pointsData={pointsData as PointsData} />
              {userTasks.length > 0 ? (
                <UserTask userTasks={userTasks as TUserTask} />
              ) : null}
              <Leaderboard pointsData={pointsData as PointsData} />
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
};

export default Statistics;

//@ts-nocheck
import { Dispatch, SetStateAction } from "react";
import Countdown from "react-countdown";

const CountdownTimer = ({
  remainingTime,
  setIsCheckinAvaliable,
}: {
  remainingTime: number;
  setIsCheckinAvaliable: Dispatch<SetStateAction<boolean>>;
}) => {
  // function to check if the time is single digit, if it's add 0 to the current time
  const formatTime = (hours: number, minutes: number, seconds: number) => {
    if (hours < 10) {
      hours = "0" + hours;
    }
    if (minutes < 10) {
      minutes = "0" + minutes;
    }
    if (seconds < 10) {
      seconds = "0" + seconds;
    }
    return `${hours}:${minutes}:${seconds}`;
  };

  const renderer = ({ hours, minutes, seconds, completed }) => {
    if (completed) {
      setIsCheckinAvaliable(true);
    } else {
      return <span>{formatTime(hours, minutes, seconds)}</span>;
    }
  };
  return (
    <>
      {remainingTime !== 0 ? (
        <Countdown
          date={Date.now() + remainingTime * 1000}
          renderer={renderer}
        />
      ) : null}
    </>
  );
};

export default CountdownTimer;

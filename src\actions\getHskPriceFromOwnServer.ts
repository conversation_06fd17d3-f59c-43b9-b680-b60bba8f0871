"use server";
import * as z from "zod";

export const getHskPriceFromOwnServer = async () => {
  let hskPriceData: THskPriceResponse["data"]["data"] = [];
  const url = `https://asteroidx.io/api/v1/market/history/hashkey-ecopoints?days=365&interval=daily`;
  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as THskPriceResponse;
    hskPriceData = data.data.data.map((price) => {
      return {
        ...price,
      };
    });
  }

  return hskPriceData;
};

// types
const DatumSchema = z.object({
  timestamp: z.number(),
  date: z.string(),
  price: z.number(),
});

const DataSchema = z.object({
  coinId: z.string(),
  data: z.array(DatumSchema),
});

const THskPriceResponseSchema = z.object({
  code: z.number(),
  info: z.string(),
  data: DataSchema,
});
export type THskPriceResponse = z.infer<typeof THskPriceResponseSchema>;

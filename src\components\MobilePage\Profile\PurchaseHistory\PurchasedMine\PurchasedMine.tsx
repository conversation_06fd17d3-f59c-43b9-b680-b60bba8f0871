import { MinePurchased } from "../hooks/usePurchaseHistory";
import styles from "./PurchasedMine.module.scss";
import Image from "next/image";

const PurchasedMine = ({
  mineDetails,
}: {
  mineDetails: MinePurchased["mineDetails"];
}) => {
  return (
    // <div className={styles.container}>
    //   <img src={mineDetails.image} alt="mine image" />
    //   <div className={styles.detailsWrapper}>
    //     <div className={styles.details}>
    //       <h5>Mine:</h5>
    //       <p>{mineDetails.name}</p>
    //     </div>
    //     <div className={styles.details}>
    //       <h5>Mineral:</h5>
    //       <p>{mineDetails.mineral}</p>
    //     </div>
    //     <div className={styles.details}>
    //       <h5>Price:</h5>
    //       <p>${mineDetails.price}</p>
    //     </div>
    //   </div>
    // </div>
    <div className={styles.bodyWrapper}>
      <div className={styles.titleContainer}>
        <div className={styles.subtitle}>
          <h1 className={styles.title}>{mineDetails.name}</h1>
        </div>
      </div>
      <div className={styles.imgContainer}>
        <div className={styles.imageFrame}>
          {/* <img src={mines.mineImages[0]} alt={mines.name} /> */}
          <Image
            src={mineDetails.image}
            alt={mineDetails.name}
            width={500}
            height={300}
          />
        </div>
        <div className={styles.logoWrapper}>
          <div className={styles.detailsWrapper}>
            <div className={styles.details}>
              <h5>Mineral:</h5>
              <p>{mineDetails.mineral}</p>
            </div>
            <div className={styles.details}>
              <h5>Price:</h5>
              <p>${mineDetails.price}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchasedMine;

import { TypeAnimation } from "react-type-animation";
import styles from "./MainTitle.module.scss";
import { MutableRefObject, useLayoutEffect } from "react";
import { CameraControls, useProgress, useScroll } from "@react-three/drei";
import { useSnapshot } from "valtio";
import { universeAnimationStore } from "@/stores/universeAnimation";
import { useInView } from "react-intersection-observer";
import MinesInformation from "../MinesInformation/MinesInformation";
import { useRouter } from "next/navigation";
import { useFrame, useThree } from "@react-three/fiber";
import { hoverModelStore } from "@/stores/hoverModel";
import { useMediaQuery } from "usehooks-ts";

type MainTitleProps = {
  cameraControlsRef: MutableRefObject<CameraControls | undefined>;
  minesInformationInView: ReturnType<typeof useInView>["inView"];
  minesInformationRef: ReturnType<typeof useInView>["ref"];
  replace: ReturnType<typeof useRouter>["replace"];
  numberOfPages: number;
};
const MainTitle = ({
  cameraControlsRef,
  minesInformationInView,
  minesInformationRef,
  replace,
  numberOfPages,
}: MainTitleProps) => {
  const universeAnimationSnapshot = useSnapshot(universeAnimationStore);
  const hoverModelSnapshot = useSnapshot(hoverModelStore);
  const scrollData = useScroll();
  const isMobile = useMediaQuery("(max-width: 1279px)");

  const { progress: modelLoadingProgress } = useProgress();

  useFrame(() => {});

  useLayoutEffect(() => {
    if (modelLoadingProgress === 100) {
      setTimeout(() => {
        cameraControlsRef.current?.setLookAt(0, 0, 30, 0, 0, 0, true);
        universeAnimationSnapshot.setHasInitialAnimationDone(true);
      }, 4500);
    }

    // return () => {
    //   clearTimeout(animationTimer);
    // };
  }, [modelLoadingProgress]);

  return (
    <>
      <div
        className={styles.container}
        style={{
          display: universeAnimationSnapshot.showFinalAnimation
            ? "none"
            : "block",
        }}
      >
        {!universeAnimationSnapshot.hasInitialAnimationDone ? (
          <TypeAnimation
            sequence={["", 1000, "ASTEROIDX", 1000]}
            className={styles.textBeforeAnimation}
            // repeat={Infinity}
            // speed={1}
            cursor={false}
          />
        ) : null}

        {universeAnimationSnapshot.hasInitialAnimationDone ? (
          <>
            <div className={styles.textWrapperAfterAnimation}>
              <div className={styles.text}>asteroidx</div>
              <div className={styles.subText}>
                <h1 className={styles.animatedText}>
                  Physical
                  {isMobile ? <br /> : <>&nbsp;</>}
                  <TypeAnimation
                    sequence={[
                      "mining launchpad",
                      4000,
                      "asset digitization",
                      4000,
                      "pegging",
                      4000,
                      "equity trading",
                      4000,
                    ]}
                    className={styles.animatedTypedText}
                    repeat={Infinity}
                    // speed={1}
                  />
                </h1>
              </div>
            </div>
            {hoverModelSnapshot.hovered ? (
              <h1
                className={styles.tooltip}
                style={{ display: minesInformationInView ? "none" : "block" }}
              >
                Double click to Main Page
              </h1>
            ) : null}
            <h1
              className={styles.scrollDowninstruction}
              style={{ display: minesInformationInView ? "none" : "block" }}
            >
              {isMobile ? "Swipe Down" : "scroll down for details"}
            </h1>
            <div
              className={styles.scrolldownIcon}
              style={{ display: minesInformationInView ? "none" : "block" }}
            />
            <h1
              className={styles.slogan}
              style={{ display: minesInformationInView ? "none" : "block" }}
            >
              {isMobile ? "Mining Launchpad" : "Physical Mining Launchpad"}
            </h1>
          </>
        ) : null}
        <MinesInformation
          minesInformationRef={minesInformationRef}
          cameraControlsRef={cameraControlsRef}
          replace={replace}
          numberOfPages={numberOfPages}
        />
      </div>
    </>
  );
};

export default MainTitle;

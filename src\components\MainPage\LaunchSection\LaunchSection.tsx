import { bottomUpFadeIn, fadeIn } from "@/animations/animations";
import hexagon from "@/assets/images/hexagon.webp";
import styles from "./LaunchSection.module.scss";
import { motion } from "framer-motion";
import UserProfile from "./UserProfile/UserProfile";
import AreaBought from "./AreaBought/AreaBought";
import { useAccount, useNetwork } from "wagmi";
import { useEffect } from "react";
import { showError } from "@/lib/notification";
import PurchaseHistory from "./PurchaseHistory/PurchaseHistory";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import { navbarButtonDetails } from "../TopNavigation/NavbarContent/NavbarContent";

const LaunchSection = () => {
  const { isConnected } = useAccount();
  const { chain } = useNetwork();
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);

  const switchToMainPage = () => {
    navbarButtonSnapshot.setSelectedButton(navbarButtonDetails[0].title);
  };

  useEffect(() => {
    if (!isConnected) {
      showError("Please Connect Wallet");
      switchToMainPage();
      return;
    }
    if (chain?.unsupported) {
      showError("Please Switch Network");
      switchToMainPage();
      return;
    }
  }, [isConnected, chain]);

  return (
    <motion.div
      className={styles.container}
      variants={fadeIn(1)}
      initial="hidden"
      animate="visible"
    >
      {[...Array(5)].map((_, index) => {
        return (
          <motion.img
            key={index}
            className={styles.hexagon}
            src={hexagon.src}
            variants={fadeIn(1, index * 0.7, 0.7)}
          />
        );
      })}
      {isConnected && !chain?.unsupported ? (
        <>
          <div className={styles.userProfileContainer}>
            <UserProfile />
            <PurchaseHistory />
          </div>
          {/* <AreaBought /> */}
        </>
      ) : null}
    </motion.div>
  );
};

export default LaunchSection;

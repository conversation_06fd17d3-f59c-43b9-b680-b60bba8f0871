import styles from "./NavbarButton.module.scss";
import variables from "@/styles/variables.module.scss";
import { navbarButtonDetails } from "../BottomContents/BottomContents";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { navbarButtonStore } from "@/stores/navbarButton";
import { buttonEffect } from "@/animations/animations";
import { useAccount } from "wagmi";

import { connectWalletModalStore } from "@/stores/connectWalletModal";
import { useSnapshot } from "valtio";

type Unpacked<T> = T extends (infer U)[] ? U : T;
type ButtonDetails = {
  buttonDetails: Unpacked<typeof navbarButtonDetails>;
};
type NavbarButtonProps = ButtonDetails & typeof navbarButtonStore;

const NavbarButton = ({
  buttonDetails,
  selectedButton,
  setSelectedButton,
  setIsInitialAnimationDone,
}: NavbarButtonProps) => {
  const [isHovering, setIsHovering] = useState(false);
  const { isConnected } = useAccount();
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);

  useEffect(() => {
    if (buttonDetails.title === navbarButtonDetails[0].title) {
      setSelectedButton(
        typeof buttonDetails.title === "function"
          ? buttonDetails.title(isConnected)
          : buttonDetails.title,
      );
    }
  }, []);

  const isFocus = selectedButton === buttonDetails.title;

  return (
    <motion.div
      whileTap={buttonEffect.tap}
      className={styles.container}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onClick={() => {
        setSelectedButton(
          typeof buttonDetails.title === "function"
            ? buttonDetails.title(isConnected)
            : buttonDetails.title,
        );
        setIsInitialAnimationDone(true);
        if (buttonDetails.title !== navbarButtonDetails[0].title) {
          connectWalletModalSnapshot.setIsOpenConnectWalletModal(false);
        }
      }}
    >
      <div>
        <div className={styles.icons}>
          {buttonDetails.icon(
            isFocus || isHovering
              ? variables.colorPrimary
              : variables.colorPrimaryContrast,
          )}
        </div>

        <div>
          <h1
            className={
              isFocus
                ? styles.titleFocus
                : isHovering
                ? styles.titleHover
                : styles.title
            }
          >
            {typeof buttonDetails.title === "function"
              ? buttonDetails.title(isConnected).toUpperCase()
              : buttonDetails.title.toUpperCase()}
          </h1>
        </div>
      </div>
    </motion.div>
  );
};

export default NavbarButton;

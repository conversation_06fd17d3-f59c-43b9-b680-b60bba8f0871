import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";

const useMaxAmount = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [maxAmountPerListing, setMaxAmountPerListing] = useState(0n);
  const [maxPricePerToken, setMaxPricePerToken] = useState(0n);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setMarketpaceAddress("0x");
    }
  }, [chain]);

  useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "MAX_AMOUNT_PER_LISTING",
    enabled: isConnected && marketpaceAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setMaxAmountPerListing(data);
    },
  });

  useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "MAX_PRICE_PER_TOKEN",
    enabled: isConnected && marketpaceAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setMaxPricePerToken(data);
    },
  });
  return {
    maxAmountPerListing,
    maxPricePerToken,
  };
};

export default useMaxAmount;

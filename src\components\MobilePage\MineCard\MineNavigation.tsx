import { useEffect, useState, useRef } from "react";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import MineCard from "./MineCard";
import styles from "./MineNavigation.module.scss";

import arrowIcon from "@/assets/icons/leftNavigation/arrow.png";
import arrowFrame from "@/assets/icons/leftNavigation/frame.png";

import { mineCardStore } from "@/stores/mineCard";
import { useNetwork } from "wagmi";

import { ignoreMineLists } from "@/constants/ignoreMineList";

interface MineNavigationProps {
  onBack: () => void;
}

const MineNavigation = ({ onBack }: MineNavigationProps) => {
  const { chain } = useNetwork();
  const isSpecialEventMine = (mine: { shortName: string }) =>
    mine.shortName === "hskreward";
  const filteredMines =
  chain && minesDetails.filter((mine) => mine.supportedNetwork.includes(chain.id))
    ? minesDetails.filter(
        (mine) =>
          mine.supportedNetwork.includes(chain.id) &&
          !ignoreMineLists.includes(mine.name) &&
          !isSpecialEventMine(mine)
      )
    : minesDetails.filter(
        (mine) =>
          !ignoreMineLists.includes(mine.name) &&
          !isSpecialEventMine(mine)
      );
  const [currentIndex, setCurrentIndex] = useState(0);
  const totalMines = filteredMines.length;
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const isMounted = useRef(false);

  const updateCurrentIndex = (change: number) => {
    setCurrentIndex(
      (prevIndex) => (prevIndex + change + totalMines) % totalMines,
    );
  };

  const syncSelectedMine = () => {
    mineCardSnapshot.setSelectedMine(filteredMines[currentIndex].name);
  };

  // useEffect(handleMineSelectionUpdate, [mineCardSnapshot.selectedMine]);
  useEffect(() => {
    if (isMounted.current) {
      syncSelectedMine();
    } else {
      isMounted.current = true;
    }
  }, [currentIndex]);

  const indicators = filteredMines.map((_, index) => (
    <span
      key={index}
      className={`${styles.indicator} ${
        index === currentIndex ? styles.active : ""
      }`}
    />
  ));

  return (
    <div className={styles.container}>
      <div
        className={styles.leftButtonWrapper}
        onClick={() => updateCurrentIndex(-1)}
      >
        <img src={arrowFrame.src} />
        <div className={styles.buttonArrow}>
          <img src={arrowIcon.src} alt="Previous" />
        </div>
      </div>

      <div className={styles.wrapper}>
        <MineCard onBack={onBack} />
        <div className={styles.indicatorWrapper}>
          <div className={styles.indicators}>{indicators}</div>
        </div>
      </div>

      <div
        className={styles.rightButtonWrapper}
        onClick={() => updateCurrentIndex(1)}
      >
        <img src={arrowFrame.src} />
        <div className={styles.buttonArrow}>
          <img src={arrowIcon.src} alt="Next" />
        </div>
      </div>
    </div>
  );
};

export default MineNavigation;

import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import {
  useAccount,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import { extractErrorType } from "@/utils/errorHandling";

const usePurchaseAllListingAmount = () => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [isPurchasingListing, setIsPurchasingListing] = useState(false);
  const [value, setValue] = useState(0n);
  const [listingId, setListingId] = useState(0n);
  const [amount, setAmount] = useState(0n);
  const [pricePerToken, setPricePerToken] = useState(0n);
  const [errorMessage, setErrorMessage] = useState("");
  const [refresh, setRefresh] = useState(0);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  useEffect(() => {
    if (pricePerToken && amount) {
      setValue(BigInt(amount) * BigInt(pricePerToken));
    }
    refetchPreparePurchase();
  }, [amount, pricePerToken, refresh]);

  const {
    config: purchaseListingConfig,
    error: prepareError,
    refetch: refetchPreparePurchase,
  } = usePrepareContractWrite({
    address: marketplaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "purchaseListing",
    args: [listingId, amount],
    enabled:
      isConnected &&
      marketplaceAddress !== "0x" &&
      listingId !== 0n &&
      amount !== 0n &&
      refresh >= 0,
    cacheTime: 0,
    staleTime: 0,
    onSuccess: () => {
      setRefresh((prev) => prev + 1);
      // console.log(

      //   "[usePurchaseAllListingAmount] Purchase preparation successful.",
      // );
    },
    onError: (error) => {
      // console.error(
      //   "[usePurchaseAllListingAmount] Error during purchase preparation:",
      //   error,
      // );
    },
  });

  const { write: purchaseListingWrite, data: purchaseListingData } =
    useContractWrite({
      ...purchaseListingConfig,
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
        setIsPurchasingListing(false);
      },
    });
  const { isLoading: isWaitingForPurchaseListing } = useWaitForTransaction({
    confirmations: 5,
    hash: purchaseListingData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Purchased!");
      setIsPurchasingListing(false);
    },
    onError: (error) => {
      showError(
        extractErrorType(error) ||
          (error as BaseError).shortMessage ||
          error.message,
      );
      setIsPurchasingListing(false);
    },
  });

  const purchaseListing = () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (prepareError) {
      showError(extractErrorType(prepareError));
      return;
    }

    if (!purchaseListingWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
        return;
      }
      return;
    }

    setIsPurchasingListing(true);
    try {
      purchaseListingWrite?.();
    } catch (error) {
      setIsPurchasingListing(false);
      showError(extractErrorType(error) || "Failed to purchase listing");
    }
  };

  return {
    isWaitingForPurchaseListing,
    isPurchasingListing,
    purchaseListing,
    listingId,
    amount,
    pricePerToken,
    setAmount,
    setListingId,
    setPricePerToken,
    value,
    refresh,
    setRefresh,
  };
};

export default usePurchaseAllListingAmount;

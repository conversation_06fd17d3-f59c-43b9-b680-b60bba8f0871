@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.leaderboardContainer {
  margin-top: 1rem;
  width: 100%;
  padding-bottom: 1rem;
  // border: 1px solid $color-primary-contrast;
}

// .leaderboard {
//   width: 100%;

//   // margin-top: 2rem;

//   padding: 1rem 1rem 2rem;
//   background: $color-black-transparent;

//   th,
//   td {
//     // padding: 1rem;
//     text-align: left;

//   }

//   th {
//     color: $color-warning;
//     font-size: 1.2rem;
//     font-weight: bold;
//   }

//   .avatar {
//     display: inline-block;
//     width: 20px;
//     height: 20px;
//     border-radius: 50%;
//     background-color: #ccc;
//     margin-right: 10px;
//   }
// }
.title {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: $margin-md;
  font-size: $font-size-md;
  text-transform: uppercase;
  color: $color-primary;
  font-family: "Garalama", sans-serif;
  font-weight: $font-weight-extralight;
  letter-spacing: 1px;
}

.leaderboardList {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.leaderboardRow {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem;
  border-bottom: 1px solid $color-primary-contrast;
}

.positionLabel {
  font-size: $font-size-sm;
  color: $color-primary;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.addressValue {
  font-size: $font-size-sm;
  color: $color-primary;
}

.pointsValue {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  font-size: $font-size-sm;
  color: $color-primary;
  text-align: right;
  font-family: $font-family-poppins;
  // border: 1px solid yellow;
}

/* 为最后一行移除底部边框 */
.leaderboardRow:last-child {
  border-bottom: none;
}

/* 可选：添加hover效果 */
.leaderboardRow:hover {
  background: rgba(0, 188, 212, 0.5);
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 90%;
  // border: 1px solid yellow;
  @include row-between;

  .overview {
    font-size: $font-size-md;
    // font-weight: $font-weight-semibold;
    color: $color-primary;
    text-transform: uppercase;
  }

  .mineralInfo {
    width: 70%;
    @include row-center;
    justify-content: flex-end;
    gap: 2rem;

    div {
      cursor: pointer;
      line-height: 114.5%;
      // opacity: 0.2;

      // &:hover {
      //   text-shadow: 0 0 15px $color-primary;
      //   color: $color-primary;
      //   opacity: 1;
      // }

      h1 {
        font-size: $font-size-2xl;
        text-transform: uppercase;
        color: $color-primary;
        // color: lightgrey;
        font-weight: $font-weight-semibold;
      }
      h2 {
        font-size: $font-size-md;
        text-transform: uppercase;
        color: $color-primary-transparent-contrast;
        font-weight: $font-weight-light;
      }
    }
  }
}

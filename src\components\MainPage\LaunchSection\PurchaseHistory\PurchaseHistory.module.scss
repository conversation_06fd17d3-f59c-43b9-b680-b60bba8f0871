@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;

  .historyWrapper {
    width: 100%;
    height: 92%;
    // border: 1px solid green;
    // margin-bottom: $margin-md;
    overflow: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    .historyContainer {
      width: 100%;
      height: 220px;
      border: 1px solid $color-warning;
      background: $color-black-transparent-medium;
      box-shadow: 0 0 20px $color-primary-contrast;
      margin-bottom: $margin-lg;
      padding: $padding-md;
      @include row-center;
    }
  }
}

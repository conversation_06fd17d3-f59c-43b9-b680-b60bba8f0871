@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  position: relative;
  padding: 1rem;
  margin-bottom: 1rem;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary-transparent-contrast;
    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }

  .innerContainer {
    width: 100%;
    position: relative;
    padding: 1rem;

    .mineInfo {
      .header {
        margin-bottom: 1rem;

        .titleSection {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 1rem;

          h2 {
            color: $color-primary;
            font-size: $font-size-xl;
            font-weight: $font-weight-bold;
          }

          .price {
            color: $color-primary;
            font-size: $font-size-md;
            font-weight: $font-weight-medium;
          }
        }

        .location {
          .city {
            color: $color-primary;
            font-size: $font-size-sm;
            // display: block;
            margin-bottom: 0.5rem;
          }

          h3 {
            color: white;
            font-size: $font-size-lg;
            font-weight: $font-weight-medium;
          }
        }
      }

      .details {
        .detailItem {
          margin-bottom: 1rem;

          .label {
            color: $color-primary;
            font-size: $font-size-xs;
            display: block;
            // margin-bottom: 0.25rem;
            letter-spacing: 0.05em;
          }

          .value {
            color: white;
            font-size: $font-size-sm;
            font-weight: $font-weight-light;
          }
        }
      }
    }

    .buyOverlay {
      line-height: 150%;
      position: absolute;
      width: 100%;
      height: 90px;
      padding: 1rem;
      left: 0;
      bottom: 0;
      background: transparent;
      background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 1) 0%,
        rgba(0, 0, 0, 0.6) 100%
      );

      h1 {
        text-align: center;
        font-size: $font-size-xl;
        font-weight: $font-weight-extrabold;
      }

      h3 {
        text-align: center;
        font-size: $font-size-md;
        font-weight: $font-weight-extralight;
      }
      img {
        width: 100%;
        object-position: center;
        object-fit: cover;
        cursor: pointer;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary-transparent-contrast;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%,
        0 0,
        1px 1px,
        1px calc(100% - 1px),
        calc(100% - 1px) calc(100% - 1px),
        calc(100% - 1px) calc(32px + 0.41px),
        calc(100% - 32px - 0.41px) 1px,
        1px 1px
      );
    }
  }
}

import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export const universeAnimationStore = proxy({
  hasInitialAnimationDone: false,
  setHasInitialAnimationDone: (status: boolean) => {
    universeAnimationStore.hasInitialAnimationDone = status;
  },
  showFinalAnimation: false,
  setShowFinalAnimation: (status: boolean) => {
    universeAnimationStore.showFinalAnimation = status;
  },
});

devtools(universeAnimationStore, {
  name: "universeAnimationStore",
  enabled: false,
});

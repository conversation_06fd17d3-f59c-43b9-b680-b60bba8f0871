import React, { useEffect, useLayoutEffect } from "react";
import styles from "./Leaderboard.module.scss";
import avatarIcon from "../assets/pointSystemIcon.png";
import Link from "next/link";
import { pointSystemData } from "@/constants/pointSystem/final";
import { PointsData, pointsDataStore } from "@/stores/pointsData";
import { useSnapshot } from "valtio";
import moment from "moment";

interface LeaderboardProps {
  pointsData: PointsData;
}

const leaderboardData = pointSystemData.slice(0, 10);
const trimWalletAddress = (address: string) => {
  return address.slice(0, 6) + "..." + address.slice(-4);
};
const Leaderboard: React.FC<LeaderboardProps> = ({ pointsData }) => {
  const { totalUsers } = useSnapshot(pointsDataStore);

  // useLayoutEffect(() => {
  //   pointsDataStore.fetchTotalUsers();
  // }, []);
  return (
    <div className={styles.leaderboardContainer}>
      <h2 className={styles.title}>Leaderboard</h2>
      <h3 className={styles.lastUpdate}>
        Last Update: {moment().subtract(4, "hours").calendar()}
      </h3>
      {/* <h3 className={styles.totalUsers}>
        Total Active Users: <span>{totalUsers.toLocaleString("en-US")}</span>
      </h3> */}
      {/* <Link href={"/main"} replace className={styles.homeButton}>
        HOME
      </Link> */}
      <table className={styles.leaderboard}>
        <thead>
          <tr>
            <th>Position</th>
            <th>Address</th>
            <th>Points</th>
          </tr>
        </thead>
        <tbody>
          {pointsData.pointsItems.map((entry, index) => (
            <tr key={index}>
              <td>{index + 1}</td>
              <td>
                <span style={{ display: "flex", alignItems: "center" }}>
                  <span className={styles.avatar}>
                    <img
                      src={avatarIcon.src}
                      alt="avatar icon"
                      width={"100%"}
                    />
                  </span>
                  {trimWalletAddress(entry.userAddress)}
                </span>
              </td>
              <td>{entry.totalPoints}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Leaderboard;

"use server";
import requestBody from "@/components/MainPage/RegisterMine/requestBody.json";
type TRequestBody = typeof requestBody;

export const uploadMineFormData = async (body: TRequestBody) => {
  const url = `https://asteroidx.io/api/mine/detail/add`;
  await fetch(url, {
    cache: "no-cache",
    method: "POST",
    body: JSON.stringify(body),
    headers: {
      "Content-Type": "application/json",
    },
  });
};

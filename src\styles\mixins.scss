@mixin row-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin row-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

@mixin col-between {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

@mixin titleText() {
  // animation: background-pan 4s linear infinite;
  background: linear-gradient(
    to right,
    $color-black-transparent-dark,
    $color-black-transparent-dark
  );
  background-clip: text;
  background-size: 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: $font-weight-bold;
  font-size: $font-size-6xl;
  text-align: center;
  -webkit-text-stroke: 4px $color-primary;
  text-shadow: 0 0 20px $color-primary-transparent-contrast;
  // 0 0 30px $color-primary-transparent-contrast,
  // 0 0 40px $color-primary-transparent-contrast,
  // 0 0 50px $color-primary-transparent-contrast,
  // 0 0 60px $color-primary-transparent-contrast;
  letter-spacing: 0.5rem;
  text-transform: uppercase;
}

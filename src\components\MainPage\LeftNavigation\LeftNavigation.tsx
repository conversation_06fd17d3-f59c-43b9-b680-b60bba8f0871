import { useState } from "react";
import styles from "./LeftNavigation.module.scss";
import NavigationButton from "./NavigationButton/NavigationButton";
import Newsletter from "./Newsletter/Newsletter";

const LeftNavigation = ({ delay = 2 }) => {
  const [isButtonClicked, setIsButtonClicked] = useState(false);

  const navigationButtonProps = {
    delay,
    isButtonClicked,
    setIsButtonClicked,
  };

  const newsletterProps = {
    delay: (delay += 2),
    isButtonClicked,
    setIsButtonClicked,
  };

  return (
    <div className={styles.container}>
      <NavigationButton {...navigationButtonProps} />
      <Newsletter {...newsletterProps} />
    </div>
  );
};

export default LeftNavigation;

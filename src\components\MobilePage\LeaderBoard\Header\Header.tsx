import React from "react";
import styles from "./Header.module.scss";
import PointSystemIcon from "@/sampleAssets/images/PointSystemIcon.png";
import Image from "next/image";

const Header = () => {
  return (
    <>
      <div className={styles.container}>
        <Image
          src={PointSystemIcon}
          alt="points system icon"
          width={100}
          height={100}
        />
        <div className={styles.description}>
          {/* <h1>Claim Your Asteroid X Airdrop</h1> */}
          <div className={styles.rewardPool}>
            <b>
              Current Reward Pool:{"  "}
              <h1 className={styles.rewardAmount}>
                $100,000 USDT + Asteroid X Tokens
              </h1>
            </b>
          </div>
          <h3>
            The Asteroid X Points System is a transparent and engaging mechanism
            designed to reward users&apos; active participation and
            contributions, offering proportional rewards based on points
            accumulated through various platform activities.{" "}
            <a
              href="https://medium.com/@ASTEROID_X/asteroid-x-points-system-guide-b746bf323db3"
              target="_blank"
              className={styles.guide}
            >
              <b>GUIDE</b>
            </a>
          </h3>
        </div>
        {/* <img
          src={asteroidToken.src}
          alt="asteroid icon"
          className={styles.icon}
        /> */}
      </div>
    </>
  );
};

export default Header;

import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./TransactionData.module.scss";
import { AnimatePresence, MotionStyle, motion } from "framer-motion";
import { slideIn } from "@/animations/animations";
import ShowMoreButton from "../../LeftNavigation/Newsletter/ShowMoreButton/ShowMoreButton";
import TransactionDetails from "./TransactionDetails/TransactionDetails";

interface TransactionDataProps {
  delay?: number;
  isButtonClicked: boolean;
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
}

const TransactionData = ({ isButtonClicked, delay }: TransactionDataProps) => {
  const [hasInitialAnimationDone, setHasInitialAnimationDone] = useState(false);
  const [isShowMoreButtonClicked, setIsShowMoreButtonClicked] = useState(false);

  const showMoreButtonConfigs = {
    isShowMoreButtonClicked,
    setIsShowMoreButtonClicked,
  };

  const transactionDetailsConfigs = {
    isShowMoreButtonClicked,
  };

  const borderExpandEffect: MotionStyle = {
    height: isShowMoreButtonClicked ? "617px" : "500px",
    transition: "height 0.25s linear",
  };

  useEffect(() => {
    setTimeout(
      () => {
        setHasInitialAnimationDone(true);
      },
      delay && delay * 1000,
    );
  }, []);

  return (
    <>
      {!isButtonClicked ? (
        <motion.div
          style={borderExpandEffect}
          className={styles.wrapper}
          variants={slideIn(500, 0, 0.5, hasInitialAnimationDone ? 0 : delay)}
          initial="hidden"
          animate="visible"
        >
          <ShowMoreButton {...showMoreButtonConfigs} />
          <TransactionDetails {...transactionDetailsConfigs} />
        </motion.div>
      ) : (
        <motion.div
          style={borderExpandEffect}
          className={styles.wrapper}
          variants={slideIn(0, 500, 0.5, 0)}
          initial="hidden"
          animate="visible"
        />
      )}
    </>
  );
};

export default TransactionData;

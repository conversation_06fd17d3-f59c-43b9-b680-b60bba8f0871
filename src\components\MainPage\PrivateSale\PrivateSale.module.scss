@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  @include row-center;

  .modalFrame {
    margin-top: 2rem;
    width: 90%;
    height: 80%;
    position: relative;
    background: $color-black-transparent;
    clip-path: polygon(
      0 0,
      calc(100% - 80px) 0,
      100% 80px,
      100% calc(100% - 48px),
      calc(100% - 48px) 100%,
      48px 100%,
      0 calc(100% - 48px)
    );

    .comingSoonOverlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;
      border-radius: inherit;

      span {
        color: $color-primary;
        font-weight: bold;
        font-size: 4rem;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
    }

    .scrollArea {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .mainContent {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        black 0%,
        $color-primary-contrast 100%
      );
      position: absolute;

      .backgroundLayer1 {
        position: absolute;
        top: 0;
        right: 0;
        width: 937px;
        height: 1080px;
        background-image: url("./assets/backgroundImage1.png");
        background-size: cover;
        background-position: center;
        z-index: 0;
      }

      .backgroundLayer2 {
        position: absolute;
        top: 0;
        right: 0;
        width: 856px;
        height: 945px;
        background-image: url("./assets/backgroundImage3.png");
        background-size: cover;
        background-position: center;
        z-index: 1;
      }

      .spotlightBeam {
        position: absolute;
        top: 0;
        right: 0;
        width: 200%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0) 30%,
          rgba(0, 200, 255, 0.1) 45%,
          rgba(255, 255, 255, 0.4) 50%,
          rgba(0, 200, 255, 0.1) 55%,
          rgba(0, 0, 0, 0) 70%
        );
        transform: translateX(47%) rotate(0deg) skewX(0deg);
        z-index: 1;
        pointer-events: none;
        mix-blend-mode: screen;
        opacity: 0.8;
      }

      .mainLayout {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
        @include row-center;
      }

      &::before {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(0, 200, 255, 0) 0%,
          rgba(0, 200, 255, 0) 50%,
          rgba(255, 255, 255, 0) 85%,
          rgba(255, 255, 255, 0) 100%
        );
        pointer-events: none;
        z-index: 0;
      }
    }

    &::before {
      content: "";
      position: absolute;
      z-index: 1;
      inset: 0;
      background: $color-primary;
      clip-path: polygon(
        0 0,
        calc(100% - 81px) 0,
        100% 81px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px),
        0 0,
        1px 1px,
        1px calc(100% - 48px - 0.41px),
        calc(48px + 0.41px) calc(100% - 1px),
        calc(100% - 48px - 0.41px) calc(100% - 1px),
        calc(100% - 1px) calc(100% - 48px - 0.41px),
        calc(100% - 1px) calc(48px + 0.41px),
        calc(100% - 48px - 0.41px) 1px,
        1px 1px
      );
    }
  }
}

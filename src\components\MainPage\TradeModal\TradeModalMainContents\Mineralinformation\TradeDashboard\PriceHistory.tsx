import { useState } from "react";
import PriceHistoryGraph from "./PriceHistoryGraph";
import styles from "./TradeDashboard.module.scss";

const PriceHistory = () => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div className={styles.priceHistory}>
      <div
        className={styles.headerContainer}
        onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className={styles.mainHeader}>PRICE HISTORY</h2>
        <span className={styles.toggleIcon}>{isOpen ? "-" : "+"}</span>
      </div>
      {isOpen && (
        <div className={styles.graph}>
          <PriceHistoryGraph />
        </div>
      )}
    </div>
  );
};

export default PriceHistory;

import React from "react";
import styles from "./Purchase.module.scss"
import PurchaseContent from "./PurchaseContent/PurchaseContent";
import PurchaseHeader from "./PurchaseContent/PurchaseHeader";
import { minesDetails } from "@/constants/mineDetails";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;

export interface PurchaseProps {
  information: Unpacked<typeof minesDetails>;
}

const Purchase = () => {
  return (
    <div className={styles.container}>
      <div className={styles.cardwrapper}>
        <div className={styles.cardHeader}>
          <PurchaseHeader />
          <PurchaseContent
            // key={mine.index} // 假设每个mine有一个唯一的id属性作为key
          />

        </div>
        {/* <MineFooter /> */}
      </div>
    </div>
  );
};

export default Purchase;

import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./Profile.module.scss";
import ProfileContent from "./ProfileContent";

interface ProfileProps {
  showPointSystem: boolean;
  setShowPointSystem: Dispatch<SetStateAction<boolean>>;
}

const Profile = ({ showPointSystem, setShowPointSystem }: ProfileProps) => {

  return (
    <>
      <div className={styles.wrapper}>
        <ProfileContent
          showPointSystem={showPointSystem}
          setShowPointSystem={setShowPointSystem}
        />
      </div>
    </>
  );
};

export default Profile;
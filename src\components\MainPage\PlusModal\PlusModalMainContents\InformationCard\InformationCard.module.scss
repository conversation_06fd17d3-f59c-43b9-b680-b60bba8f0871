@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$avatar-size: 66px;

@mixin avatar {
  width: $avatar-size;
  height: $avatar-size;
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-sm;
  @include row-center;
  // cursor: pointer;

  .letter {
    font-size: $font-size-2xl;
    font-weight: $font-weight-light;
    color: #00f1ff;
    text-transform: uppercase;
  }
}

.container {
  cursor: pointer;
  width: 100%;
  // height: 75px;
  position: relative;
  // background: $color-black-transparent-dark;
  // backdrop-filter: blur(30px);
  padding: $padding-md;
  margin-bottom: $margin-md;
  // clip-path: polygon(0 0, calc(100% - 32px) 0, 100% 32px, 100% 100%, 0 100%);

  .contentWrapper {
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-md;

    .avatar {
      @include avatar();
    }

    .infoWrapper {
      @include col-center;
      align-items: flex-start;
      line-height: 127.5%;
      gap: $spacing-sm;

      h1 {
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $color-primary;
        text-transform: uppercase;
      }
      .subtitle {
        width: 100%;
        @include row-between;
        justify-content: flex-start;
        gap: $spacing-sm;

        h2 {
          font-size: $font-size-md;
          color: #027b81;
          font-weight: $font-weight-medium;
        }
      }
    }
  }
  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary-transparent-contrast;
    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }
}

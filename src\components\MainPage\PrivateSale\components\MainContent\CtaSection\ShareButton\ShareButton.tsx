import { useAccount } from "wagmi";
import { MainContentProps } from "../../MainContent";
import styles from "./ShareButton.module.scss";
import { showError } from "@/lib/notification";

const ShareButton = ({ openRewardsModal }: MainContentProps) => {
  const { address } = useAccount();

  const handleButtonClick = async () => {
    if (!address) {
      showError("Please connect your wallet");
      return;
    }
    openRewardsModal?.();
  };

  return (
    <div className={styles.container} onClick={handleButtonClick}>
      <div className={styles.content}>
        <h3 className={styles.title}>Share</h3>
        <p className={styles.subtitle}>One click to earn up to 10% HSK back</p>
      </div>
    </div>
  );
};

export default ShareButton;

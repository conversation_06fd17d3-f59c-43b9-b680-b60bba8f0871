import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState, useEffect } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { formatEther } from "viem";

type TPriceHistory = { date: number | string; price: number };

const usePriceHistory = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [priceHistory, setPriceHistory] = useState<TPriceHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);

  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const OFFSET = 0n;
  const LIMIT = 10n;

  // Function to convert Unix timestamp to formatted date
  const convertUnixTimeToDate = (unixTime: string): string => {
    const date = new Date(Number(unixTime) * 1000);
    return date.toLocaleDateString();
  };

  const analyzePriceHistory = (priceHistory: TPriceHistory[]) => {
    if (priceHistory.length === 0) {
      return {
        lowest: 0,
        highest: 0,
        current: 0,
      };
    }

    const prices = priceHistory.map((item) => item.price);

    return {
      lowest: prices.length > 0 ? Math.min(...prices) : 0,
      highest: prices.length > 0 ? Math.max(...prices) : 0,
      current: prices[prices.length - 1] || 0,
    };
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const shouldFetch =
    isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0;

  const { refetch } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getPriceHistoryPaginated",
    args: [BigInt(asteroidMineNumber), OFFSET, LIMIT],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
    onSuccess: (data) => {
      const formatted = data[0]
        .map((price) => ({
          date: new Date(Number(price.timestamp) * 1000).toLocaleDateString(),
          price: Number(formatEther(price.price)),
        }))
        .sort(
          (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
        );

      setPriceHistory(formatted);
      setHasFetched(true);
    },
    onSettled: () => setLoading(false),

    onError: () => {
      setHasFetched(true);
      setLoading(false);
    },
  });

  useEffect(() => {
    if (shouldFetch) {
      setLoading(true);
      refetch();
    }
  }, [shouldFetch, refetch]);

  // useContractRead({
  //   address: marketpaceAddress,
  //   abi: asteroidXMarketplaceAbi,
  //   functionName: "getPriceHistoryPaginated",
  //   args: [BigInt(asteroidMineNumber), OFFSET, LIMIT],
  //   enabled:
  //     isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
  //   watch: true,
  //   onSuccess: (data) => {
  //     const tempPriceHistory = data[0].map((price) => ({
  //       date: Number(price.timestamp),
  //       price: Number(formatEther(price.price)),
  //     }));
  //     tempPriceHistory.sort((a, b) => a.date - b.date);
  //     tempPriceHistory.forEach((price) => {
  //       //@ts-ignore
  //       price.date = convertUnixTimeToDate(price.date.toString());
  //     });

  //     setPriceHistory(tempPriceHistory);
  //   },
  // });

  return { priceHistory, analyzePriceHistory, loading, hasFetched };
};

export default usePriceHistory;

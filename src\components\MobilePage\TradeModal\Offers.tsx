import styles from "./CurrentPrice.module.scss";
import TokenPrice from "@/components/common/TokenPrice";
import useActiveListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useActiveListing";
import usePurchaseAllListingAmount from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/usePurchaseAllListingAmount";
import { useState } from "react";
import useApprove from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useApprove";
import useActiveOffer from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useActiveOffer";
import useAcceptOffer from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useAcceptOffer";
import { networkConfigs } from "@/constants/networkConfigs";
import { useAccount, useNetwork } from "wagmi";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";

const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const Offers = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded((prevState) => !prevState);
  };
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { allActiveOfferSortedByPrice, formatUnixTimestamp } = useActiveOffer();
  const { chain } = useNetwork();
  const {
    acceptOffer,
    isAcceptingOffer,
    isWaitingForAcceptOffer,
    offerId,
    setOfferId,
  } = useAcceptOffer();
  const { isApprovedForAll } = useApprove();
  const { address } = useAccount();

  const handleAcceptOffer = () => {
    acceptOffer();
  };

  const handleMouseOver = (index: number) => {
    if (allActiveOfferSortedByPrice) {
      setOfferId(allActiveOfferSortedByPrice[index].offerId);
    }
  };

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // 计算分页数据
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems =
    allActiveOfferSortedByPrice?.slice(startIndex, endIndex) || [];
  const totalPages = allActiveOfferSortedByPrice
    ? Math.ceil(allActiveOfferSortedByPrice.length / itemsPerPage)
    : 0;

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <div className={styles.container}>
      <div className={styles.listings}>
        <div
          className={`${styles.mainHeader} ${
            isExpanded ? styles.expanded : styles.collapsed
          }`}
          onClick={handleToggle}
        >
          <span className={styles.glowText}>OFFERS</span>
          <span className={styles.buttonHighlight}></span>
        </div>
        {isExpanded && (
          <>
            <div className={styles.listingsTable}>
              <div className={styles.tableHeader}>
                <div>Address</div>
                <div>Price per Token</div>
                <div>Quantity</div>
                <div>Total Amount</div>
                <div>Expiration</div>
                <div>Action</div>
              </div>

              <div className={styles.tableBody}>
                {allActiveOfferSortedByPrice &&
                allActiveOfferSortedByPrice.length > 0 ? (
                  currentItems.map((offer, index) => (
                    <div className={styles.tableRow} key={index}>
                      <div>
                        <a
                          href={
                            chain &&
                            networkConfigs[chain.id].etherscanAddress +
                              offer.buyer
                          }
                          target="_blank"
                          style={{ color: "#4895ef" }}
                        >
                          {trimWalletAddress(offer.buyer)}
                        </a>
                      </div>
                      <div className={styles.price}>
                        <TokenPrice
                          amount={offer.pricePerToken}
                          tokenAddress={offer.paymentToken}
                          showCurrency={true}
                          currencySymbol={selectedMine.currency}
                        />
                      </div>
                      <div className={styles.quantity}>
                        {offer.amount.toString()}
                      </div>
                      <div className={styles.price}>
                        <TokenPrice
                          amount={offer.pricePerToken * offer.amount}
                          tokenAddress={offer.paymentToken}
                          showCurrency={true}
                          currencySymbol={selectedMine.currency}
                        />
                      </div>
                      <div className={styles.expiration}>
                        {formatUnixTimestamp(Number(offer.expirationTime))}
                      </div>
                      <div>
                        <button
                          className={`${styles.buyButton} ${
                            isAcceptingOffer || isWaitingForAcceptOffer
                              ? styles.disabled
                              : ""
                          }`}
                          onClick={handleAcceptOffer}
                          onMouseEnter={() => handleMouseOver(index)}
                          disabled={
                            isAcceptingOffer ||
                            isWaitingForAcceptOffer ||
                            !isApprovedForAll ||
                            offer.buyer.toLowerCase() === address?.toLowerCase()
                          }
                          style={{
                            opacity:
                              isAcceptingOffer ||
                              isWaitingForAcceptOffer ||
                              !isApprovedForAll ||
                              offer.buyer.toLowerCase() ===
                                address?.toLowerCase()
                                ? 0.5
                                : 1,
                            pointerEvents:
                              isAcceptingOffer ||
                              isWaitingForAcceptOffer ||
                              !isApprovedForAll ||
                              offer.buyer.toLowerCase() ===
                                address?.toLowerCase()
                                ? "none"
                                : "auto",
                          }}
                        >
                          {(isAcceptingOffer || isWaitingForAcceptOffer) &&
                          offerId === offer.offerId
                            ? "Processing..."
                            : offer.buyer.toLowerCase() ===
                              address?.toLowerCase()
                            ? "YOUR OFFER"
                            : "ACCEPT"}
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className={styles.noListings}>
                    No active offering available
                  </div>
                )}
              </div>
            </div>
            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={styles.pageButton}
                >
                  ← Previous
                </button>

                <span className={styles.pageInfo}>
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={styles.pageButton}
                >
                  Next →
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Offers;

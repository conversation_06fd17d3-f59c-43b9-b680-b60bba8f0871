import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { mineCardStore } from "@/stores/mineCard";
import { useSnapshot } from "valtio";

const useMineTokenId = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);

  const getSelectedMineId = (chainId: number) => {
    const selectedMine = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0].name;

    switch (selectedMine) {
      case minesDetails[0].name:
        return networkConfigs[chainId].assetIds.mt;
      case minesDetails[1].name:
        return networkConfigs[chainId].assetIds.matsa;
      case minesDetails[2].name:
        return networkConfigs[chainId].assetIds.zephyr;
      case minesDetails[3].name:
        return networkConfigs[chainId].assetIds.jim;
      case minesDetails[4].name:
        return networkConfigs[chainId].assetIds.pcgold;
      case minesDetails[5].name:
        return networkConfigs[chainId].assetIds.menzies;
      case minesDetails[6].name:
        return networkConfigs[chainId].assetIds.hskreward;
      default:
        break;
    }
  };
  return { getSelectedMineId };
};

export default useMineTokenId;

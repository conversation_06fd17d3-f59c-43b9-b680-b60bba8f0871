import { motion, AnimatePresence } from "framer-motion";
import styles from "./ShareModal.module.scss";
import { useState, useLayoutEffect } from "react";
import { useAccount } from "wagmi";
import useTokenInfo from "@/components/MainPage/PrivateSale/components/MainContent/CtaSection/SaleDetails/hooks/useTokenInfo";
import { getInvitationCodeFromOwnServer } from "@/actions/getInvitationCodeFromOwnServer";
import { showSuccess } from "@/lib/notification";
import { useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
}
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000);

  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
};

const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 4)}...${address.slice(-4)}`;
};

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose }) => {
  const [shortCode, setShortCode] = useState("");
  const { address } = useAccount();
  const { chain } = useNetwork();
  const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";
  const isDev = process.env.NODE_ENV === "development";
  const websiteUrl = isDev
    ? "http://localhost:3000/main?invitation="
    : isTestnet
    ? "https://testnet.asteroidx.io/main?invitation="
    : "https://asteroidx.io/main?invitation=";

  const {
    rewardPoints,
    contributions,
    totalContributions,
    currentPage,
    totalPages,
    changePage,
    isLoading,
  } = useTokenInfo(shortCode);

  useLayoutEffect(() => {
    if (address) {
      getInvitationCodeFromOwnServer(address).then((shortCode) =>
        setShortCode(shortCode),
      );
    } else {
      setShortCode("");
      onClose();
    }
  }, [address]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className={styles.overlay}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          <motion.div
            className={styles.modal}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
          >
            <button className={styles.closeButton} onClick={onClose}>
              ×
            </button>

            <div className={styles.content}>
              <p className={styles.description}>
                {/* Thank you for your continued support of HSK and Asteroid X. Here,
                <strong> you can keep earning $Asteroid tokens</strong> and continuously
                increase your rewards. */}
                <strong>Share & Expand - Grow with Us</strong>
              </p>
              <div className={styles.divider} />

              <div className={styles.section}>
                <h2 className={styles.sectionTitle}>Invite friends</h2>
                {/* <p className={styles.inviteDescription}>
                  For every contribution made by your invitees, you will receive{" "}
                  <span className={styles.tokenText}>5%</span> of their HSK
                  amount as a direct reward.
                </p> */}
                <div className={styles.inviteSection}>
                  <div className={styles.inviteLink}>
                    {websiteUrl + shortCode}
                  </div>

                  <button
                    className={styles.copyButton}
                    onClick={() => {
                      navigator.clipboard
                        .writeText(websiteUrl + shortCode)
                        .then(() => {
                          showSuccess("Invitation link copied to clipboard");
                        })
                        .catch((err) => {
                          showSuccess(`Failed to copy text: ${err}`);
                        });
                    }}
                  >
                    COPY INVITATION
                  </button>
                </div>

                <div className={styles.divider} />

                <div className={styles.contributionDetails}>
                  <h2 className={styles.sectionTitle}>Contribution Details</h2>
                  <div className={styles.totalContributions}>
                    Total contributions: {totalContributions}
                  </div>

                  <div className={styles.contributionTable}>
                    <div className={styles.tableHeader}>
                      <div>Address</div>
                      <div>Amount (HSK)</div>
                      <div>Date</div>
                    </div>

                    <div className={styles.tableBody}>
                      {contributions.map((record, index) => {
                        const value = parseFloat(record.amount) * 0.05;
                        const truncated = Math.floor(value * 1000) / 1000;
                        const rewardAmount = truncated.toFixed(3);

                        return (
                          <div key={index} className={styles.tableRow}>
                            {/* <div className={styles.address}>
                              {trimWalletAddress(record.contributor)}
                            </div> */}
                            <div>
                              <a
                                href={
                                  chain &&
                                  networkConfigs[chain.id].etherscanAddress +
                                    record.contributor
                                }
                                target="_blank"
                                className={styles.address}
                              >
                                {trimWalletAddress(record.contributor)}
                              </a>
                            </div>
                            <div className={styles.amountValue}>
                              {rewardAmount}
                            </div>
                            <div className={styles.date}>
                              {formatDate(record.timestamp)}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {totalPages > 1 && (
                    <div className={styles.pagination}>
                      <button
                        className={styles.paginationButton}
                        onClick={() => changePage(currentPage - 1)}
                        disabled={currentPage === 0}
                      >
                        Previous
                      </button>
                      <span className={styles.pageNumber}>
                        {currentPage + 1} / {totalPages}
                      </span>
                      <button
                        className={styles.paginationButton}
                        onClick={() => changePage(currentPage + 1)}
                        disabled={currentPage >= totalPages - 1}
                      >
                        Next
                      </button>
                    </div>
                  )}
                </div>
                <div className={styles.divider} />

                <h2 className={styles.sectionTitle}>Your current rewards</h2>
                <p className={styles.sectionDescription}>
                  For every contribution made by your invitees, you will receive{" "}
                  <span className={styles.tokenText}>5%</span> of their HSK
                  amount as a direct reward.
                </p>
                <div className={styles.rewardBox}>
                  Reward {rewardPoints} HSK
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ShareModal;

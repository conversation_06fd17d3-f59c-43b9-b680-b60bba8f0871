@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  // border-radius: 1rem;
  margin-top: 1rem;
  background: $color-black-transparent;
  gap: 0.5rem;
  @include col-center;

  .wrapper {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    // border: 1px solid $color-primary;
    .leftContainer {
      flex: 3;
    }

    .rightContainer {
      flex: 2;
    }
  }
  .priceTag {
    color: $color-primary-transparent-contrast;
    font-size: $font-size-2xl;
    font-weight: $font-weight-light;
    margin-top: -0.5rem;

    span {
      font-size: $font-size-md;

      color: $color-primary;
    }
  }

  .pointSection {
    width: 100%;
    // border: 1px solid $color-primary;
  }
  .pointTitle {
    h2 {
      color: $color-primary;
      font-size: 0.7rem;
      font-family: $font-family-poppins;
      margin-bottom: 0.1rem;
    }
  }

  .timeSection {
    width: 100%;
    align-items: left;
    text-transform: uppercase;
    margin: $margin-sm 0;

    h2 {
      font-family: "Garalama", sans-serif;
      font-weight: $font-weight-extralight;
      color: $color-primary-transparent-contrast; // Updated to cyan color from image
      font-size: 0.8rem;

      margin-bottom: 0.1rem;
    }
  }
}

.title {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: $margin-md;
  font-size: $font-size-md;
  text-transform: uppercase;
  color: $color-primary;
  font-family: "Garalama", sans-serif;
  font-weight: $font-weight-extralight;
  letter-spacing: 1px;
}
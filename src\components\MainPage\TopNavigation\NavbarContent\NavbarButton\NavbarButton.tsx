import styles from "./NavbarButton.module.scss";
import variables from "@/styles/variables.module.scss";
import { navbarButtonDetails } from "../NavbarContent";
import ArrowDownIcon from "../components/ArrowDownIcon";
import { motion } from "framer-motion";
import { useState } from "react";
import { navbarButtonStore } from "@/stores/navbarButton";
import { buttonEffect } from "@/animations/animations";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import { useSnapshot } from "valtio";

type Unpacked<T> = T extends (infer U)[] ? U : T;
type ButtonDetails = {
  buttonDetails: Unpacked<typeof navbarButtonDetails>;
};
type NavbarButtonProps = ButtonDetails & typeof navbarButtonStore;

const NavbarButton = ({
  buttonDetails,
  selectedButton,
  setSelectedButton,
  setIsInitialAnimationDone,
}: NavbarButtonProps) => {
  const [isHovering, setIsHovering] = useState(false);
  const isFocus = selectedButton === buttonDetails.title;

  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);

  return (
    <motion.div
      whileTap={buttonEffect.tap}
      className={styles.container}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onClick={() => {
        setSelectedButton(buttonDetails.title);
        setIsInitialAnimationDone(true);
        if (buttonDetails.title !== navbarButtonDetails[0].title) {
          connectWalletModalSnapshot.setIsOpenConnectWalletModal(false);
        }
      }}
    >
      {buttonDetails.icon(
        isFocus || isHovering
          ? variables.colorPrimary
          : variables.colorPrimaryContrast,
      )}
      <div>
        <h1
          className={
            isFocus
              ? styles.titleFocus
              : isHovering
              ? styles.titleHover
              : styles.title
          }
        >
          {buttonDetails.title.toUpperCase()}
        </h1>
        <h2
          className={
            isFocus
              ? styles.subtitleFocus
              : isHovering
              ? styles.subtitleHover
              : styles.subtitle
          }
        >
          {buttonDetails.subTitle.toUpperCase()}
        </h2>
      </div>
      {/* <ArrowDownIcon
        color={
          isFocus || isHovering
            ? variables.colorPrimary
            : variables.colorPrimaryContrast
        }
      /> */}
    </motion.div>
  );
};

export default NavbarButton;

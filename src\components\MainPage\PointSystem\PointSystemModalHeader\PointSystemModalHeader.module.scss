@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 90%;
  // border: 1px solid yellow;
  @include row-between;
  gap: 1rem;

  .overview {
    flex: 1;
    font-size: $font-size-4xl;
    // font-weight: $font-weight-semibold;
    color: $color-primary;
    text-transform: uppercase;
  }

  .mineralInfo {
    flex: 4;
    @include row-center;
    justify-content: flex-start;
    gap: 3rem;

    div {
      // cursor: pointer;
      line-height: 150%;

      h1 {
        font-size: $font-size-4xl;
        text-transform: uppercase;
        color: $color-primary;
        // color: lightgrey;
        // font-weight: $font-weight-semibold;
      }
      h2 {
        font-size: $font-size-xl;
        text-transform: uppercase;
        color: $color-primary-contrast;
        font-weight: $font-weight-light;
      }
    }
    .claimButton {
      @include row-center;
      width: 30%;

      button {
        @include row-center;
        margin-left: 5rem;
        width: 100%;
        text-align: center;
        font-size: $font-size-sm;
        font-family: $font-family-poppins;
        // color: $color-primary;
        color: $color-warning;
        text-transform: uppercase;
        font-weight: $font-weight-semibold;
        border: $border-width-xs solid $color-primary-contrast;
        padding: $padding-sm 0;
        background: $color-primary-transparent;
        backdrop-filter: blur(5px);
        border-radius: $border-radius-sm;
        cursor: pointer;

        span {
          color: $color-primary-transparent-contrast;
        }

        &:hover {
          border: $border-width-xs solid $color-primary;
        }
      }
    }
  }
}

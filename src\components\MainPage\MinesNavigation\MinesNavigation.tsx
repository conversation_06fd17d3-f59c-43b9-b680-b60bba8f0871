import { minesDetails } from "@/constants/mineDetails";
import MineCard from "./MineCard/MineCard";
import SpecialEventMineCard from "./SpecialEventMineCard/MineCard";
import { motion } from "framer-motion";
import { fadeIn } from "@/animations/animations";
import RedPocket from "../RedPocket/RedPocket";
import { ignoreMineLists } from "@/constants/ignoreMineList";

const MinesNavigation = ({ delay = 1 }) => {
  return (
    <>
      {minesDetails.map((mine, index) => {
        if (mine.shortName === "hskreward") {
          return (
            // <SpecialEventMineCard
            //   delay={delay + mine.delayOffset}
            //   information={mine}
            //   key={index}
            // />
            null
          );
        } else if (ignoreMineLists.includes(mine.shortName)) {
          return null;
        } else {
          return (
            <MineCard
              delay={delay + mine.delayOffset}
              information={mine}
              key={index}
            />
          );
        }
      })}
      {/* <RedPocket /> */}
    </>
  );
};

export default MinesNavigation;

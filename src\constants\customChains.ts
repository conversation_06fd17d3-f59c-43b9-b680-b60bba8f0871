import { Chain } from "wagmi";

export const hashkeyTestnet = {
  id: 133,
  name: "HashkeyTestnet",
  network: "hashkeyTestnet",
  nativeCurrency: {
    decimals: 18,
    name: "<PERSON><PERSON><PERSON>",
    symbol: "HS<PERSON>",
  },
  rpcUrls: {
    public: { http: ["https://testnet.hsk.xyz"] },
    default: { http: ["https://testnet.hsk.xyz"] },
  },
  blockExplorers: {
    etherscan: {
      name: "HashKey Chain Explorer",
      url: "https://testnet-explorer.hsk.xyz",
    },
    default: {
      name: "HashKey Chain Explorer",
      url: "https://testnet-explorer.hsk.xyz",
    },
  },
  testnet: true,
} as const satisfies Chain;

export const hashkey = {
  id: 177,
  name: "Hashkey",
  network: "hashkey",
  nativeCurrency: {
    decimals: 18,
    name: "HSK",
    symbol: "HSK",
  },
  rpcUrls: {
    public: { http: ["https://mainnet.hsk.xyz"] },
    default: { http: ["https://mainnet.hsk.xyz"] },
  },
  blockExplorers: {
    etherscan: {
      name: "HashKey Chain Explorer",
      url: "https://hashkey.blockscout.com",
    },
    default: {
      name: "HashKey Chain Explorer",
      url: "https://hashkey.blockscout.com",
    },
  },
  testnet: false,
} as const satisfies Chain;

export const BNBTestnet = {
  id: 97,
  name: "BNB Testnet",
  network: "bnbTestnet",
  nativeCurrency: {
    decimals: 18,
    name: "BNB",
    symbol: "tBNB",
  },
  rpcUrls: {
    public: {
      http: ["https://bsc-testnet-rpc.publicnode.com"],
      webSocket: ["wss://bsc-testnet-rpc.publicnode.com"],
    },
    default: {
      http: ["https://data-seed-prebsc-1-s3.bnbchain.org:8545"],
      webSocket: ["wss://bsc-testnet.drpc.org"],
    },
  },
  blockExplorers: {
    etherscan: {
      name: "BNB Smart Chain Testnet Explorer",
      url: "https://testnet.bscscan.com",
    },
    default: {
      name: "BNB Smart Chain Testnet Explorer",
      url: "https://testnet.bscscan.com",
    },
  },
  testnet: true,
} as const satisfies Chain;

export const BNB = {
  id: 56,
  name: "BNB CHAIN",
  network: "BNB",
  nativeCurrency: {
    decimals: 18,
    name: "BNB",
    symbol: "BNB",
  },
  rpcUrls: {
    public: {
      http: ["https://binance.llamarpc.com"],
      webSocket: ["wss://bsc-rpc.publicnode.com"],
    },
    default: { http: ["https://bsc-dataseed.bnbchain.org"], webSocket: [] },
  },
  blockExplorers: {
    etherscan: {
      name: "BNB Smart Chain Explorer",
      url: "https://bscscan.com",
    },
    default: {
      name: "BNB Smart Chain Explorer",
      url: "https://bscscan.com",
    },
  },
  testnet: false,
} as const satisfies Chain;

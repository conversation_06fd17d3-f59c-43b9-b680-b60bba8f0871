import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./Newsletter.module.scss";
import { AnimatePresence, MotionStyle, motion } from "framer-motion";
import { slideIn } from "@/animations/animations";
import ShowMoreButton from "./ShowMoreButton/ShowMoreButton";
import NewsContents from "./NewsContents/NewsContents";

interface NewsletterProps {
  delay?: number;
  isButtonClicked: boolean;
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
}

const Newsletter = ({ isButtonClicked, delay }: NewsletterProps) => {
  const [hasInitialAnimationDone, setHasInitialAnimationDone] = useState(false);
  const [isShowMoreButtonClicked, setIsShowMoreButtonClicked] = useState(false);

  const showMoreButtonConfigs = {
    isShowMoreButtonClicked,
    setIsShowMoreButtonClicked,
  };

  const newsContentsConfigs = {
    isShowMoreButtonClicked,
  };

  const borderExpandEffect: MotionStyle = {
    height: isShowMoreButtonClicked ? "617px" : "500px",
    transition: "height 0.25s linear",
  };

  useEffect(() => {
    setTimeout(
      () => {
        setHasInitialAnimationDone(true);
      },
      delay && (delay + 2) * 1000,
    );
  }, []);

  return (
    <>
      {!isButtonClicked ? (
        <motion.div
          style={borderExpandEffect}
          className={styles.wrapper}
          variants={slideIn(-500, 0, 0.5, hasInitialAnimationDone ? 0 : delay)}
          initial="hidden"
          animate="visible"
        >
          <ShowMoreButton {...showMoreButtonConfigs} />
          <NewsContents {...newsContentsConfigs} />
        </motion.div>
      ) : (
        <motion.div
          style={borderExpandEffect}
          className={styles.wrapper}
          variants={slideIn(0, -500, 0.5, 0)}
          initial="hidden"
          animate="visible"
        />
      )}
    </>
  );
};

export default Newsletter;

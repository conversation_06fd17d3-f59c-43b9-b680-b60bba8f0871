import TokenPrice from "@/components/common/TokenPrice";
import styles from "./TradeDashboard.module.scss";
import useActiveOffer from "./hooks/useActiveOffer";
import { useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";
import useTokenActivities from "./hooks/useTokenActivities";
import { useState } from "react";
import { is } from "@react-three/fiber/dist/declarations/src/core/utils";

const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const Activities = () => {
  const ZERO_ADDRESS = "******************************************";
  const { formatUnixTimestamp } = useActiveOffer();
  const { chain } = useNetwork();
  const {
    tokenActivitiesFilteredByType,
    activityTypes,
    marketplaceVaultAddress,
  } = useTokenActivities();
  const [isOpen, setIsOpen] = useState(false);

  const ReplaceZeroAddress = () => (
    <a
      href={
        chain &&
        marketplaceVaultAddress &&
        networkConfigs[chain.id].etherscanAddress + marketplaceVaultAddress
      }
      target="_blank"
      style={{ color: "#4895ef" }}
    >
      {marketplaceVaultAddress && trimWalletAddress(marketplaceVaultAddress)}
    </a>
  );

  return (
    <div className={styles.listings}>
      <div
        className={styles.headerContainer}
        onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className={styles.mainHeader}>EVENTS</h2>
        <span className={styles.toggleIcon}>{isOpen ? "-" : "+"}</span>
      </div>
      {isOpen && (
        <div className={styles.listingsTable}>
          <div className={styles.tableHeader}>
            <div>Event</div>
            <div>Price per Token</div>
            <div>Quantity</div>
            <div>From</div>
            <div>To</div>
            <div>Date</div>
          </div>

          <div className={styles.tableBody}>
            {tokenActivitiesFilteredByType &&
            tokenActivitiesFilteredByType.length > 0 ? (
              tokenActivitiesFilteredByType.map((token, index) => (
                <div className={styles.tableRow} key={index}>
                  <div className={styles.price}>
                    {activityTypes[token.activityType]}
                  </div>
                  <div className={styles.price}>
                    <TokenPrice
                      amount={token.price}
                      tokenAddress={token.paymentToken}
                    />
                  </div>
                  <div className={styles.price}>{token.amount.toString()}</div>
                  <div>
                    {token.from === ZERO_ADDRESS ? (
                      <ReplaceZeroAddress />
                    ) : (
                      <a
                        href={
                          chain &&
                          networkConfigs[chain.id].etherscanAddress + token.from
                        }
                        target="_blank"
                        style={{ color: "#4895ef" }}
                      >
                        {trimWalletAddress(token.from)}
                      </a>
                    )}
                  </div>
                  <div>
                    {token.to === ZERO_ADDRESS ? (
                      <ReplaceZeroAddress />
                    ) : (
                      <a
                        href={
                          chain &&
                          networkConfigs[chain.id].etherscanAddress + token.to
                        }
                        target="_blank"
                        style={{ color: "#4895ef" }}
                      >
                        {trimWalletAddress(token.to)}
                      </a>
                    )}
                  </div>
                  <div className={styles.expiration}>
                    {formatUnixTimestamp(Number(token.timestamp))}
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noListings}>
                No active activities available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Activities;

import styles from "./TradeModalHeader.module.scss";
import variables from "@/styles/variables.module.scss";
import lineIconLight from "@/assets/icons/plusModal/lineIconLight.png";
import lineIconMedium from "@/assets/icons/plusModal/lineIconMedium.png";
import lineIconDark from "@/assets/icons/plusModal/lineIconDark.png";
import { useState } from "react";
import { plusModalStore } from "@/stores/plusModal";
import { useSnapshot } from "valtio";

const TradeModalHeader = () => {
  // const [selectedTitle, setSelectedTitle] = useState(headerDetails[0].title);
  const plusModalSnapshot = useSnapshot(plusModalStore);

  // console.log(isTitleSelected);
  return (
    <div className={styles.container}>
      <h1 className={styles.overview}>overview</h1>
      <div className={styles.mineralInfo}>
        <img src={lineIconLight.src} alt="light line icon" />
        <img src={lineIconMedium.src} alt="medium line icon" />
        <img src={lineIconDark.src} alt="dark line icon" />
        <div>
          <h1>ASTEROID X MARKETPLACE</h1>
          {/* <h2 style={{ color: "red" }}>
            non-functional UI display for demonstration purposes only
          </h2> */}
        </div>
        {/* {headerDetails.map((details, index) => (
          <div
            key={index}
            style={
              plusModalSnapshot.selectedTitle === details.title
                ? {
                    textShadow: `0px 0px 15px ${variables.colorPrimary}`,
                    color: variables.colorPrimary,
                    opacity: 1,
                  }
                : undefined
            }
            onClick={() => plusModalSnapshot.setSelectedTitle(details.title)}
          >
            <h1>{details.title}</h1>
            <h2>{details.subtitle}</h2>
          </div>
        ))} */}
      </div>
    </div>
  );
};

export const headerDetails = [
  { title: "mineral information", subtitle: "details about this mine" },
  { title: "the company", subtitle: "details about this company" },
  { title: "investor zone", subtitle: "details about investment" },
];

export default TradeModalHeader;

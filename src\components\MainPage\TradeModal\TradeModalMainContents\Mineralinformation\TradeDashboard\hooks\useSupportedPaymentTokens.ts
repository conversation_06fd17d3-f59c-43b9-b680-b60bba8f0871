import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";

type PaymentToken = {
  address: `0x${string}`;
  feeRateBps: bigint;
};

const useSupportedPaymentTokens = () => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [supportedPaymentTokens, setSupportedPaymentTokens] = useState<
    PaymentToken[]
  >([]);
  const [isUsdtSupported, setIsUsdtSupported] = useState(false);
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");

  const { chain } = useNetwork();
  const { isConnected } = useAccount();

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setUsdtAddress(networkConfigs[chain.id].usdtAddress);
    } else {
      setMarketplaceAddress("0x");
      setUsdtAddress("0x");
    }
  }, [chain]);

  const { data: paymentTokensInfo } = useContractRead({
    address: marketplaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getAllSupportedPaymentTokensInfo",
    enabled: isConnected && marketplaceAddress !== "0x",
    watch: true,
  });

  useEffect(() => {
    if (paymentTokensInfo) {
      const [tokens, feeRates] = paymentTokensInfo as [
        `0x${string}`[],
        bigint[],
      ];

      // Map to array of token objects with their fee rates
      const tokenObjects = tokens.map((addr, index) => ({
        address: addr,
        feeRateBps: feeRates[index],
      }));

      setSupportedPaymentTokens(tokenObjects);

      // Check if USDT is in the supported tokens list
      const isUsdtInList = tokens.some(
        (token) => token.toLowerCase() === usdtAddress.toLowerCase(),
      );

      setIsUsdtSupported(isUsdtInList);
    } else {
      setSupportedPaymentTokens([]);
      setIsUsdtSupported(false);
    }
  }, [paymentTokensInfo, usdtAddress]);

  return {
    isUsdtSupported,
    usdtAddress,
  };
};

export default useSupportedPaymentTokens;

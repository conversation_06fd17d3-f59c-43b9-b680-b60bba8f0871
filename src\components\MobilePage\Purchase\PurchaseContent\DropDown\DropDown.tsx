import React from "react";
import * as Select from "@radix-ui/react-select";
import classnames from "classnames";
import {
  CheckIcon,
  ChevronDownIcon
} from "@radix-ui/react-icons";
import styles from "./DropDown.module.scss";

const SelectCurrency: React.FC = () => (
  <Select.Root>
    <Select.Trigger className={styles.SelectTrigger} aria-label="Currency">
      <Select.Value placeholder="Select a Currency" />
      <Select.Icon className={styles.SelectIcon}>
        <ChevronDownIcon />
      </Select.Icon>
    </Select.Trigger>
    <Select.Portal>
      <Select.Content className={styles.SelectContent}>
        <Select.Viewport className={styles.SelectViewport}>
          <Select.Group>
            <SelectItem value="USD">USD</SelectItem>
            <SelectItem value="AUD">AUD</SelectItem>
            <SelectItem value="RMB">RMB</SelectItem>
          </Select.Group>
        </Select.Viewport>
      </Select.Content>
    </Select.Portal>
  </Select.Root>
);

type SelectItemProps = React.ComponentProps<typeof Select.Item> & {
  children: React.ReactNode;
  className?: string;
};

const SelectItem = React.forwardRef<HTMLDivElement, SelectItemProps>(
  ({ children, className, ...props }, forwardedRef) => {
    return (
      <Select.Item
        className={classnames("SelectItem", className)}
        {...props}
        ref={forwardedRef}
      >
        <Select.ItemText>{children}</Select.ItemText>
        <Select.ItemIndicator>
          <CheckIcon />
        </Select.ItemIndicator>
      </Select.Item>
    );
  },
);

SelectItem.displayName = "SelectItem"; // Set a display name for debugging purposes

export default SelectCurrency;

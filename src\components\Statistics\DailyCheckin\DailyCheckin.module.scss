@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.checkinSection {
  margin-bottom: 1rem;
  padding-bottom: 2rem;
  text-align: center;

  h1 {
    text-transform: uppercase;
    animation: background-pan 3s linear infinite;
    background: linear-gradient(
      to right,
      rgb(123, 31, 162),
      rgb(103, 58, 183),
      rgb(244, 143, 177),
      rgb(123, 31, 162)
    );
    background-size: 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    white-space: nowrap;
  }

  .checkinButton {
    text-align: center;
    width: 300px;
    font-size: $font-size-sm;
    font-family: $font-family-poppins;
    color: $color-warning;
    text-transform: uppercase;
    font-weight: $font-weight-semibold;
    border: $border-width-xs solid $color-primary-contrast;
    padding: $padding-sm;
    background: $color-primary-transparent;
    backdrop-filter: blur(5px);
    border-radius: $border-radius-sm;
    margin-top: 1rem;
    cursor: pointer;

    span {
      color: $color-primary-transparent-contrast;
    }

    &:hover {
      border: $border-width-xs solid $color-primary;
    }
  }
}

@keyframes background-pan {
  from {
    background-position: 0% center;
  }

  to {
    background-position: -200% center;
  }
}

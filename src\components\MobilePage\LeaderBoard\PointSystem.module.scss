@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.wrapper {
  width: 100%;
  height: 100%;
  background: $color-primary;
  // box-shadow: 0 0 10px $color-primary, 0 0 20px $color-primary-transparent;
  backdrop-filter: blur(10px);
  padding: $padding-lg;
  mask-image: linear-gradient(to bottom, black 85%, transparent);
  // mask-image: linear-gradient(to bottom, black 85%, transparent);

  // 使用新的 clip-path
  clip-path: polygon(
    0% 8%,
    15% 8%,
    30% 0,
    70% 0,
    85% 8%,
    100% 8%,
    100% 85%,
    100% 100%,
    85% 100%,
    15% 100%,
    0 100%,
    0% 85%
  );
  position: relative;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: black;
    filter: drop-shadow(0 0 20px rgba(0, 247, 255, 0.5));
    clip-path: polygon(
      // 内边框路径（向内缩小以创建边框效果）
      1px calc(8% + 1px),
      calc(15% + 1px) calc(8% + 1px),
      calc(30% + 1px) 1px,
      calc(70% - 1px) 1px,
      calc(85% - 1px) calc(8% + 1px),
      calc(100% - 1px) calc(8% + 1px),
      calc(100% - 1px) calc(85% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(85% - 1px) calc(100% - 1px),
      calc(15% + 1px) calc(100% - 1px),
      1px calc(100% - 1px),
      1px calc(85% - 1px)
    );
  }

  // &::after {
  //   content: "";
  //   position: absolute;
  //   inset: 0;
  //   background: linear-gradient(
  //     180deg,
  //     rgba($color-primary, 0.3) 0%,
  //     transparent 100%
  //   );
  //   pointer-events: none;
  // }
}

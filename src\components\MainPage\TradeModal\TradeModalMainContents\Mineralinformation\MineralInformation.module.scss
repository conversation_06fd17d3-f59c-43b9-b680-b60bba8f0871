@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin detailsWrapper {
  width: 100%;
  margin-bottom: $margin-lg;

  h1 {
    color: $color-primary;
    text-decoration: underline;
  }
  p {
    color: $color-primary-contrast;
    font-size: $font-size-3xl;
  }
}

.container {
  width: 100%;
  height: 100%;
  @include col-between;
  align-items: flex-start;

  .scrollArea {
    width: 100%;
    height: 93%;
    overflow: hide;
    // border: 1px solid green;
    position: relative;

    &::-webkit-scrollbar {
      display: none;
    }

    .tradeDashboardScrollArea {
      width: 100%;
      height: 100%;
      overflow: scroll;
      position: absolute;
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .companyDetails {
      width: 100%;
      height: 100%;
    }

    .companyDetailsComingSoon {
      width: 100%;
      height: 100%;
      @include col-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .companyDetailsWrapper {
      @include detailsWrapper;

      img {
        width: 100%;
        // height: 100%;
        object-fit: cover;
      }
    }

    .investorZoneComingSoon {
      width: 100%;
      height: 100%;
      @include row-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .investorZone {
      width: 100%;
      height: 100%;
    }

    .investorZoneWrapper {
      @include detailsWrapper;
    }

    .imageFrame {
      @include row-center;
      // align-items: flex-start;
      gap: 2rem;
      width: 100%;
      height: 100%;
      position: relative;
      background: $color-black-transparent;
      padding: 3rem;
      clip-path: polygon(
        0 0,
        calc(100% - 80px) 0,
        100% 80px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px)
      );

      .purchaseCardScrollArea {
        width: 100%;
        height: 100%;
        overflow: scroll;
        @include row-center;
        gap: 2rem;
        flex-wrap: wrap;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      .overlay {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        bottom: 0;
        background: linear-gradient(
          0deg,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0.1) 100%
        );

        .buyButtonContainer {
          position: absolute;
          bottom: 0;
          right: 100px;

          button {
            background: $color-black-transparent;
            border: 1px solid $color-primary-contrast;
            color: $color-primary-contrast;
            padding: 1rem;
            font-size: $font-size-xl;
            font-weight: $font-weight-bold;
            cursor: pointer;

            &:hover {
              // background: $color-primary;
              color: $color-primary;
              border: 1px solid $color-primary;
            }
          }
        }

        .mineDetailsContainer {
          position: absolute;
          bottom: 0;
          left: 50px;
          width: 100%;
          line-height: 200%;
          // border: 1px solid green;

          h3 {
            color: $color-primary-contrast;
            span {
              font-size: $font-size-sm;
            }
          }

          .mineDetailsTitle {
            @include row-between;
            width: 30%;

            h1 {
              color: $color-primary;
              text-transform: uppercase;
            }

            h3 {
              color: $color-primary-contrast;
            }
          }
        }
      }

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        clip-path: polygon(
          0 0,
          calc(100% - 81px) 0,
          100% 81px,
          100% calc(100% - 48px),
          calc(100% - 48px) 100%,
          48px 100%,
          0 calc(100% - 48px),
          0 0,
          1px 1px,
          1px calc(100% - 48px - 0.41px),
          calc(48px + 0.41px) calc(100% - 1px),
          calc(100% - 48px - 0.41px) calc(100% - 1px),
          calc(100% - 1px) calc(100% - 48px - 0.41px),
          calc(100% - 1px) calc(48px + 0.41px),
          calc(100% - 48px - 0.41px) 1px,
          1px 1px
        );
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .mainTitle {
    // width: 70%;
    height: 80px;
    // border: 1px solid green;
    @include col-between;
    justify-content: flex-start;
    align-items: flex-start;
    align-self: flex-end;
    // margin-top: 1rem;
    margin-bottom: -3%;
    // line-height: 200%;

    .topTitle {
      align-self: center;
      @include row-center;

      .mineral {
        background: rgba(255, 255, 255, 0.07);
        padding: 0 $padding-xs;
        border-radius: $border-radius-sm;
        font-size: $font-size-md;
        color: grey;
        font-weight: $font-weight-medium;
      }

      img {
        margin-left: $spacing-sm;
      }

      .location {
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        color: grey;
      }
    }

    .bottomTitle {
      margin-top: -$spacing-sm;
      font-size: $font-size-5xl;
      font-weight: $font-weight-light;
      color: grey;
    }
  }
}

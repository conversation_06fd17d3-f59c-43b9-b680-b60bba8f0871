import CtaSection from "./CtaSection/CtaSection";
import HeroSection from "./HeroSection/HeroSection";
import styles from "./MainContent.module.scss";

export interface MainContentProps {
  openRewardsModal?: () => void;
  openJoinNowModal?: () => void;
}

const MainContent: React.FC<MainContentProps> = ({
  openRewardsModal,
  openJoinNowModal,
}) => {
  // Add a button or some UI element to trigger the modal
  return (
    <div className={styles.container}>
      <HeroSection />
      <CtaSection
        openRewardsModal={openRewardsModal}
        openJoinNowModal={openJoinNowModal}
      />
    </div>
  );
};

export default MainContent;

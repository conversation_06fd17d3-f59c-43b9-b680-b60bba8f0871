import styles from "./MineListContent.module.scss";
import locationIcon from "@/assets/icons/minesNavigation/locationIcon.png";
import { minesDetails } from "@/constants/mineDetails";
import Image from "next/image";
import table from "@/assets/images/table.jpg";
import { useState, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { showError } from "@/lib/notification";
import { toast } from "react-toastify";
import { BaseError } from "viem";
// import { ignoreMineLists } from "@/constants/ignoreMineList";
import { useNetwork, useSwitchNetwork } from "wagmi";

interface MineListContentsProps {
  isShowMoreButtonClicked: boolean;
  showMineNavigation: boolean;
  handleMineClick: any;
}

const MineListHeader = () => (
  <div className={styles.headerWrapper}>
    <div className={styles.header}>
      <h1 className={styles.title}>MineList</h1>
      <h2 className={styles.subTitle}>list of mines</h2>
    </div>
  </div>
);

// NetworkInfo.tsx
interface NetworkInfoProps {
  chain: any;
  chains: any[];
  switchNetwork?: (networkId: number) => void;
  showAll: boolean;
  onToggleView: (show: boolean) => void;
}

const NetworkInfo = ({
  chain,
  chains,
  switchNetwork,
  showAll,
  onToggleView,
}: NetworkInfoProps) => {
  const [showNetworkMenu, setShowNetworkMenu] = useState(false);

  const handleToggleNetworkMenu = () => {
    setShowNetworkMenu(!showNetworkMenu);
  };

  const handleSelectNetwork = useCallback(
    (networkId: number) => {
      switchNetwork?.(networkId);
      setShowNetworkMenu(false);
    },
    [switchNetwork],
  );

  return (
    <>
      <div className={styles.netWork}>
        <h4 className={styles.blockChainInfo}>
          {chain ? (
            <div>
              Your Network is{" "}
              <span className={styles.value}>
                {chain.unsupported ? "Unsupported" : chain.name}
              </span>
            </div>
          ) : (
            <div>Log In to continue</div>
          )}
        </h4>
      </div>
      {chain && (
        <div>
          {chains.length === 1 ? (
            chain.unsupported && (
              <motion.button
                className={styles.switchButton}
                whileTap={buttonEffect.tap}
                onClick={handleToggleNetworkMenu}
              >
                Switch to supported network
              </motion.button>
            )
          ) : (
            <>
              <motion.button
                className={styles.switchButton}
                whileTap={buttonEffect.tap}
                onClick={handleToggleNetworkMenu}
              >
                {showNetworkMenu ? "Close Networks" : "Select Network"}
              </motion.button>

              {showNetworkMenu && (
                <div
                  className={styles.modalOverlay}
                  onClick={handleToggleNetworkMenu}
                >
                  <div
                    className={styles.modalContent}
                    onClick={(e) => e.stopPropagation()} // 阻止点击内容区域关闭弹窗
                  >
                    <button
                      className={styles.closeButton}
                      onClick={handleToggleNetworkMenu}
                    >
                      ×
                    </button>
                    <h3>Select a Network</h3>
                    {chains.map((networkChain) => (
                      <div
                        key={networkChain.id}
                        className={`${styles.networkMenuItem} ${
                          networkChain.id === chain.id
                            ? styles.activeNetwork
                            : ""
                        }`}
                        onClick={() => handleSelectNetwork(networkChain.id)}
                      >
                        {networkChain.name}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          <NetworkFilter
            showAll={showAll}
            onToggleView={onToggleView}
            chainName={chain.name}
          />
        </div>
      )}
    </>
  );
};
// NetworkFilter.tsx
interface NetworkFilterProps {
  showAll: boolean;
  onToggleView: (show: boolean) => void;
  chainName: string;
}

const NetworkFilter = ({
  showAll,
  onToggleView,
  chainName,
}: NetworkFilterProps) => (
  <div className={styles.filterContainer}>
    <div
      className={`${styles.filterButton} ${showAll ? styles.active : ""}`}
      onClick={() => onToggleView(true)}
    >
      All Networks
    </div>
    <span className={styles.divider}>/</span>
    <div
      className={`${styles.filterButton} ${!showAll ? styles.active : ""}`}
      onClick={() => onToggleView(false)}
    >
      {chainName}
    </div>
  </div>
);

// MineCard.tsx
interface MineCardProps {
  mine: any;
  isUnsupported: boolean | undefined;
  onClick: () => void;
}

const MineCard = ({ mine, isUnsupported, onClick }: MineCardProps) => (
  <div
    className={`${styles.bodyWrapper} ${
      isUnsupported ? styles.unsupported : ""
    }`}
    onClick={onClick}
  >
    <div className={styles.titleContainer}>
      <div>
        <div className={styles.subtitle}>
          <h1 className={styles.mineral}>{mine.mineMineral}</h1>
          <Image
            src={locationIcon.src}
            alt="location icon"
            width={15}
            height={15}
            style={{ objectFit: "cover" }}
          />
          <h1 className={styles.location}>{mine.mineLocation}</h1>
        </div>
        <h1 className={styles.title}>{mine.name}</h1>
      </div>
    </div>
    <div className={styles.imgContainer}>
      <div className={styles.imageFrame}>
        <Image
          src={mine.mineImages[0]}
          alt={mine.name}
          width={500}
          height={400}
          style={{ objectFit: "fill" }}
        />
      </div>
      <div className={styles.information}>
        <MineInfo label="price" value={`$${mine.minePrice}`} />
        <MineInfo label="Reserve Estimate" value={`$${mine.mineStorage}`} />
      </div>
    </div>
  </div>
);

// MineInfo.tsx
const MineInfo = ({ label, value }: { label: string; value: string }) => (
  <div className={styles.logoWrapper}>
    <div>
      <h1 className={styles.title}>{label}</h1>
      <h1 className={styles.subtitle}>{value}</h1>
    </div>
  </div>
);

// Main Component
const MineListContents = ({ handleMineClick }: MineListContentsProps) => {
  const { chain } = useNetwork();
  const [showAll, setShowAll] = useState(true);
  const { chains, switchNetwork } = useSwitchNetwork({
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      toast.success("Switched to " + (chain?.name ?? "unknown network"));
    },
  });

  const sortedMines = useMemo(
    () =>
      [...minesDetails].sort((a, b) => {
        const aSupported =
          chain && a.supportedNetwork.includes(chain?.id) ? 1 : 0;
        const bSupported =
          chain && b.supportedNetwork.includes(chain?.id) ? 1 : 0;
        return bSupported - aSupported;
      }),
    [chain],
  );

  const visibleMines = useMemo(
    () =>
      sortedMines.filter(
        (mine) =>
          showAll || (chain && mine.supportedNetwork.includes(chain.id)),
      ),
    [sortedMines, showAll, chain],
  );

  const localIgnoreMineLists = ["mt", "matsa", "zephyr"];

  return (
    <>
      <MineListHeader />
      <div className={styles.scrollArea}>
        <NetworkInfo
          chain={chain}
          chains={chains}
          switchNetwork={switchNetwork}
          showAll={showAll}
          onToggleView={setShowAll}
        />
        {visibleMines.map((mine) => {
          if (mine.shortName === "hskreward") {
            return null;
          } else if (localIgnoreMineLists.includes(mine.shortName)) {
            return null;
          } else {
            return (
              <MineCard
                key={mine.name}
                mine={mine}
                isUnsupported={
                  chain && !mine.supportedNetwork.includes(chain.id)
                }
                onClick={() => handleMineClick(mine.name)}
              />
            );
          }
        })}
      </div>
    </>
  );
};

export default MineListContents;

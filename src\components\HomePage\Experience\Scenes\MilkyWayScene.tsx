import { Environment, useTexture } from "@react-three/drei";
import { useThree } from "@react-three/fiber";
import { memo } from "react";

const MilkyWayScene = () => {
  const { scene } = useThree();
  const galaxy = useTexture("/textures/canvasBg.jpg");
  scene.background = galaxy;
  return (
    <>
      {/* <mesh>
        <sphereGeometry attach="geometry" args={[1200, 800, 800]} />
        <meshBasicMaterial attach="material" map={galaxy} side={2} />
      </mesh> */}
    </>
  );
};

export default memo(MilkyWayScene);

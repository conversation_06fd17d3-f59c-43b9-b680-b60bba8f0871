@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  @include row-between;
  margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 87%;
  height: 80%;
  overflow: scroll;
  padding-right: $padding-md;
  padding-bottom: $padding-lg;

  // border: 1px solid yellow;

  &::-webkit-scrollbar {
    display: none;
  }
  .bodyWrapper {
    padding: $padding-sm 0;
    border-bottom: 1px solid #e7e7e70f;
    cursor: pointer;

    .time {
      width: fit-content;
      font-size: $font-size-xs;
      // font-weight: $font-weight-light;
      // color: $color-primary-transparent-contrast;
      color: $color-primary;
      // background: rgba(255, 255, 255, 0.1);
      background: $color-black-transparent;
      // text-transform: uppercase;
      padding: 0 $padding-xs;
      border-radius: $border-radius-sm;
    }
    .bodyText {
      font-size: $font-size-md;
      // color: $color-primary-transparent-contrast;
      color: white;
      font-weight: $font-weight-semibold;
    }

    &:hover {
      .time,
      .bodyText {
        color: $color-primary;
      }
    }
  }
}

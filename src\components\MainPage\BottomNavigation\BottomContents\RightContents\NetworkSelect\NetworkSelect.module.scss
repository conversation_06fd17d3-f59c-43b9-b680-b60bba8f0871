@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.PopoverContent {
  border: $border-width-2xs solid $color-primary;
  padding: $padding-md;
  width: 350px;
  z-index: $z-index-1;
  background-color: $color-black-transparent-dark;
  animation-duration: 500ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;

  h1 {
    width: 100%;
    text-align: center;
    color: $color-primary;
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    text-transform: uppercase;
    padding-bottom: $padding-sm;
    border-bottom: $border-width-2xs solid $color-primary-transparent;
    margin-bottom: $margin-sm;
  }

  .networkSelect {
    width: 100%;
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-sm;
    padding: $padding-sm $padding-sm;
    cursor: pointer;

    &:hover {
      background-color: $color-primary-transparent-contrast;
    }

    h2 {
      color: $color-primary;
      font-size: $font-size-md;
      font-weight: $font-weight-semibold;
    }
  }

  .PopoverClose {
    outline: none;
    border: none;
    border-radius: 100%;
    height: 25px;
    width: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: inherit;
    position: absolute;
    top: 16px;
    right: $padding-sm;
    cursor: pointer;

    &:hover {
      background-color: $color-primary-transparent-contrast;
    }
  }

  .PopoverArrow {
    fill: $color-primary;
  }
}

.PopoverContent[data-state="open"][data-side="top"] {
  animation-name: slideDownAndFade;
}
.PopoverContent[data-state="open"][data-side="right"] {
  animation-name: slideLeftAndFade;
}
.PopoverContent[data-state="open"][data-side="bottom"] {
  animation-name: slideUpAndFade;
}
.PopoverContent[data-state="open"][data-side="left"] {
  animation-name: slideRightAndFade;
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

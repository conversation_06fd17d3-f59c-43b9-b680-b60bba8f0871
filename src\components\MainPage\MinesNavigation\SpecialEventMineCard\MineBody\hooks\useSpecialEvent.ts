import { rewardAsteroid<PERSON>Abi } from "@/constants/abis/RewardAsteroidXABI";
import { hashkey, hashkeyTestnet } from "@/constants/customChains";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import React, { useLayoutEffect, useState } from "react";
import { BaseError } from "viem";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";

const useSpecialEvent = () => {
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const [rewardAddress, setRewardAddress] = useState<`0x${string}`>("0x");
  const [totalParticipants, setTotalParticipants] = useState(0n);
  const [totalReward, setTotalReward] = useState(0n);
  const [participateTime, setParticipateTime] = useState(0n);
  const [endTime, setEndTime] = useState(0n);
  const [claimableReward, setClaimableReward] = useState(0n);
  const [claimStartTime, setClaimStartTime] = useState(0n);
  const [purchaseTime, setPurchaseTime] = useState(0n);
  const [isClaiming, setIsClaiming] = useState(false);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setRewardAddress(networkConfigs[chain.id].rewardAddress);
    } else {
      setRewardAddress("0x");
    }
  }, [chain]);

  const { data: currentPeriodId } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "currentPeriodId",
    enabled:
      isConnected &&
      address !== "0x" &&
      !chain?.unsupported &&
      rewardAddress !== "0x",
    watch: true,
  });

  useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "periodInfo",
    args: [currentPeriodId ?? BigInt(0)],
    enabled:
      rewardAddress !== "0x" &&
      currentPeriodId !== undefined &&
      isConnected &&
      address !== "0x" &&
      !chain?.unsupported,
    watch: true,
    onSuccess: (data) => {
      setTotalParticipants(data.participantsCount);
      setTotalReward(data.totalReward);
      setParticipateTime(data.startTime);
      setEndTime(data.endTime);
      setClaimStartTime(data.claimStartTime);
    },
  });

  useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "userRewardInfo",
    args: [address ?? "0x"],
    enabled:
      rewardAddress !== "0x" &&
      isConnected &&
      address !== "0x" &&
      !chain?.unsupported,
    watch: true,
    onSuccess: (data) => {
      setClaimableReward(data.claimableReward);
      setPurchaseTime(data.timestamp);
    },
  });

  const { data: userRewardInfo } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "userRewardInfo",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && !!address,
    watch: true,
  });

  // Calculate user reward
  const { data: userReward } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "calculateUserReward",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && !!address,
    watch: true,
  });

  const { config: claimConfig } = usePrepareContractWrite({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "claim",
    enabled:
      isConnected &&
      !chain?.unsupported &&
      !!userRewardInfo &&
      !userRewardInfo.hasClaimed &&
      (userReward ?? 0n) > 0n,
    onError: (error) => {
      // console.error("Prepare claim error:", error);
    },
  });

  const { write: claimWrite, data: claimData } = useContractWrite({
    ...claimConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const { isLoading: isWaitingForClaim } = useWaitForTransaction({
    hash: claimData?.hash,
    onSuccess: () => {
      showSuccess("Successfully claimed HSK rewards!");
      setIsClaiming(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const handleClaim = async () => {
    if (!isConnected) {
      showError("Please connect your wallet first");
      return;
    }

    if (chain?.unsupported) {
      showError("Please switch to a supported network");
      return;
    }

    const currentTimestamp = BigInt(Math.floor(Date.now() / 1000));
    if (currentTimestamp < claimStartTime) {
      showError("Claim has not started yet");
      return;
    }

    if (!claimWrite) {
      if (userRewardInfo?.hasClaimed) {
        showError("You have already claimed your rewards");
        return;
      }

      if ((userReward ?? 0n) <= 0n) {
        showError("No rewards available to claim");
        return;
      }

      showError("Unable to claim rewards at this time");
      return;
    }

    setIsClaiming(true);
    try {
      claimWrite?.();
    } catch (error) {
      setIsClaiming(false);
      showError("Failed to claim rewards");
    }
  };

  return {
    totalParticipants,
    totalReward,
    participateTime,
    claimableReward,
    endTime,
    claimStartTime,
    isClaiming,
    isWaitingForClaim,
    handleClaim,
    userRewardInfo,
    userReward,
    purchaseTime,
  };
};

export default useSpecialEvent;

import {
  getPointsDataServer,
  getTotalUsersServer,
  getUserTaskServer,
} from "@/actions/getPointsDataServer";
import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export type PointsData = Awaited<ReturnType<typeof getPointsDataServer>>;
export type TUserTask = Awaited<ReturnType<typeof getUserTaskServer>>;

export const pointsDataStore = proxy({
  pointsData: {
    pointsItems: [],
    total: 0,
    userPoints: 0,
    percent: 0,
    rank: "",
  } as PointsData,
  setPointsData: (newPointsData: PointsData) => {
    pointsDataStore.pointsData = newPointsData;
  },
  fetchPointsData: async (address: string) => {
    const pointsData = await getPointsDataServer(address);
    pointsDataStore.setPointsData(pointsData);
  },

  totalUsers: 0,
  fetchTotalUsers: async () => {
    const totalUsers = await getTotalUsersServer();
    pointsDataStore.totalUsers = totalUsers;
  },

  userTasks: [] as TUserTask,
  fetchUserTasks: async (address: string) => {
    const userTasks = await getUserTaskServer(address);
    pointsDataStore.userTasks = userTasks;
  },
});

devtools(pointsDataStore, {
  name: "pointsDataStore",
  enabled: false,
});

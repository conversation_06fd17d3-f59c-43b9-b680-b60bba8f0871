@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.wrapper {
  width: 100%;
  height: 100%;
  // position: relative;
  background: $color-black-transparent-light;
  box-shadow:
    0 0 10px $color-primary,
    0 0 20px $color-primary-transparent;
  backdrop-filter: blur(10px);
  padding: $padding-lg;
  mask-image: linear-gradient(to bottom, black 85%, transparent);
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    background: linear-gradient(
      to bottom,
      $color-primary,
      $color-black-transparent
    );
    // box-shadow: 0 0 10rem 10rem $color-primary;
    box-shadow:
      0 0 10px $color-primary,
      0 0 20px $color-primary-transparent;
    clip-path: polygon(
      0 0,
      calc(100% - 2rem) 0,
      100% 2rem,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      0.3rem calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 0.2rem) calc(2rem + 0.83px),
      calc(100% - 2rem - 0.83px) 1px,
      1px 1px
    );
  }
}

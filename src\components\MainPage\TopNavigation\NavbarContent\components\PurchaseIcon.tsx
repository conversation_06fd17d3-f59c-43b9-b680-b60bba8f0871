import variables from "@/styles/variables.module.scss";

const PurchaseIcon = ({ color = variables.colorPrimaryContrast }) => {
  return (
    <svg
      width="56"
      height="56"
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Frame">
        <path
          id="Vector"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7 17.5H49L46.6667 49H9.33333L7 17.5Z"
          stroke={color}
          strokeWidth="3"
          strokeLinejoin="round"
        />
        <path
          id="Vector_2"
          d="M18.6667 22.1667V7H37.3333V22.1667"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          id="Vector_3"
          d="M18.6667 39.6667H37.3333"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};

export default PurchaseIcon;

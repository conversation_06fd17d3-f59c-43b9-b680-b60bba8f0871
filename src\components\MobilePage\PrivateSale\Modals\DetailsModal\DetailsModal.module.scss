.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000; // 增加 z-index 确保在最上层
}

.modal {
  position: fixed;
  width: 85%;
  padding: 1.5rem;
  margin: 0; // 清除可能的外边距
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) !important; // 强制居中
  background: #001a1a;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1001; // 确保模态框在遮罩层之上

  &::-webkit-scrollbar {
    display: none;
  }
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #00ffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;

  &:hover {
    color: white;
  }
}

.content {
  .description {
    color: #ffffff;
    margin-bottom: 1rem;
    line-height: 1.6;

    strong {
      color: #00ffff;
    }
  }

  .navigationLinks {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;

    .navLink {
      padding: 0.6rem 1rem;
      border-radius: 8px;
      background: rgba(0, 255, 255, 0.1);
      color: #00ffff;
      font-size: 0.8rem;
      font-weight: 600;
      text-decoration: none;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      border: 1px solid rgba(0, 255, 255, 0.2);

      &:hover {
        background: rgba(0, 255, 255, 0.2);
        transform: translateY(-2px);
      }
    }
  }
}

.saleInfoContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 0;
}

.infoRow {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .value {
    font-size: 1rem;
    color: #00ffff;
    font-weight: 500;
  }
}

.divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(0, 255, 255, 0.3),
    transparent
  );
  margin: 0.5rem 0; // 增加上下间距
  position: relative;

  &::before {
    // 添加发光效果
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      to right,
      transparent,
      rgba(0, 255, 255, 0.1),
      transparent
    );
    filter: blur(2px);
  }
}

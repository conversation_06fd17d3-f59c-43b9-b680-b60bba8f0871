import { proxy } from "valtio";
import { DownloadableFile } from "@/constants/mineDetails";

interface DownloadModalState {
  isOpen: boolean;
  selectedFiles: Set<string>;
  isDownloading: boolean;
  downloadProgress: number;
  selectedMine: string;
}

interface DownloadModalActions {
  setIsOpen: (isOpen: boolean) => void;
  toggleFileSelection: (fileId: string) => void;
  selectAllFiles: (fileIds: string[]) => void;
  clearSelection: () => void;
  setIsDownloading: (isDownloading: boolean) => void;
  setDownloadProgress: (progress: number) => void;
  setSelectedMine: (mine: string) => void;
  reset: () => void;
}

export type DownloadModalStore = DownloadModalState & DownloadModalActions;

const initialState: DownloadModalState = {
  isOpen: false,
  selectedFiles: new Set<string>(),
  isDownloading: false,
  downloadProgress: 0,
  selectedMine: "",
};

export const downloadModalStore = proxy<DownloadModalStore>({
  ...initialState,

  setIsOpen: (isOpen: boolean) => {
    downloadModalStore.isOpen = isOpen;
    if (!isOpen) {
      downloadModalStore.reset();
    }
  },

  toggleFileSelection: (fileId: string) => {
    const newSelection = new Set(downloadModalStore.selectedFiles);
    if (newSelection.has(fileId)) {
      newSelection.delete(fileId);
    } else {
      newSelection.add(fileId);
    }
    downloadModalStore.selectedFiles = newSelection;
  },

  selectAllFiles: (fileIds: string[]) => {
    const allSelected = fileIds.every((id) =>
      downloadModalStore.selectedFiles.has(id),
    );
    if (allSelected) {
      // Deselect all
      downloadModalStore.selectedFiles = new Set();
    } else {
      // Select all
      downloadModalStore.selectedFiles = new Set(fileIds);
    }
  },

  clearSelection: () => {
    downloadModalStore.selectedFiles = new Set();
  },

  setIsDownloading: (isDownloading: boolean) => {
    downloadModalStore.isDownloading = isDownloading;
    if (!isDownloading) {
      downloadModalStore.downloadProgress = 0;
    }
  },

  setDownloadProgress: (progress: number) => {
    downloadModalStore.downloadProgress = Math.max(0, Math.min(100, progress));
  },

  setSelectedMine: (mine: string) => {
    downloadModalStore.selectedMine = mine;
    downloadModalStore.clearSelection();
  },

  reset: () => {
    downloadModalStore.selectedFiles = new Set();
    downloadModalStore.isDownloading = false;
    downloadModalStore.downloadProgress = 0;
  },
});

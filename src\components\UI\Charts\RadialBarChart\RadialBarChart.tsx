import {
  Radial<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dial<PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Legend,
  Tooltip,
} from "recharts";
import { useEffect, useState } from "react";

interface RadialBarChartData {
  name: string;
  value: number;
  fill: string;
}

interface RadialBarChartProps {
  data: RadialBarChartData[];
  height?: number;
  innerRadius?: string;
  outerRadius?: string;
  showLegend?: boolean;
}

const CustomRadialBarChart = ({
  data,
  height = 300,
  innerRadius = "20%",
  outerRadius = "70%",
  showLegend = true,
}: RadialBarChartProps) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div
          style={{
            backgroundColor: "#1a1a1a",
            border: "1px solid #333",
            borderRadius: "8px",
            color: "#ffffff",
            fontSize: "14px",
            padding: "12px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          }}
        >
          <p
            style={{
              color: "#ffffff",
              fontWeight: "bold",
              margin: "0 0 4px 0",
            }}
          >
            {data.name}
          </p>
          <p style={{ margin: 0 }}>{data.value} NFTs</p>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }: any) => {
    if (isMobile) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-around', 
          width: '100%',
          marginTop: '10px',
          fontSize: '14px'
        }}>
          {payload.map((entry: any, index: number) => (
            <div key={`item-${index}`} style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ 
                width: '10px', 
                height: '10px', 
                backgroundColor: entry.color,
                borderRadius: '50%',
                marginRight: '5px' 
              }} />
              <span>{entry.value}</span>
            </div>
          ))}
        </div>
      );
    }
    
    return (
      <ul style={{ 
        listStyle: 'none', 
        padding: 0,
        position: 'absolute',
        right: 0,
        top: '50%',
        transform: 'translateY(-50%)'
      }}>
        {payload.map((entry: any, index: number) => (
          <li key={`item-${index}`} style={{ 
            display: 'flex', 
            alignItems: 'center',
            margin: '5px 0' 
          }}>
            <div style={{ 
              width: '10px', 
              height: '10px', 
              backgroundColor: entry.color,
              borderRadius: '50%',
              marginRight: '5px' 
            }} />
            <span>{entry.value}</span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <RadialBarChart
        cx={isMobile ? "50%" : "40%"}
        cy="50%"
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        data={data}
        margin={isMobile ? 
          { top: 10, right: 20, bottom: 60, left: 20 } : 
          { top: 20, right: 80, bottom: 20, left: 20 }
        }
      >
        <RadialBar dataKey="value" cornerRadius={10} fill="#8884d8" />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && (
          <Legend 
            content={<CustomLegend />}
            iconType="circle"
            layout={isMobile ? "horizontal" : "vertical"}
            align={isMobile ? "center" : "right"}
            verticalAlign={isMobile ? "bottom" : "middle"}
          />
        )}
      </RadialBarChart>
    </ResponsiveContainer>
  );
};

export default CustomRadialBarChart;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.wrapper {
  width: 100%;
  height: 100%;
  // background: $color-black-transparent-light;
  backdrop-filter: blur(10px);
  // mask-image: linear-gradient(to bottom, black 65%, transparent);
  padding: $padding-lg;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    background: linear-gradient(
      to bottom,
      $color-primary,
      $color-black-transparent
    );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 2rem) 0,
      100% 2rem,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      0.3rem calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 0.2rem) calc(2rem + 0.83px),
      calc(100% - 2rem - 0.83px) 1px,
      1px 1px
    );
  }
}

.headerWrapper {
  @include row-between;
  margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 87%;
  height: 82%;
  overflow: scroll;
  // background: $color-primary-transparent;
  padding-right: $padding-md;

  backdrop-filter: blur(30px);
  @include row-center;
  &::-webkit-scrollbar {
    display: none;
  }
  // border: 1px solid yellow;

  // align-items: flex-end;

  // .formContainer {
  //   width: 100%;
  //   height: 100%;
  //   border-radius: 1rem;
  //   padding: 1rem 3rem;
  //   // margin-top: 6rem;
  //   background: $color-black-transparent;
  //   @include col-center;

  .formDataGroup {
    width: 100%;
    // @include row-center;
    // gap: 6rem;
  }

  .formDataContainer {
    width: 100%;
    height: 100%;
    // border: 1px solid yellow;
    overflow: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    .submitButton {
      @include row-center;
      width: 100%;
      text-align: center;
      font-size: $font-size-sm;
      font-family: $font-family-poppins;
      // color: $color-primary;
      color: $color-warning;
      text-transform: uppercase;
      font-weight: $font-weight-semibold;
      border: $border-width-xs solid $color-primary-contrast;
      padding: $padding-sm 0;
      margin-bottom: $margin-sm;
      background: $color-primary-transparent;
      backdrop-filter: blur(5px);
      border-radius: $border-radius-sm;
      cursor: pointer;

      span {
        color: $color-primary-transparent-contrast;
      }

      &:hover {
        border: $border-width-xs solid $color-primary;
      }
    }

    .formSection {
      @include col-center;
      align-items: flex-start;
      width: 100%;
      margin-bottom: 1rem;

      label {
        color: $color-primary;
        font-weight: $font-weight-semibold;
        font-size: $font-size-lg;
      }

      input,
      select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid grey;
        border-radius: 0.25rem;
        background: transparent;
        color: white;

        &:focus {
          outline: $color-warning solid 3px;
        }
        option {
          color: black;
          // background-color: $color-black-transparent;
        }
      }

      p {
        color: $color-danger;
        font-size: $font-size-sm;
      }

      input::file-selector-button {
        text-transform: uppercase;
        font-weight: bold;
        color: dodgerblue;
        padding: 0.5em;
        border: 0px solid grey;
        border-radius: 3px;
        background: black;
        cursor: pointer;
      }

      .currentValuation {
        width: 100%;
        @include row-between;

        span {
          margin-right: 0.5rem;
          font-size: $font-size-xl;
        }
      }
    }
  }
  // }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$content-height: 94%;

.container {
  width: 100%;
  height: 100%;
  // border: 1px solid yellow;
  @include row-between;
  gap: $spacing-md;

  .buyerInfo {
    width: 282px;
    height: 100%;
    padding: $padding-md 0;
    // border: 1px solid yellow;
    @include col-between;
    justify-content: flex-start;
    gap: $spacing-sm;

    .headerWrapper {
      width: 100%;

      @include row-between;
      // margin-bottom: $margin-sm;
      .header {
        line-height: 108%;
        text-transform: uppercase;

        .title {
          font-size: $font-size-2xl;
          color: $color-primary;
          font-weight: $font-weight-semibold;
        }
        .subTitle {
          font-size: $font-size-sm;
          color: $color-primary-transparent-contrast;
          font-weight: $font-weight-light;
        }
      }
    }

    .scrollArea {
      width: 100%;
      // height: 89%;
      height: 100%;
      overflow: scroll;
      scroll-behavior: smooth;
      // margin: 1rem 0;
      // border: 1px solid yellow;

      &::-webkit-scrollbar {
        display: none;
      }

      .leaderboardCard {
        padding: 1rem 0;
        border-bottom: 1px solid $color-primary-contrast;

        .position {
          color: $color-primary;
          font-size: $font-size-2xl;
          font-weight: $font-weight-extrabold;
        }

        .points {
          @include row-between;
          color: $color-primary;
          font-weight: $font-weight-light;
        }
      }
    }
  }

  .verticalLine {
    width: 30px;
    height: 100%;
    @include row-center;
  }
  .mainContent {
    flex: 1;
    height: 100%;
    // border: 1px solid yellow;
    padding: $padding-md $padding-md $padding-xl;
  }
}

import styles from "./PointSystemModalHeader.module.scss";
import variables from "@/styles/variables.module.scss";
import lineIconLight from "@/assets/icons/plusModal/lineIconLight.png";
import lineIconMedium from "@/assets/icons/plusModal/lineIconMedium.png";
import lineIconDark from "@/assets/icons/plusModal/lineIconDark.png";
import { useState } from "react";
import { plusModalStore } from "@/stores/plusModal";
import { useSnapshot } from "valtio";

const PointSystemModalHeader = () => {
  // const [selectedTitle, setSelectedTitle] = useState(headerDetails[0].title);
  const plusModalSnapshot = useSnapshot(plusModalStore);

  // console.log(isTitleSelected);
  return (
    <div className={styles.container}>
      <h1 className={styles.overview}>overview</h1>
      <div className={styles.mineralInfo}>
        <img src={lineIconLight.src} alt="light line icon" />
        <img src={lineIconMedium.src} alt="medium line icon" />
        <img src={lineIconDark.src} alt="dark line icon" />
        <div>
          <h1>Asteroid X Airdrop</h1>
          <h2>points system</h2>
        </div>
        {/* <div className={styles.claimButton}>
          <button>claim your reward</button>
        </div> */}
      </div>
    </div>
  );
};

export const headerDetails = [
  { title: "mineral information", subtitle: "details about this mine" },
  { title: "the company", subtitle: "details about this company" },
  { title: "investor zone", subtitle: "details about investment" },
];

export default PointSystemModalHeader;

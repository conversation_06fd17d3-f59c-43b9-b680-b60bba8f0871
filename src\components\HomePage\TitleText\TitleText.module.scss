@import "@/styles/variables.module.scss";

.gradientText {
  animation: background-pan 2s linear infinite;
  background: linear-gradient(
    to right,
    $color-primary,
    $color-primary-contrast,
    $color-primary
  );
  background-clip: text;
  background-size: 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: $font-weight-bold;
  font-size: $font-size-6xl;
  text-align: center;
}

@keyframes background-pan {
  from {
    background-position: 0% center;
  }

  to {
    background-position: -200% center;
  }
}

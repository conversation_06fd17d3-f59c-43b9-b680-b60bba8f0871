import { bottomUpFadeIn, fadeIn } from "@/animations/animations";
import styles from "./DepositsSection.module.scss";
import { motion } from "framer-motion";

const DepositsSection = () => {
  return (
    <motion.div
      className={styles.container}
      variants={fadeIn(1)}
      initial="hidden"
      animate="visible"
    >
      <motion.h1 className={styles.textTitle} variants={bottomUpFadeIn(1)}>
        COMING SOON
      </motion.h1>
    </motion.div>
  );
};

export default DepositsSection;

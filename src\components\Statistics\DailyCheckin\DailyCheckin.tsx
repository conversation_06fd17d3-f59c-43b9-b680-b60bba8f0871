import styles from "./DailyCheckin.module.scss";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import {
  checkinUserServer,
  getUserCheckinStatusServer,
} from "@/actions/getPointsDataServer";
import { useAccount } from "wagmi";
import { useEffect, useState } from "react";
import { showError, showSuccess } from "@/lib/notification";
import CountdownTimer from "./CountdownTimer/CountdownTimer";
import { pointsDataStore } from "@/stores/pointsData";
const DailyCheckin = () => {
  const { address } = useAccount();
  const [isCheckinAvaliable, setIsCheckinAvaliable] = useState(false);
  const [isCheckingin, setIsCheckingin] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);

  const countdownTimerProps = {
    remainingTime,
    setIsCheckinAvaliable,
  };

  useEffect(() => {
    if (address) {
      getUserCheckinStatusServer(address).then((data) => {
        if (data.canCheckin === true) {
          setIsCheckinAvaliable(true);
        } else {
          setIsCheckinAvaliable(false);
        }
        setRemainingTime(data.nextCheckinSeconds);
      });
    }
  }, [address]);

  const checkinUser = async () => {
    if (address) {
      setIsCheckingin(true);
      const { code, data } = await checkinUserServer(address);
      if (code === 0) {
        showSuccess("Checkin successful");
        setIsCheckinAvaliable(false);
      } else {
        showError("Checkin failed, contact support");
      }
      setRemainingTime(data.nextCheckinSeconds);
      setIsCheckingin(false);

      await Promise.all([
        pointsDataStore.fetchPointsData(address),
        pointsDataStore.fetchUserTasks(address),
      ]);
    }
  };

  return (
    <>
      {isCheckinAvaliable ? (
        <div className={styles.checkinSection}>
          <h1>asteroidx daily checkin</h1>
          <h3>Earn Extra 5 points</h3>
          <motion.button
            className={styles.checkinButton}
            whileTap={buttonEffect.tap}
            onClick={checkinUser}
            disabled={isCheckingin}
          >
            Check In
          </motion.button>
        </div>
      ) : (
        <CountdownTimer {...countdownTimerProps} />
      )}
    </>
  );
};

export default DailyCheckin;

import { useAccount, useNetwork } from "wagmi";
import TradeModal from "../TradeModal/TradeModal";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import { navbarButtonDetails } from "../TopNavigation/NavbarContent/NavbarContent";
import { useEffect } from "react";
import { showError } from "@/lib/notification";

const PurchaserSection = () => {
  const { isConnected } = useAccount();
  const { chain } = useNetwork();
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);

  const switchToMainPage = () => {
    navbarButtonSnapshot.setSelectedButton(navbarButtonDetails[0].title);
  };

  useEffect(() => {
    if (!isConnected) {
      showError("Please Connect Wallet");
      switchToMainPage();
      return;
    }
    if (chain?.unsupported) {
      showError("Please Switch Network");
      switchToMainPage();
      return;
    }
  }, [isConnected, chain]);

  return (
    <TradeModal />
    // <motion.div
    //   className={styles.container}
    //   variants={fadeIn(1)}
    //   initial="hidden"
    //   animate="visible"
    // >
    //   <motion.h1 className={styles.textTitle} variants={bottomUpFadeIn(1)}>
    //     COMING SOON
    //   </motion.h1>
    // </motion.div>
  );
};

export default PurchaserSection;

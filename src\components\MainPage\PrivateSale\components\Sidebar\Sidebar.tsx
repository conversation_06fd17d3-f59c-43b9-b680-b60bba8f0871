import styles from "./Sidebar.module.scss";
import asteroidXIcon from "../../assets/asteroidXIcon.png";
import facebookIcon from "../../assets/facebookIcon.png";
import twitterIcon from "../../assets/twitterIcon.png";
import instagramIcon from "../../assets/instagramIcon.png";
import discord from "../../assets/discordIcon.png";
import Link from "next/link";

const Sidebar = () => {
  const Icons = ({
    src,
    alt,
    link,
  }: {
    src: string;
    alt: string;
    link: string;
  }) => (
    <Link className={styles.iconWrapper} href={link} target="_blank">
      <img src={src} alt={alt} width={32} height={32} />
    </Link>
  );

  return (
    <div className={styles.container}>
      {/* icon bar */}
      <div className={styles.iconsGroup}>
        <div className={styles.logoContainer}>
          <Icons src={asteroidXIcon.src} alt="asteroidXIcon" link="#" />
        </div>
        <div className={styles.socialIcons}>
          {/* <Icons src={facebookIcon.src} alt="facebookIcon" link="#" /> */}
          <Icons
            src={twitterIcon.src}
            alt="twitterIcon"
            link="https://x.com/Asteroid_AU"
          />
          {/* <Icons src={instagramIcon.src} alt="instagramIcon" link="#" /> */}
          <Icons
            src={discord.src}
            alt="discordIcon"
            link="https://discord.com/invite/fCgtggqjhA"
          />
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

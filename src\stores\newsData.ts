import { getNewsData } from "@/actions/getNewsData";
import { getNewsDataFromOwnServer } from "@/actions/getNewsDataFromOwnServer";
import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export type NewsData = Awaited<ReturnType<typeof getNewsDataFromOwnServer>>;

export const newsDataStore = proxy({
  newsData: [] as NewsData,
  setNewsData: (newNewsData: NewsData) => {
    newsDataStore.newsData = newNewsData;
  },
  fetchNewsData: async () => {
    const newData = await getNewsDataFromOwnServer();
    newsDataStore.setNewsData(newData);
  },
});

devtools(newsDataStore, {
  name: "newsDataStore",
  enabled: false,
});

import styles from "./CurrentPrice.module.scss";
import { useState } from "react";
import TokenPrice from "@/components/common/TokenPrice";
import useActiveListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useActiveListing";
import usePurchaseAllListingAmount from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/usePurchaseAllListingAmount";
import useApprove from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useApprove";
import BuyListingModal from "./Modals/BuyListingModal";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import { mineCardStore } from "@/stores/mineCard";

const Listings = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded((prevState) => !prevState);
  };
  const {
    allActiveListingSortedByPriceCombined,
    setFilterValue,
    allActiveListingSortedByPriceFiltered,
  } = useActiveListing();
  const [isBuyListingModalOpen, setIsBuyListingModalOpen] = useState(false);
  const {
    isPurchasingListing,
    isWaitingForPurchaseListing,
    purchaseListing,
    setAmount,
    setListingId,
    setPricePerToken,
    listingId,
  } = usePurchaseAllListingAmount();
  const { isApprovedForAll } = useApprove();
  // 新增分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // 计算分页数据
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems =
    allActiveListingSortedByPriceCombined?.slice(startIndex, endIndex) || [];
  const totalPages = allActiveListingSortedByPriceCombined
    ? Math.ceil(allActiveListingSortedByPriceCombined.length / itemsPerPage)
    : 0;

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <div className={styles.container}>
      <div className={styles.listings}>
        <div
          className={`${styles.mainHeader} ${
            isExpanded ? styles.expanded : styles.collapsed
          }`}
          onClick={handleToggle}
        >
          <span className={styles.glowText}>LISTINGS</span>
          <span className={styles.buttonHighlight}></span>
        </div>
        {isExpanded && (
          <>
            <div className={styles.listingsTable}>
              <div className={styles.listingTableHeader}>
                <div>Price per Token</div>
                <div>Quantity</div>
                <div>Selling Price</div>
                {/* <div>Expiration</div> */}
                <div>Action</div>
              </div>

              <div className={styles.tableBody}>
                {allActiveListingSortedByPriceCombined &&
                allActiveListingSortedByPriceCombined.length > 0 ? (
                  currentItems.map((listing, index) => (
                    <div className={styles.listingTableRow} key={index}>
                      <div className={styles.price}>
                        <TokenPrice
                          amount={listing.pricePerToken}
                          tokenAddress={listing.paymentToken}
                          showCurrency={true}
                          currencySymbol={selectedMine.currency}
                        />
                      </div>
                      <div className={styles.quantity}>
                        {listing.amount.toString()}
                      </div>
                      <div className={styles.price}>
                        <TokenPrice
                          amount={listing.pricePerToken * listing.amount}
                          tokenAddress={listing.paymentToken}
                          showCurrency={true}
                          currencySymbol={selectedMine.currency}
                        />
                      </div>
                      {/* <div className={styles.expiration}>
                    {formatUnixTimestamp(Number(listing.expirationTime))}
                  </div> */}
                      <div>
                        <button
                          className={`${styles.buyButton} ${
                            isPurchasingListing || isWaitingForPurchaseListing
                              ? styles.disabled
                              : ""
                          }`}
                          onClick={() => {
                            setFilterValue(listing.pricePerToken);
                            setIsBuyListingModalOpen(true);
                          }}
                          disabled={
                            isPurchasingListing ||
                            isWaitingForPurchaseListing ||
                            !isApprovedForAll
                          }
                          style={{
                            opacity:
                              isPurchasingListing ||
                              isWaitingForPurchaseListing ||
                              !isApprovedForAll
                                ? 0.5
                                : 1,
                            pointerEvents:
                              isPurchasingListing ||
                              isWaitingForPurchaseListing ||
                              !isApprovedForAll
                                ? "none"
                                : "auto",
                          }}
                        >
                          {(isPurchasingListing ||
                            isWaitingForPurchaseListing) &&
                          listingId === listing.listingId
                            ? "Processing..."
                            : "BUY"}
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className={styles.noListings}>
                    No active listings available
                  </div>
                )}
              </div>
            </div>
            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={styles.pageButton}
                >
                  ← Previous
                </button>

                <span className={styles.pageInfo}>
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={styles.pageButton}
                >
                  Next →
                </button>
              </div>
            )}
          </>
        )}
      </div>
      <BuyListingModal
        isOpen={isBuyListingModalOpen}
        onClose={() => setIsBuyListingModalOpen(false)}
        allActiveListingSortedByPriceFiltered={
          allActiveListingSortedByPriceFiltered
        }
      />
    </div>
  );
};

export default Listings;

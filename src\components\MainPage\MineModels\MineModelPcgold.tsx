import styles from "./MineModel.module.scss";
import ReactPannellum from "react-pannellum";
import projectBackground from "./modelImages/img.jpeg";
import { useMediaQuery } from "usehooks-ts";

const MineModelPcgold = () => {
  const isMobile = useMediaQuery("(max-width: 1279px)");

  return (
    <div className={styles.container}>
      <ReactPannellum
        id={projectBackground.src}
        sceneId={projectBackground.src}
        imageSource={projectBackground.src}
        config={{
          autoLoad: true,
          compass: true,
          title: "PCGOLD",
          description: isMobile
            ? "<div style='font-size: 12px; background: transparent;'>Premier gold mining operation with advanced extraction facilities and state-of-the-art processing plant.</div>"
            : "Premier gold mining operation with advanced extraction facilities and state-of-the-art processing plant.",
          autoRotate: 0.8,
          showControls: true,
          yaw: 100,
          mouseZoom: false,
          hotSpots: [
            {
              type: "info",
              text: "Main Shaft Entrance",
              pitch: -10,
              yaw: 0,
            },
            {
              type: "info",
              text: "Processing Plant",
              pitch: -15,
              yaw: 10,
            },
            {
              type: "info",
              text: "Exploration Site",
              pitch: -5,
              yaw: 20,
            },
            {
              type: "info",
              text: "Equipment Yard",
              pitch: -8,
              yaw: -15,
            },
            {
              type: "info",
              text: "Administration Building",
              pitch: 0,
              yaw: 30,
            },
          ],
        }}
        className={styles.screen}
      />
    </div>
  );
};

export default MineModelPcgold;

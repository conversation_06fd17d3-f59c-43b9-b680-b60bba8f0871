<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Bitget Wallet</title>
    <defs>
        <linearGradient id="bitgetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#00F2FE" />
            <stop offset="50%" stop-color="#4F5BD5" />
            <stop offset="100%" stop-color="#FFFFFF" />
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="bitget-wallet">
            <rect id="Background" fill="url(#bitgetGradient)" x="0" y="0" width="40" height="40" rx="12"></rect>
            <path d="M11,20 L18,13 L18,17 L15,20 L18,23 L18,27 L11,20 Z" id="LeftArrow" fill="#000000"/>
            <path d="M29,20 L22,13 L22,17 L25,20 L22,23 L22,27 L29,20 Z" id="RightArrow" fill="#000000"/>
        </g>
    </g>
</svg> 
import Barchart3d from "@/components/UI/Charts/Barchart3d/Barchart3d";
import { MinePurchased } from "@/components/MainPage/LaunchSection/PurchaseHistory/hooks/usePurchaseHistory";
import styles from "./PurchasedPortion.module.scss";
import CustomRadialBarChart from "@/components/UI/Charts/RadialBarChart/RadialBarChart";

const PurchasedPortion = ({
  tokenDetails,
  mineDetails,
}: {
  tokenDetails: MinePurchased["tokenDetails"];
  mineDetails: MinePurchased["mineDetails"];
}) => {
  const formatBlockchainData = () => {
    const userBalance = Number(tokenDetails.currentUserBalance);
    const othersBalance =
      Number(tokenDetails.soldAmount) / Number(tokenDetails.minAmount) -
      userBalance;
    const avaliableBalance =
      // Number(tokenDetails.totalSupply) - othersBalance - userBalance;
      mineDetails.manualTotalSupply - othersBalance - userBalance;

    return { userBalance, othersBalance, avaliableBalance };
  };

  const { userBalance, othersBalance, avaliableBalance } =
    formatBlockchainData();

  // const data = [
  //   ["Portion", "Amount"],
  //   ["Yours", userBalance],
  //   ["Others", othersBalance],
  //   ["Avaliable", avaliableBalance],
  // ];

  const data = [
    {
      name: `Yours`,
      value: userBalance,
      fill: "#8884d8",
    },
    {
      name: "Others",
      value: othersBalance,
      fill: "#82ca9d",
    },
    {
      name: "Available",
      value: avaliableBalance,
      fill: "#ffc658",
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.userBalanceSection}>
        <div className={styles.balanceLabel}>Your Balance:</div>
        <div className={styles.balanceValue}>
          {userBalance.toLocaleString()} Shares
        </div>
        <div className={styles.balancePercentage}>
          {(
            (userBalance / (userBalance + othersBalance + avaliableBalance)) *
            100
          ).toFixed(2)}
          % of Total
        </div>
      </div>
      {/* <Barchart3d data={data} backgroundColor="transparent" /> */}
      <CustomRadialBarChart data={data} height={300} />
    </div>
  );
};

export default PurchasedPortion;

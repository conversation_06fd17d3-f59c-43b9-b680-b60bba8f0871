import Barchart3d from "@/components/UI/Charts/Barchart3d/Barchart3d";
import { MinePurchased } from "@/components/MainPage/LaunchSection/PurchaseHistory/hooks/usePurchaseHistory";
import styles from "./PurchasedPortion.module.scss";

const PurchasedPortion = ({
  tokenDetails,
  mineDetails,
}: {
  tokenDetails: MinePurchased["tokenDetails"];
  mineDetails: MinePurchased["mineDetails"];
}) => {
  const formatBlockchainData = () => {
    const userBalance = Number(tokenDetails.currentUserBalance);
    const othersBalance =
      Number(tokenDetails.soldAmount) / Number(tokenDetails.minAmount) -
      userBalance;
    const avaliableBalance =
      // Number(tokenDetails.totalSupply) - othersBalance - userBalance;
      mineDetails.manualTotalSupply - othersBalance - userBalance;

    return { userBalance, othersBalance, avaliableBalance };
  };

  const { userBalance, othersBalance, avaliableBalance } =
    formatBlockchainData();

  const data = [
    ["Portion", "Amount"],
    ["Yours", userBalance],
    ["Others", othersBalance],
    ["Avaliable", avaliableBalance],
  ];
  return (
    <div className={styles.container}>
      <Barchart3d data={data} backgroundColor="transparent" />
    </div>
  );
};

export default PurchasedPortion;

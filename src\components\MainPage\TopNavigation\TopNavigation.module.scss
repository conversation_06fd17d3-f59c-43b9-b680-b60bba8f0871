@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(-3deg) rotateY(0deg);

.container {
  position: absolute;
  z-index: $z-index-1;
  right: 0;
  top: 0;
  width: 100%;
  gap: $spacing-lg;
  // border: 2px solid yellow;
  @include col-center;

  .trapezoid {
    width: 1755px;
    height: 104px;
    @include row-center;
    position: relative;
    -webkit-perspective: 100px;
    perspective: 100px;

    &::after {
      position: absolute;
      width: 100%;
      height: 100%;
      background: $color-black-transparent;
      backdrop-filter: blur(5px);
      border: $border-width-xs solid $color-primary;
      border-top: none;
      content: "";
      left: 0;
      top: 0;
      z-index: -1;
      -webkit-transform: $rotation-angle;
      transform: $rotation-angle;
    }
  }

  .marketDataContainer {
    width: 1600px;
    height: 28px;
    border: $border-width-xs solid $color-primary-transparent-contrast;
    background: $color-black-transparent;
    backdrop-filter: blur(5px);
    @include row-center;
    padding: 0 $padding-sm;

    .testnetBanner {
      color: $color-warning;
      font-size: 16px;
      text-align: center;

      .testnetLink {
        color: $color-primary;
        text-decoration: underline;
        cursor: pointer;

        &:hover {
          color: $color-primary-contrast;
        }
      }
    }
  }
}

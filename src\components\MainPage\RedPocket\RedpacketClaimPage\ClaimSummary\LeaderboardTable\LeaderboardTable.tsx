import { useAccount } from "wagmi";
import useRedpacket from "../../../hooks/useRedpacket";
import styles from "./LeaderboardTable.module.scss";

const LeaderboardTable = () => {
  const {
    topParticipants,
    totalParticipants,
    currentPage,
    setCurrentPage,
    pageSize,
  } = useRedpacket();
  const { address } = useAccount();

  const isCurrentUser = (entryAddress: string) => {
    if (!address) return false;
    return entryAddress === `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const totalPages = Math.ceil(totalParticipants / pageSize);

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className={styles.tableWrapper}>
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>Rank</th>
              <th>Address</th>
              <th>Amount</th>
              <th>Claim Time</th>
            </tr>
          </thead>
          <tbody>
            {topParticipants.map((entry) => (
              <tr
                key={entry.rank}
                className={
                  isCurrentUser(entry.address) ? styles.currentUser : ""
                }
              >
                <td>{entry.rank}</td>
                <td>
                  {entry.address}
                  {isCurrentUser(entry.address) && (
                    <span className={styles.youLabel}> (YOU)</span>
                  )}
                </td>
                <td>{entry.nftAmount}</td>
                <td>{entry.claimTime}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button
            onClick={handlePrevPage}
            disabled={currentPage === 0}
            className={styles.pageButton}
          >
            Previous
          </button>
          <span className={styles.pageInfo}>
            Page {currentPage + 1} of {totalPages}
          </span>
          <button
            onClick={handleNextPage}
            disabled={currentPage >= totalPages - 1}
            className={styles.pageButton}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default LeaderboardTable;

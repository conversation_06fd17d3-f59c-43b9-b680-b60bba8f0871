import toast from "react-hot-toast";
import { toast as reactToastify } from 'react-toastify';
import variables from "@/styles/variables.module.scss";

const style = {
  borderRadius: 0,
  border: `2px solid ${variables.colorPrimary}`,
  color: variables.colorPrimary,
  background: "#000000",
};

// 创建一个检测函数，因为我们不能直接在模块级别使用 hooks
const isMobileDevice = () => {
  // 使用 window.innerWidth 作为替代方案
  return typeof window !== 'undefined' && window.innerWidth < 1280;
};

const showError = (message: string) => {
  if (isMobileDevice()) {
    // 移动设备使用 react-toastify
    reactToastify.error(message);
  } else {
    // PC 设备使用 react-hot-toast
    toast.error(message, {
      style,
    });
  }
};

const showSuccess = (message: string) => {
  if (isMobileDevice()) {
    // 移动设备使用 react-toastify
    reactToastify.success(message);
  } else {
    // PC 设备使用 react-hot-toast
    toast.success(message, {
      style,
    });
  }
};

const showCustom = (Message: JSX.Element) => {
  toast.custom(Message);
};

export { showError, showSuccess, showCustom };
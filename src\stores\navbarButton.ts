import { proxy } from "valtio";
import { navbarButtonDetails } from "@/components/MainPage/TopNavigation/NavbarContent/NavbarContent";
import { devtools } from "valtio/utils";

export const navbarButtonStore = proxy({
  selectedButton: "listed minesites",
  setSelectedButton: (selected: string) => {
    navbarButtonStore.selectedButton = selected;
  },
  isInitialAnimationDone: false,
  setIsInitialAnimationDone: (isDone: boolean) => {
    navbarButtonStore.isInitialAnimationDone = isDone;
  },
});

devtools(navbarButtonStore, {
  name: "navbarButtonStore",
  enabled: false,
});

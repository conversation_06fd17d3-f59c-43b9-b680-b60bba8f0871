import { plusModalStore } from "@/stores/plusModal";
import { MineCardProps } from "../MineCard";
import Buyer from "./Buyer/Buyer";
import styles from "./MineBody.module.scss";
import snakeIcon from "@/assets/icons/minesNavigation/snakeIcon.png";
import { useSnapshot } from "valtio";

interface InformationProps {
  title: string;
  subtitle: string;
  borderBottom?: boolean;
  showLogo?: boolean;
}

const MineBody = ({ information }: Pick<MineCardProps, "information">) => {
  const plusModalSnapshot = useSnapshot(plusModalStore);

  const Information = ({
    title,
    subtitle,
    borderBottom,
    showLogo,
  }: InformationProps) => (
    <>
      <div
        className={styles.information}
        style={{ borderBottom: borderBottom ? undefined : "none" }}
      >
        <div className={styles.logoWrapper}>
          <div>
            <h1 className={styles.title}>{title}</h1>
            <h1 className={styles.subtitle}>{subtitle}</h1>
          </div>
          {showLogo ? (
            <img src={snakeIcon.src} alt={"snake icon"} height={35} />
          ) : null}
        </div>
      </div>
    </>
  );

  return (
    <div className={styles.container}>
      <div className={styles.titleInformation}>
        <div className={styles.imageFrame}>
          <img src={information.mineImages[0]} alt={information.name} />
        </div>
        <div>
          <Information title={"price"} subtitle={`$${information.minePrice}`} />
          <Information title={"Highlight"} subtitle={information.mineStorage} />
        </div>
      </div>

      <Information
        title={"Mine Location"}
        subtitle={information.location[0]}
        borderBottom
      />
      <Information
        title={"TENEMENT AREA"}
        subtitle={information.area === "" ? "N/A" : information.area}
        borderBottom
        // showLogo
      />
      <Information
        title={"RESOURCE TYPE"}
        subtitle={information.mineType}
        borderBottom
      />
      <Information
        title={"LICENSE VALIDITY"}
        subtitle={information.year}
        borderBottom
      />
      <Information
        title={"RESOURCE ESTIMATE"}
        subtitle={information.resource === "" ? "N/A" : information.resource}
        borderBottom
      />
      <Information
        title={"GEOLOGY"}
        subtitle={information.storage === "" ? "N/A" : information.storage}
        borderBottom
      />
      {/* <a
        onClick={() => plusModalSnapshot.setIsOpenPlusModal(true)}
        style={{
          textAlign: "center",
          width: "100%",
          textTransform: "uppercase",
          color: "blue",
          cursor: "pointer",
        }}
      >
        Click for more details
      </a> */}
      {/* remove this when special events is over */}
      <Buyer information={information} />
    </div>
  );
};

export default MineBody;

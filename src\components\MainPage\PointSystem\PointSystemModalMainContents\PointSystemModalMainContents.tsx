import styles from "./PointSystemModalMainContents.module.scss";
import VerticalLine from "./VerticalLine/VerticalLine";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import PointSysteminformation from "./PointSysteminformation/PointSysteminformation";
import moment from "moment";
import { useSnapshot } from "valtio";
import { pointsDataStore } from "@/stores/pointsData";
import { Suspense } from "react";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;
type LeaderboardCardProps = {
  entry: Unpacked<(typeof pointsDataStore)["pointsData"]["pointsItems"]>;
  position: number;
};

const trimWalletAddress = (address: string) => {
  return address.slice(0, 6) + "..." + address.slice(-4);
};
const LeaderboardCard = ({ entry, position }: LeaderboardCardProps) => (
  <div className={styles.leaderboardCard}>
    {/* position */}
    <h1 className={styles.position}>#{position}</h1>
    {/* point */}
    <div className={styles.points}>
      <p>{trimWalletAddress(entry.userAddress)}</p>
      <p>{entry.totalPoints} POINTS</p>
    </div>
  </div>
);

const PointSystemModalMainContents = () => {
  const { pointsData } = useSnapshot(pointsDataStore);

  return (
    <div className={styles.container}>
      <div className={styles.buyerInfo}>
        <div className={styles.headerWrapper}>
          <div className={styles.header}>
            <h1 className={styles.title}>Leaderboard</h1>
            <h2 className={styles.subTitle}>
              Last Update: {moment().subtract(4, "hours").calendar()}
            </h2>
          </div>
          {/* <img src={dotDotDot.src} alt="dot dot dot icon" /> */}
        </div>
        <div className={styles.scrollArea}>
          {pointsData.pointsItems.map((entry, index) => (
            <LeaderboardCard key={index} entry={entry} position={index + 1} />
          ))}
        </div>
      </div>
      <div className={styles.verticalLine}>
        <VerticalLine />
      </div>
      <div className={styles.mainContent}>
        <PointSysteminformation />
      </div>
    </div>
  );
};

export default PointSystemModalMainContents;

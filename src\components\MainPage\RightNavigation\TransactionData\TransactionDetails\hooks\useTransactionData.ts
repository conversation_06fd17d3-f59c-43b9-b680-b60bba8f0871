import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { creatorNF<PERSON>bi } from "@/constants/abis/creatorNFTAbi";
import { launchPadAsteroidABI } from "@/constants/abis/LaunchPadAsteroidABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { supportedNetwork } from "@/wagmi/wagmiConfigs";
import { Item } from "@radix-ui/react-select";
import { useEffect, useState } from "react";
import { formatEther } from "viem";
import { useContractReads, useContractRead } from "wagmi";

export type TransactionData = {
  name: string;
  tokenId: number;
  tokenPrice: string;
};

export const useTransactionData = () => {
  const getLaunchPadAddress = (index: number) =>
    networkConfigs[supportedNetwork[index].id].launchPadAddress;
  const getAsteroidAddress = (index: number) =>
    networkConfigs[supportedNetwork[index].id].asteroidAddress;

  const [launchPadAddressBsc, setLaunchPadAddressBsc] = useState(
    getLaunchPadAddress(0),
  );
  const [launchPadAddressPolygon, setLaunchPadAddressBscPolygon] = useState(
    getLaunchPadAddress(1),
  );
  const [asteroidAddressBsc, setAsteroidAddressBsc] = useState(
    getAsteroidAddress(0),
  );
  const [asteroidAddressPolygon, setAsteroidAddressPolygon] = useState(
    getAsteroidAddress(1),
  );

  const [transactionBsc, setTransactionBsc] = useState<TransactionData[]>([]);
  const [transactionPolygon, setTransactionPolygon] = useState<
    TransactionData[]
  >([]);

  const [minePriceBsc, setMinePriceBsc] = useState<string[]>([]);
  const [minePricePolygon, setMinePricePolygon] = useState<string[]>([]);

  const listTokenIds = (chainId: number) => {
    let asteroidAddress: `0x${string}`;
    switch (chainId) {
      case supportedNetwork[0].id:
        asteroidAddress = asteroidAddressBsc;
      case supportedNetwork[1].id:
        asteroidAddress = asteroidAddressPolygon;
      default:
        break;
    }
    const tokenIds = networkConfigs[chainId].assetIds;
    const tempContractConfig: any = [];
    //const mt = tokenIds.mt
    minesDetails.map((item, index) => {
      let tokenId;
      switch (item.shortName) {
        case "mt":
          tokenId = tokenIds.mt;
        case "matsa":
          tokenId = tokenIds.matsa;
        case "zephyr":
          tokenId = tokenIds.zephyr;
      }
      tempContractConfig.push({
        address: asteroidAddress,
        abi: asteroidAddressABI,
        functionName: "tokenMinAmounts",
        args: [tokenId],
        chainId: chainId,
      });
    });
    return tempContractConfig;
  };

  const { data: tokenPriceBsc } = useContractReads({
    contracts: listTokenIds(supportedNetwork[0].id),
    watch: true,
  });

  const { data: tokenPricePolygon } = useContractReads({
    contracts: listTokenIds(supportedNetwork[1].id),
    watch: true,
  });

  // setup for on-chain data read without wallet connected
  useEffect(() => {
    if (minesDetails && minePriceBsc && minePricePolygon) {
      const tempContractListBsc: TransactionData[] = [];
      const tempContractListPolygon: TransactionData[] = [];
      minesDetails.map((item, index) => {
        tempContractListBsc.push({
          name: item.name,
          tokenId: index,
          tokenPrice: minePriceBsc[index],
        });
        tempContractListPolygon.push({
          name: item.name,
          tokenId: index,
          tokenPrice: minePricePolygon[index],
        });
      });
      setTransactionBsc(tempContractListBsc);
      setTransactionPolygon(tempContractListPolygon);
    }
  }, [minePriceBsc, minePricePolygon]);

  useEffect(() => {
    if (tokenPriceBsc && tokenPricePolygon) {
      const tempMinePriceBsc: string[] = [];
      const tempMinePricePolygon: string[] = [];

      tokenPriceBsc.map((price) => {
        if (price.status === "success") {
          // @ts-ignore
          tempMinePriceBsc.push(price.result);
        }
        setMinePriceBsc(tempMinePriceBsc);
      });

      tokenPricePolygon.map((price) => {
        if (price.status === "success") {
          // @ts-ignore
          tempMinePricePolygon.push(price.result);
        }
        setMinePricePolygon(tempMinePricePolygon);
      });
    }
  }, [tokenPriceBsc, tokenPricePolygon]);

  return { transactionBsc, transactionPolygon };
};

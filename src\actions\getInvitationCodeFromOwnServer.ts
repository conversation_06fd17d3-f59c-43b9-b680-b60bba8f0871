"use server";
import * as z from "zod";

export const getInvitationCodeFromOwnServer = async (
  walletAddress: `0x${string}`,
) => {
  let shortCode: TInvitation["data"]["shortCode"] = "";
  const url = `https://asteroidx.io/api/v1/wallet/link?address=${walletAddress}`;
  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TInvitation;
    return data.data.shortCode;
  }

  return shortCode;
};

//types
const DataSchema = z.object({
  walletAddress: z.string(),
  shortCode: z.string(),
});
type Data = z.infer<typeof DataSchema>;

const TInvitationSchema = z.object({
  code: z.number(),
  info: z.string(),
  data: DataSchema,
});
type TInvitation = z.infer<typeof TInvitationSchema>;

@import "@/styles/variables.module.scss";
@import "@/styles/mixins.scss";

@mixin title($color) {
  @include row-center;
  margin: 0;
  font-size: $font-size-2xs;
  color: $color;
}

.container {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  padding: $padding-sm;
  gap: $spacing-sm;
  // border: 1px solid yellow;
  line-height: 107%;
  cursor: pointer;

  .icons {
    @include row-center;
    // width: 50px;
  }

  .title {
    @include title($color-primary-contrast);
  }

  .titleHover {
    @include title($color-primary);
  }
  .titleFocus {
    @include title($color-primary);
    text-shadow: 0 3px 10px $color-primary;
  }
}

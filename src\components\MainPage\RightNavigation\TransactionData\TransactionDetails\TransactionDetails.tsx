import { useSnapshot } from "valtio";
import styles from "./TransactionDetails.module.scss";
import variables from "@/styles/variables.module.scss";
import { newsDataStore } from "@/stores/newsData";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import { useEffect, useState, useMemo } from "react";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import {
  TransactionData,
  useTransactionData,
} from "./hooks/useTransactionData";
import {
  commoditiesDetails,
  getMetalsDetails,
} from "@/constants/commoditiesDetails";
import Linechart from "@/components/UI/Charts/Linechart/Linechart";
import { metalsDataStore } from "@/stores/metalsData";
// const filterList = ["BSC", "POLYGON"] as const;

interface TransactionDetailsProps {
  isShowMoreButtonClicked: boolean;
}
// type FilterList = (typeof filterList)[keyof typeof filterList];

const TransactionDetails = ({
  isShowMoreButtonClicked,
}: TransactionDetailsProps) => {
  // const [selectedFilter, setSelectedFilter] = useState<FilterList>(
  //   filterList[0],
  // );
  // const { transactionBsc, transactionPolygon } = useTransactionData();
  // const newsDataSnapshot = useSnapshot(newsDataStore);

  // const getPriceUnit = () => {
  //   switch (selectedFilter) {
  //     case "BSC":
  //       return "BNB";
  //     case "POLYGON":
  //       return "MATIC";
  //     default:
  //       null;
  //   }
  // };
  // const DisplayTransaction = ({
  //   transactionData,
  // }: {
  //   transactionData: TransactionData[];
  // }) => (
  //   <>
  //     {transactionData
  //       .sort((a, b) => b.tokenId - a.tokenId)
  //       .map((transaction, index) => (
  //         <div key={index} className={styles.bodyWrapper}>
  //           <div className={styles.transactionTitleWrapper}>
  //             <h1 className={styles.transactionTitle}>{transaction.name}</h1>
  //             <h1 className={styles.transactionTitle}>
  //               {transaction.tokenPrice} <span>{getPriceUnit()}</span>
  //             </h1>
  //           </div>
  //           <div className={styles.transactionSubTitleWrapper}>
  //             <h1 className={styles.transactionSubtitle}>
  //               # {transaction.tokenId}
  //             </h1>
  //             <h1 className={styles.transactionSubtitle}>PER CARAT</h1>
  //           </div>
  //         </div>
  //       ))}
  //   </>
  // );

  // const getSelectedFilter = () => {
  //   switch (selectedFilter) {
  //     case "BSC":
  //       return <DisplayTransaction transactionData={transactionBsc} />;
  //     case "POLYGON":
  //       return <DisplayTransaction transactionData={transactionPolygon} />;
  //     default:
  //       return null;
  //   }
  // };

  const [metalsData, setMetalsData] = useState<typeof commoditiesDetails>([]);
  const metalsDataSnapshot = useSnapshot(metalsDataStore);
  // console.log(metalsDataSnapshot.metalsData);
  const REFRESH_INTERVAL = 60000; // 60000 毫秒 = 1 分钟

  const processedMetalsData = useMemo(() => {
    if (metalsDataSnapshot.metalsData) {
      return getMetalsDetails(
        JSON.parse(JSON.stringify(metalsDataSnapshot.metalsData)),
      );
    }
    return [];
  }, [metalsDataSnapshot.metalsData]);

  useEffect(() => {
    const refreshData = async () => {
      // console.log("Refreshing MetalsData");
      await metalsDataStore.fetchMetalsData();
    };

    refreshData();

    const intervalId = setInterval(refreshData, REFRESH_INTERVAL);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  useEffect(() => {
    setMetalsData(processedMetalsData);
  }, [processedMetalsData]);

  return (
    <>
      <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>commodities data</h1>
          <h2 className={styles.subTitle}>list of updated prices</h2>
        </div>
        {/* <img src={dotDotDot.src} alt="dot dot dot icon" /> */}
      </div>
      {/* <div className={styles.filter}>
        {filterList.map((item, index) => {
          const isSelected = selectedFilter === item;
          return (
            <motion.h1
              whileTap={buttonEffect.tap}
              whileHover={buttonEffect.hover}
              key={index}
              className={styles.list}
              style={{ color: isSelected ? variables.colorPrimary : undefined }}
              onClick={() => setSelectedFilter(item)}
            >
              {item}
            </motion.h1>
          );
        })}
      </div>*/}
      <div
        className={styles.scrollArea}
        style={{ height: isShowMoreButtonClicked ? "79.5%" : "75.5%" }}
      >
        {metalsData.map((item, index) => (
          <div className={styles.commoditiesDetailsWrapper} key={index}>
            <div className={styles.commoditiesNameWrapper}>
              <h1
                className={styles.commoditiesName}
                style={{ color: item.color }}
              >
                {item.name}
              </h1>
              <h2 className={styles.commoditiesPrice}>
                ${item.price.toFixed(2)}
              </h2>
            </div>
            <div className={styles.commoditiesPriceWrapper}>
              <div style={{ width: "100px", height: "50px" }}>
                <Linechart data={item.historicalData} />
              </div>
              <h3
                style={{
                  color:
                    item.priceChangePercentage24h >= 0
                      ? variables.colorPrimaryContrast
                      : variables.colorDanger,
                }}
              >
                {item.priceChangePercentage24h >= 0 && "+"}
                {item.priceChangePercentage24h}%
              </h3>
            </div>
          </div>
        ))}
        {/* {getSelectedFilter()} */}
      </div>
    </>
  );
};

export default TransactionDetails;

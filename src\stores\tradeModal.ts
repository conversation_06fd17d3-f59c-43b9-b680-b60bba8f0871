import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export const tradeModalStore = proxy({
  isOpenTradeModal: false,
  setIsOpenTradeModal: (status: boolean) => {
    tradeModalStore.isOpenTradeModal = status;
  },
  minePrice: "",
  setMinePrice: (price: string) => {
    tradeModalStore.minePrice = price;
  },
});

devtools(tradeModalStore, {
  name: "tradeModalStore",
  enabled: false,
});

import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import {
  useAccount,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useActiveListing from "./useActiveListing";
import { useMediaQuery } from "usehooks-ts";
import { toast } from "react-toastify";
import { extractErrorType } from "@/utils/errorHandling";

type PurchaseListingProps = {
  amount: bigint;
};

const usePurchaseListing = ({ amount }: PurchaseListingProps) => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [isPurchasingListing, setIsPurchasingListing] = useState(false);
  const [value, setValue] = useState(0n);
  const [errorMessage, setErrorMessage] = useState("");
  const [refresh, setRefresh] = useState(0);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const isMobile = useMediaQuery("(max-width: 1279px)");

  // Notification function that works for both mobile and desktop
  const notify = (message: string, type: "error" | "success") => {
    if (isMobile) {
      if (type === "error") {
        toast.error(message);
      } else {
        toast.success(message);
      }
    } else {
      if (type === "error") {
        showError(message);
      } else {
        showSuccess(message);
      }
    }
  };
  const { listingId, pricePerToken } = useActiveListing();
  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  useEffect(() => {
    if (pricePerToken && amount) {
      setValue(BigInt(amount) * BigInt(pricePerToken));
    }
  }, [amount, pricePerToken]);

  const { config: purchaseListingConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "purchaseListing",
      args: [listingId, amount],
      // value,
      enabled:
        isConnected &&
        marketplaceAddress !== "0x" &&
        listingId !== 0n &&
        amount !== 0n &&
        refresh >= 0,
      // value !== 0n,
      cacheTime: 0,
      staleTime: 0,
    });

  const { write: purchaseListingWrite, data: purchaseListingData } =
    useContractWrite({
      ...purchaseListingConfig,
      onError: (error) => {
        const errorMessage = extractErrorType(error);
        notify(errorMessage, "error");
        setIsPurchasingListing(false);
      },
    });

  const { isLoading: isWaitingForPurchaseListing } = useWaitForTransaction({
    confirmations: 5,
    hash: purchaseListingData?.hash,
    onSuccess: () => {
      notify("Successfully Purchased!", "success");
      setIsPurchasingListing(false);
    },
    onError: (error) => {
      notify(
        extractErrorType(error) ||
          (error as BaseError).shortMessage ||
          error.message,
        "error",
      );
      setIsPurchasingListing(false);
    },
  });

  const purchaseListing = () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      notify(`Not supported on ${chain?.name}`, "error");
      return;
    }

    if (errorMessage) {
      notify(errorMessage, "error");
      return;
    }

    if (!purchaseListingWrite) {
      if (prepareError) {
        notify(extractErrorType(prepareError), "error");
      }
      return;
    }

    setIsPurchasingListing(true);
    try {
      purchaseListingWrite?.();
    } catch (error) {
      setIsPurchasingListing(false);
      notify(extractErrorType(error) || "Failed to purchase listing", "error");
    }
  };

  return {
    isWaitingForPurchaseListing,
    isPurchasingListing,
    purchaseListing,
    value,
    refresh,
    setRefresh,
  };
};

export default usePurchaseListing;

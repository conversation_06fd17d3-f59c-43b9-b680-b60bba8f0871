import { mineCardStore } from "@/stores/mineCard";
import styles from "./PointSystemInformation.module.scss";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import locationIconGrey from "@/assets/icons/plusModal/locationIconGrey.png";
import { plusModalStore } from "@/stores/plusModal";
import {
  memo,
  useRef,
  useEffect,
  useLayoutEffect,
  Suspense,
  useState,
} from "react";
import Header from "./Header/Header";
import Summary from "./Summary/Summary";
import {
  useAccount,
  useNetwork,
  useContractWrite,
  usePrepareContractWrite,
  useWaitForTransaction,
  useContractRead,
} from "wagmi";
import { useRouter } from "next/navigation";
import { navbarButtonStore } from "@/stores/navbarButton";
import { PointsData, pointsDataStore, TUserTask } from "@/stores/pointsData";
import { navbarButtonDetails } from "@/components/MainPage/TopNavigation/NavbarContent/NavbarContent";
import { showError, showSuccess } from "@/lib/notification";
import UserTask from "./UserTask/UserTask";
import { rewardAsteroidXAbi } from "@/constants/abis/RewardAsteroidXABI";
import { BaseError } from "viem";
import { networkConfigs } from "@/constants/networkConfigs";
import useSpecialEvent from "@/components/MainPage/MinesNavigation/SpecialEventMineCard/MineBody/hooks/useSpecialEvent";

const formatNumber = (value: bigint | undefined): string => {
  if (!value) return "0";
  return value.toString();
};

const formatTimestamp = (timestamp: bigint | undefined): string => {
  if (!timestamp) return "-";
  const date = new Date(Number(timestamp) * 1000);
  return date
    .toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(",", "");
};

const weiToEth = (wei: bigint | undefined): string => {
  if (!wei) return "0";
  const eth = Number(wei) / 1e18;
  return eth.toFixed(4); // Display 4 decimal places
};

const PointSysteminformation = () => {
  const { claimStartTime } = useSpecialEvent();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();
  const router = useRouter();
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const { pointsData, userTasks } = useSnapshot(pointsDataStore);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [rewardAddress, setRewardAddress] = useState<`0x${string}`>("0x");

  // Get user reward info
  const { data: userRewardInfo } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "userRewardInfo",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && !!address,
    watch: true,
  });

  // Calculate user reward
  const { data: claimableReward } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "calculateUserReward",
    args: [address ?? "0x"],
    enabled: isConnected && !chain?.unsupported && !!address,
    watch: true,
  });

  const { config: claimConfig } = usePrepareContractWrite({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "claim",
    enabled:
      isConnected &&
      !chain?.unsupported &&
      !!userRewardInfo &&
      !userRewardInfo.hasClaimed &&
      (claimableReward ?? 0n) > 0n,
    onError: (error) => {
      console.error("Prepare claim error:", error);
    },
  });

  const { write: claimWrite, data: claimData } = useContractWrite({
    ...claimConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const { isLoading: isWaitingForClaim } = useWaitForTransaction({
    hash: claimData?.hash,
    onSuccess: () => {
      showSuccess("Successfully claimed HSK rewards!");
      setIsClaiming(false);
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsClaiming(false);
    },
  });

  const handleClaim = async () => {
    if (!isConnected) {
      showError("Please connect your wallet first");
      return;
    }

    if (chain?.unsupported) {
      showError("Please switch to a supported network");
      return;
    }

    const currentTimestamp = BigInt(Math.floor(Date.now() / 1000));
    if (currentTimestamp < claimStartTime) {
      showError("Claim has not started yet");
      return;
    }

    if (!claimWrite) {
      if (userRewardInfo?.hasClaimed) {
        showError("You have already claimed your rewards");
        return;
      }

      if ((claimableReward ?? 0n) <= 0n) {
        showError("No rewards available to claim");
        return;
      }

      showError("Unable to claim rewards at this time");
      return;
    }

    setIsClaiming(true);
    try {
      await claimWrite();
    } catch (error) {
      setIsClaiming(false);
      showError("Failed to claim rewards");
    }
  };

  const switchToMainPage = () => {
    navbarButtonSnapshot.setSelectedButton(navbarButtonDetails[0].title);
  };

  useLayoutEffect(() => {
    if (!isConnected) {
      showError("Please Connect Wallet");
      switchToMainPage();
      return;
    }
    if (chain?.unsupported) {
      // showError("Please Switch Network");
      switchToMainPage();
      return;
    }
    if (chain) {
      setRewardAddress(networkConfigs[chain.id].rewardAddress);
    }
    const fetchData = async () => {
      if (isConnected && address) {
        setIsLoading(true);
        await Promise.all([
          pointsDataStore.fetchPointsData(address),
          pointsDataStore.fetchUserTasks(address),
        ]);
      }
      setIsLoading(false);
    };

    fetchData();
  }, [isConnected, chain, address]);

  if (isLoading) return <p className={styles.loadingText}>Loading...</p>;

  return (
    <>
      {isConnected && !chain?.unsupported ? (
        <div className={styles.container}>
          <div className={styles.scrollArea}>
            <div className={styles.imageFrame}>
              <div className={styles.pointSystemScrollArea}>
                <div className={styles.detailsContainer}>
                  <Header />
                  <Summary pointsData={pointsData as PointsData} />
                  {userTasks.length > 0 ? (
                    <UserTask userTasks={userTasks as TUserTask} />
                  ) : null}
                </div>
              </div>
            </div>
          </div>
          {/* <div className={styles.mainTitle}>
            <div className={styles.rewardSection}>
              <div className={styles.rewardInfo}>
                <div className={styles.rewardRow}>
                  <span className={styles.label}>Total Purchase Amount</span>
                  <span className={styles.value}>
                    {userRewardInfo
                      ? formatNumber(userRewardInfo.purchaseAmount)
                      : "0"}{" "}
                    HSK
                  </span>
                </div>
                <div className={styles.rewardRow}>
                  <span className={styles.label}>Purchase Time</span>
                  <span className={styles.value}>
                    {formatTimestamp(userRewardInfo?.timestamp)}
                  </span>
                </div>
                <div className={styles.rewardRow}>
                  <span className={styles.label}>Claimable Reward</span>
                  <span className={styles.value}>
                    {userRewardInfo?.hasClaimed
                      ? "0"
                      : weiToEth(claimableReward)}{" "}
                    HSK
                  </span>
                </div>
                <div className={styles.rewardRow}>
                  <span className={styles.label}>Claimed Amount</span>
                  <span className={styles.value}>
                    {userRewardInfo?.hasClaimed
                      ? weiToEth(userRewardInfo.claimedReward)
                      : "0"}{" "}
                    HSK
                  </span>
                </div>
              </div>
              {BigInt(Math.floor(Date.now() / 1000)) >= claimStartTime ? (
                <button
                  className={styles.claimButton}
                  onClick={handleClaim}
                  disabled={
                    isClaiming ||
                    isWaitingForClaim ||
                    userRewardInfo?.hasClaimed ||
                    (claimableReward ?? 0n) <= 0n
                  }
                >
                  {isClaiming || isWaitingForClaim
                    ? "Claiming..."
                    : userRewardInfo?.hasClaimed
                    ? "Already Claimed"
                    : (claimableReward ?? 0n) <= 0n
                    ? "No Rewards"
                    : "Claim Rewards"}
                </button>
              ) : null}
            </div>
          </div> */}
        </div>
      ) : null}
    </>
  );
};

export default PointSysteminformation;

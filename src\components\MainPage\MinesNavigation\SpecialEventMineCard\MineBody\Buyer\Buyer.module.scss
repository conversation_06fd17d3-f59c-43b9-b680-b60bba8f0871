@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin avatar {
  width: 41px;
  height: 41px;
  border: $border-width-xs solid $color-primary;
  border-radius: $border-radius-sm;
  @include row-center;
  // cursor: pointer;

  .letter {
    font-size: $font-size-2xl;
    font-weight: $font-weight-light;
    color: #00f1ff;
    text-transform: uppercase;
  }
}
.container {
  width: 100%;
  height: 100%;
  @include col-center;
  justify-content: flex-start;
  align-items: flex-start;

  .title {
    font-size: $font-size-xs;
    color: $color-primary-transparent-contrast;
    font-weight: $font-weight-light;
    text-transform: uppercase;
  }

  .loginButtonWrapper {
    @include col-center;
    width: 100%;
    height: 100%;

    .loginButton {
      @include row-center;
      width: 100%;
      text-align: center;
      font-size: $font-size-sm;
      font-family: $font-family-poppins;
      // color: $color-primary;
      color: $color-warning;
      text-transform: uppercase;
      font-weight: $font-weight-semibold;
      border: $border-width-xs solid $color-primary-contrast;
      padding: $padding-sm 0;
      background: $color-primary-transparent;
      backdrop-filter: blur(5px);
      border-radius: $border-radius-sm;
      cursor: pointer;

      &:disabled {
        background-color: #cccccc;
        color: gray;
        cursor: not-allowed;
        transform: none;
        opacity: 0.7;

        &:hover {
          transform: none;
        }

        &:active {
          transform: none;
        }
      }

      span {
        color: $color-primary-transparent-contrast;
      }

      &:hover {
        border: $border-width-xs solid $color-primary;
      }
    }
  }

  .informationContainer {
    width: 100%;
    height: 100%;
    // border: 1px solid yellow;
    @include row-center;

    .informationWrapper {
      width: 100%;
      height: 50px;
      padding: $padding-xs 0;

      @include row-center;
      justify-content: flex-start;
      gap: $spacing-sm;
      flex-wrap: wrap;

      overflow: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .avatar {
        @include avatar();
      }
    }
  }
}

.tooltipContent {
  z-index: $z-index-1;
  user-select: none;
  animation-name: topDownAndFade;
  animation-duration: 0.5s;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;

  .tooltipArrow {
    fill: $color-primary;
  }

  .tooltipDetails {
    width: 280px;
    // height: 175px;
    position: relative;
    background: $color-black-transparent-dark;
    backdrop-filter: blur(30px);
    padding: $padding-md;
    clip-path: polygon(0 0, calc(100% - 32px) 0, 100% 32px, 100% 100%, 0 100%);
    // border: 1px solid yellow;

    .buyerCardContainer {
      width: 100%;
      @include col-center;
      gap: $spacing-sm;

      .headerWrapper {
        width: 100%;
        @include row-between;
        // margin-bottom: $margin-sm;
        .header {
          line-height: 108%;
          text-transform: uppercase;

          .headerTitle {
            font-size: $font-size-sm;
            color: $color-primary;
            font-weight: $font-weight-semibold;
          }
          .headerSubTitle {
            font-size: $font-size-xs;
            color: $color-primary-transparent-contrast;
            font-weight: $font-weight-light;
          }
        }
      }
      .userDetails {
        width: 100%;
        @include row-center;
        justify-content: flex-start;
        gap: $spacing-sm;
        border-bottom: $border-width-2xs solid $color-primary-contrast;
        padding-bottom: $spacing-md;

        .avatar {
          @include avatar();
        }
        .name {
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          color: $color-primary;
        }
        .purchasedWrapper {
          @include row-center;
          justify-content: flex-start;
          gap: $spacing-sm;
          .purchased {
            font-size: $font-size-sm;
            font-weight: $font-weight-medium;
            color: #027b81;
          }
        }
      }
      .connect {
        width: 100%;
        .connectText {
          font-size: $font-size-xs;
          font-weight: $font-weight-medium;
          color: $color-primary-transparent-contrast;
          text-transform: uppercase;
        }
        .contact {
          font-size: $font-size-xs;
          font-weight: $font-weight-medium;
          color: $color-primary;
        }
      }
    }

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary-transparent-contrast;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%,
        0 0,
        1px 1px,
        1px calc(100% - 1px),
        calc(100% - 1px) calc(100% - 1px),
        calc(100% - 1px) calc(32px + 0.41px),
        calc(100% - 32px - 0.41px) 1px,
        1px 1px
      );
    }
  }
}

@keyframes topDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

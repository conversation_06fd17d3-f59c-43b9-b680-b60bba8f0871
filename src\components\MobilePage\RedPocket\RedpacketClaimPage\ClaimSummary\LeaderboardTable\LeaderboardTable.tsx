import React from "react";
import { useAccount } from "wagmi";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";
import styles from "./LeaderboardTable.module.scss";
import table from "@/assets/images/table.jpg";
import Image from "next/image";

// 修剪钱包地址的函数
const trimWalletAddress = (address: string) => {
  return address.slice(0, 6) + "..." + address.slice(-6);
};

// 定义组件的属性接口
interface LeaderboardTableProps {}

const LeaderboardTable: React.FC<LeaderboardTableProps> = () => {
  const {
    topParticipants,
    totalParticipants,
    currentPage,
    setCurrentPage,
    pageSize,
  } = useRedpacket();
  const { address } = useAccount();

  // 判断是否是当前用户
  const isCurrentUser = (entryAddress: string) => {
    if (!address) return false;
    return entryAddress === trimWalletAddress(address);
  };

  // 计算总页数
  const totalPages = Math.ceil(totalParticipants / pageSize);

  // 处理上一页按钮点击事件
  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  // 处理下一页按钮点击事件
  const handleNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className={styles.leaderboardContainer}>
      <div className={styles.title}>
        <Image src={table} alt="table" className={styles.table} />
        <div>LeaderBoard</div>
      </div>
      {/* 表格标题部分 */}
      <div className={styles.leaderboardHeader}>
        <div className={styles.positionHeader}>Rank</div>
        <div className={styles.addressHeader}>Address</div>
        <div className={styles.amountHeader}>Amount(HSK)</div>
        <div className={styles.claimTimeHeader}>Claim Time</div>
      </div>
      <div className={styles.leaderboardList}>
        {topParticipants.map((entry) => (
          <div
            key={entry.rank}
            className={
              isCurrentUser(entry.address)
                ? `${styles.leaderboardRow} ${styles.currentUser}`
                : styles.leaderboardRow
            }
          >
            <div className={styles.positionWrapper}>
              <div className={styles.positionLabel}>#{entry.rank}</div>
            </div>
            <div className={styles.pointsValue}>
              <div className={styles.addressValue}>
                {entry.address}
                {isCurrentUser(entry.address) && (
                  <span className={styles.youLabel}> (YOU)</span>
                )}
              </div>
              <div className={styles.amountValue}>{entry.amount}</div>
              <div className={styles.claimTimeValue}>{entry.claimTime}</div>
            </div>
          </div>
        ))}
      </div>
      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button
            onClick={handlePrevPage}
            disabled={currentPage === 0}
            className={styles.pageButton}
          >
            Previous
          </button>
          <span className={styles.pageInfo}>
            Page {currentPage + 1} of {totalPages}
          </span>
          <button
            onClick={handleNextPage}
            disabled={currentPage >= totalPages - 1}
            className={styles.pageButton}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default LeaderboardTable;
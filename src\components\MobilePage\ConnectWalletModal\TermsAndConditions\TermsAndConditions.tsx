import { useSnapshot } from "valtio";
import styles from "./TermsAndConditions.module.scss";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import { Dispatch, SetStateAction, useRef } from "react";
import { termsAndConditions } from "@/constants/termsAndConditions-v2";
import { motion } from "framer-motion";

type TermsAndConditionsProps = {
  setHasUserReadTandC: Dispatch<SetStateAction<boolean>>;
};

const TermsAndConditions = ({
  setHasUserReadTandC,
}: TermsAndConditionsProps) => {
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const statementRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (statementRef.current) {
      statementRef.current.scrollTo({
        top: statementRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.statement} ref={statementRef}>
        <h2 className={styles.title}>{termsAndConditions.title}</h2>
        <h5 className={styles.lastUpdated}>{termsAndConditions.lastUpdated}</h5>
        <br />
        <p>{termsAndConditions.notice}</p>
        {termsAndConditions.sections.map((section, index) => (
          <div key={index}>
            <br />
            <h3>{section.header}</h3>
            <p>{section.contents}</p>
          </div>
        ))}
        <div className={styles.buttonGroup}>
          <button
            className={styles.cancelButton}
            onClick={() =>
              connectWalletModalSnapshot.setIsOpenConnectWalletModal(false)
            }
          >
            Decline
          </button>
          <button
            className={styles.confirmButton}
            onClick={() => setHasUserReadTandC(true)}
          >
            Accept
          </button>
        </div>
      </div>
      <motion.button
        className={styles.scrollButton}
        onClick={scrollToBottom}
        animate={{
          y: [0, -10, 0],
        }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        whileHover={{
          scale: 1.1,
        }}
      >
        ↓ Swipe Down
      </motion.button>
    </div>
  );
};

export default TermsAndConditions;

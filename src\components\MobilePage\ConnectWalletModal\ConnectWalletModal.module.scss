@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.walletSelect {
  width: 100%;
  @include row-between;
  margin-top: $margin-sm;
  cursor: pointer;
  padding: $padding-sm;

  div {
    @include row-center;
    gap: $spacing-lg;
    color: $color-primary;
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
  }

  h1 {
    color: $color-primary-transparent-contrast;
    font-size: $font-size-sm;
    text-transform: uppercase;
    font-weight: $font-weight-light;
  }

  &:hover {
    background: $color-primary-transparent;
  }
}

.container {
  width: 100%;
  height: 100%;
  position: absolute;
  // overflow-y: auto;
  // border: 1px solid yellow;
  // top: 65%;
  // left: 65%;
  // transform: translate(-50%, -50%);
  z-index: $z-index-1;
  // background: green;

  .modalFrame {
    width: 100%;
    height: 100%;
    background: $color-black-transparent-dark;
    // position: relative;
    backdrop-filter: blur(20px);
    padding: $padding-xl $padding-xl $padding-2xl;

    clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary;

      clip-path: polygon(
        0 0,
        calc(100% - 2rem) 0,
        100% 2rem,
        100% 100%,
        0 100%,
        0 0,
        1px 1px,
        0.3rem calc(100% - 1px),
        calc(100% - 1px) calc(100% - 1px),
        calc(100% - 0.2rem) calc(2rem + 0.83px),
        calc(100% - 2rem - 0.83px) 1px,
        1px 1px
      );
    }

    .connectWallet {
      position: relative;
      width: 100%;
      height: 100%;
      overflow-y: auto;
      // border: 1px solid yellow;

      .termAndCondition {
        width: 100%;

        h1 {
          width: 100%;
          text-align: center;
          color: $color-danger;
        }

        h3 {
          width: 100%;
          text-align: center;

          a {
            color: $color-warning;
            cursor: pointer;
            text-transform: uppercase;
          }
        }
      }

      .titleWrapper {
        line-height: 108%;
        padding-bottom: $padding-lg;
        border-bottom: $border-width-xs solid $color-primary-transparent;
        @include row-between;

        h1 {
          color: $color-primary;
          font-size: $font-size-md;
          text-transform: uppercase;
          font-weight: $font-weight-semibold;
        }

        h2 {
          width: 320px;
          color: $color-danger;
          font-size: $font-size-xs;
          text-transform: uppercase;
          font-weight: $font-weight-light;

          a {
            color: $color-warning;
            font-weight: $font-weight-bold;
            cursor: pointer;

            &:hover {
              color: $color-primary;
            }
          }
        }
      }
    }

    // &::before {
    //   content: "";
    //   position: absolute;
    //   inset: 0;
    //   background: $color-primary;
    //   clip-path: polygon(
    //     0 0,
    //     calc(100% - 51px) 0,
    //     100% 51px,
    //     100% calc(100% - 48px),
    //     calc(100% - 48px) 100%,
    //     48px 100%,
    //     0 calc(100% - 48px),
    //     0 0,
    //     1px 1px,
    //     1px calc(100% - 48px - 0.41px),
    //     calc(48px + 0.41px) calc(100% - 1px),
    //     calc(100% - 48px - 0.41px) calc(100% - 1px),
    //     calc(100% - 1px) calc(100% - 48px - 0.41px),
    //     calc(100% - 1px) calc(48px + 0.41px),
    //     calc(100% - 48px - 0.41px) 1px,
    //     1px 1px
    //   );
    // }
  }
  .toastify {
    font-size: $font-size-xs;
  }
}

.showMoreWallets {
  margin-top: 12px;
  padding: 10px;
  text-align: center;
  font-size: 14px;
  color: #4a9dea;
  cursor: pointer;
  transition: color 0.2s;
  border-radius: 8px;
  background-color: rgba(74, 157, 234, 0.1);

  &:hover {
    color: #70b5f9;
    background-color: rgba(74, 157, 234, 0.15);
  }
}

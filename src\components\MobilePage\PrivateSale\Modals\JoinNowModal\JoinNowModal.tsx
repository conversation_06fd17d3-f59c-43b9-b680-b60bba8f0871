import { fadeIn } from "@/animations/animations";
import { motion } from "framer-motion";
import styles from "./JoinNowModal.module.scss";
import ContributeContent from "./ContributeContent/ContributeContent";

interface JoinNowModalProps {
  isOpen: boolean;
  onClose: () => void;
  openRewardsModal: () => void;
}

const JoinNowModal = ({
  isOpen,
  onClose,
  openRewardsModal,
}: JoinNowModalProps) => {
  if (!isOpen) return null;

  return (
    <motion.div
      className={styles.modalOverlay}
      variants={fadeIn(0.3)}
      initial="hidden"
      animate="visible"
      exit="hidden"
    >
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <div className={styles.titleGroup}>
            <h2>Contribute & Earn</h2>
            <h2>Join the DrillX Presale</h2>
          </div>
          <button className={styles.closeButton} onClick={onClose}>
            ✕
          </button>
        </div>
        <div className={styles.modalBody}>
          <ContributeContent
            onClose={onClose}
            openRewardsModal={openRewardsModal}
          />
        </div>
      </div>
    </motion.div>
  );
};

export default JoinNowModal;

import { useAccount } from "wagmi";
import { MainContentProps } from "../../MainContent";
import styles from "./joinNowButton.module.scss";
import { showError } from "@/lib/notification";
import { useSearchParams } from "next/navigation";
import useTokenInfo from "../SaleDetails/hooks/useTokenInfo";
import { formatEther } from "viem";

const JoinNowButton = ({ openJoinNowModal }: MainContentProps) => {
  const searchParams = useSearchParams();
  const search = searchParams.get("invitation");
  const { address } = useAccount();
  const { userTotalPurchased } = useTokenInfo("");
  const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";

  const handleClick = () => {
    // if (!isTestnet) {
    //   showError("Coming soon! This feature is not available yet.");
    //   return;
    // }

    if (!address) {
      showError("Please connect your wallet first");
      return;
    }

    // if (search === null || search === "") {
    //   showError("Please join via invitation link");
    //   return;
    // }
    openJoinNowModal?.();
  };

  return (
    <div className={styles.container} onClick={handleClick}>
      {/* {!isTestnet && (
        <div className={styles.comingSoonOverlay}>
          <span>Coming Soon</span>
        </div>
      )} */}
      <div className={styles.content}>
        <h3 className={styles.title}>Join Now</h3>
        {userTotalPurchased === 0 ? (
          <p className={styles.subtitle}>You have no $DrillX yet</p>
        ) : (
          <p className={styles.subtitle}>
            You own{" "}
            <span className={styles.highlightedNumber}>
              {formatEther(BigInt(userTotalPurchased))}
            </span>{" "}
            $DrillX
          </p>
        )}
      </div>
    </div>
  );
};

export default JoinNowButton;

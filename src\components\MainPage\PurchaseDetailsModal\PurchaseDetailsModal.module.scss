@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

@mixin actionButton() {
  width: 130px;
  padding: $padding-sm;
  border: $border-width-2xs solid $color-primary;
  font-size: $font-size-md;
  font-family: $font-family-saira;
  background: $color-black-transparent;
  color: $color-primary-contrast;
  cursor: pointer;

  &:hover {
    background: $color-primary-contrast;
    color: $color-primary;
  }
}

.divider {
  // width: 100%;
  border: $border-width-2xs solid grey;
  margin: $margin-md $margin-md 0;
}

.container {
  position: absolute;
  width: $displayWidth;
  height: $displayHeight;
  // border: 1px solid green;
  background: $color-black-transparent-medium;
  bottom: 0;
  overflow: auto;
  z-index: $z-index-1;
  @include row-center;

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
  }

  @media screen and (max-width: 1280px) {
    width: 100%;
    height: 100%;
  }

  .soldoutOverlay {
    position: absolute;
    background: $color-black-transparent-medium;
    top: 60%;
    bottom: 10%;
    width: 100%;
    // height: 100%;
    z-index: 99;
    @include col-center;
    justify-content: flex-start;

    img {
      rotate: -45deg;
    }
  }

  .summaryContainer {
    position: absolute;
    width: 450px;
    padding: $padding-md 0;
    border: $border-width-2xs solid $color-primary;
    background: $color-black-transparent-medium;
    box-shadow: 0 0 20px $color-primary;

    .closeButton {
      position: absolute;
      outline: none;
      border: none;
      border-radius: 100%;
      height: 25px;
      width: 25px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: inherit;
      position: absolute;
      top: 28px;
      right: $padding-sm;
      cursor: pointer;

      &:hover {
        background-color: $color-primary-transparent-contrast;
      }
    }

    .summaryTitle {
      padding: 0 $padding-lg;
      // text-align: center;
    }
    .sliderContainer {
      @include row-between;
      gap: 1rem;
      padding: 0.5rem $padding-lg 0;

      h3 {
        color: $color-warning;
      }

      .SliderRoot {
        position: relative;
        display: flex;
        align-items: center;
        user-select: none;
        touch-action: none;
        width: 300px;
        height: 20px;
      }

      .SliderTrack {
        background-color: $color-primary-contrast;
        position: relative;
        flex-grow: 1;
        border-radius: 9999px;
        height: 3px;
      }

      .SliderRange {
        position: absolute;
        background-color: $color-primary;
        border-radius: 9999px;
        height: 100%;
      }

      .SliderThumb {
        display: block;
        width: 20px;
        height: 20px;
        background-color: white;
        box-shadow: 0 2px 10px $color-primary;
        border-radius: 10px;

        &:hover {
          background-color: $color-primary;
        }
        &:focus {
          outline: none;
          box-shadow: 0 0 0 5px black;
        }
      }
    }

    .connectedAccount {
      padding: 0 $padding-lg;
      color: $color-warning;
    }

    .sharesWrapper {
      width: 100%;
      @include row-center;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: $spacing-md;
      padding: $padding-sm $padding-lg;

      button {
        width: 50px;
        outline: none;
        border: $border-width-2xs solid $color-primary;
        // border-radius: 1rem;
        background: $color-primary-transparent;
        color: $color-primary;
        cursor: pointer;
        padding: $padding-xs;

        // &:hover {
        //   background: $color-primary;
        //   color: $color-primary-contrast;
        // }
      }
    }

    .totalPrice {
      text-align: center;
      color: $color-primary;
      font-size: $font-size-5xl;

      span {
        font-size: $font-size-2xl;
      }

      .informationIcon {
        width: 20px;
        height: 20px;
        cursor: pointer;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .gasFee {
      padding: 0 $padding-lg;
      text-align: center;
      margin-top: 0.8rem;

      span {
        color: $color-warning;
      }
    }

    .controlButton {
      @include row-center;
      gap: $spacing-lg;
      padding: $padding-sm $padding-lg;

      .cancelButton {
        @include actionButton();
        color: $color-danger;
        border: $border-width-2xs solid $color-danger;

        &:hover {
          background: $color-danger;
        }
      }
      .confirmButton {
        @include actionButton();
      }
    }

    .loadingContainer {
      @include row-center;
      padding: $padding-sm $padding-lg;

      .loadingStatus {
        @include row-center;
        gap: $spacing-sm;

        h3 {
          color: $color-warning;
          font-size: $font-size-xl;
        }
      }
    }

    .testToken {
      text-align: center;
      color: $color-warning;
      text-decoration: underline;
      padding-top: $padding-sm;
      cursor: pointer;
    }

    .bridgeContainer {
      position: relative;

      .bridgeOptions {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 280px;
        background: $color-black-transparent-dark;
        border: $border-width-2xs solid $color-primary;
        border-radius: 8px;
        box-shadow: 0 0 20px rgba(0, 196, 208, 0.3);
        backdrop-filter: blur(30px);
        z-index: 10;
        margin-bottom: 0.25rem;
        padding: $padding-sm;

        .bridgeOption {
          display: flex;
          flex-direction: column;
          padding: $padding-md;
          cursor: pointer;
          border-radius: 6px;
          transition: background-color 0.2s ease;

          &:hover {
            background: $color-primary-transparent;
          }

          &:not(:last-child) {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          }

          span:first-child {
            color: $color-primary-contrast;
            font-weight: $font-weight-medium;
            font-size: $font-size-md;
          }

          .bridgeUrl {
            color: $color-warning;
            font-size: $font-size-sm;
            margin-top: 0.25rem;
          }
        }
      }
    }
  }
}

// Tooltip styles
.tooltipContent {
  z-index: $z-index-1;
  user-select: none;
  animation-name: topDownAndFade;
  animation-duration: 0.3s;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;

  .tooltipDetails {
    min-width: 280px;
    max-width: 320px;
    background: $color-black-transparent-dark;
    backdrop-filter: blur(30px);
    border: $border-width-2xs solid $color-primary;
    padding: $padding-md;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 196, 208, 0.3);

    .tooltipTitle {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      margin-bottom: $margin-sm;
      text-align: center;
    }

    .tooltipInfo {
      p {
        font-size: $font-size-sm;
        color: $color-primary-contrast;
        margin: $margin-sm 0;
        line-height: 1.4;

        &:first-child {
          color: $color-warning;
          font-weight: $font-weight-medium;
        }
      }
    }
  }

  .tooltipArrow {
    fill: $color-primary;
  }
}

@keyframes topDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
}

.modal {
  position: fixed;
  width: 85%;
  padding: 1.5rem;
  margin: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) !important;
  background: #001a1a;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1001;

  &::-webkit-scrollbar {
    display: none;
  }
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #00ffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;

  &:hover {
    color: white;
  }
}

.content {
  .description {
    color: #ffffff;
    margin-bottom: 1rem;
    line-height: 1.6;

    strong {
      color: #00ffff;
    }
  }
}

.divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(0, 255, 255, 0.3),
    transparent
  );
  margin: 0.5rem 0; // 增加上下间距
  position: relative;

  &::before {
    // 添加发光效果
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      to right,
      transparent,
      rgba(0, 255, 255, 0.1),
      transparent
    );
    filter: blur(2px);
  }
}

.sectionTitle {
  color: #00ffff;
  font-size: 1.2rem;
  margin: 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rewardBox {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid #00ffff;
  padding: 1rem;
  color: #00ffff;
  border-radius: 8px;
  margin: 1rem 0;
}

.contributionDetails {
  margin-bottom: 2rem;

  h3 {
    color: white;
    margin-bottom: 0.5rem;
  }
}

.contributionTable {
  width: 100%;
  overflow-x: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  .tableHeader {
    display: grid;
    grid-template-columns: minmax(120px, 1.5fr) // Address
      minmax(120px, 1fr) // Amount
      minmax(120px, 1fr); // Date
    gap: 1rem;
    padding: 0.75rem 1rem;
    position: sticky;
    left: 0;
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
    min-width: fit-content;
  }

  .tableBody {
    max-height: 300px;
    .tableRow {
      display: grid;
      grid-template-columns: minmax(120px, 1.5fr) // Address
        minmax(120px, 1fr) // Amount
        minmax(120px, 1fr); // Date
      gap: 1rem;
      padding: 0.75rem 1rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      align-items: center;
      min-width: fit-content;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      .address {
        // font-family: monospace;
        color: #4895ef;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .amountValue {
        color: #00b8d4;
      }

      .date {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.totalContributions {
  color: #00b8d4;
  font-weight: 600;
  margin-bottom: 1rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;

  .paginationButton {
    padding: 0.5rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 4px;
    color: #00ffff;
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background: rgba(0, 255, 255, 0.2);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  flex-direction: column; // 改为竖向排列
  gap: 0.75rem; // 上下间距
  margin: 1rem 0;
}

.inviteLink {
  width: 100%; // 占满宽度
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid #00ffff;
  padding: 0.75rem;
  color: #00ffff;
  border-radius: 4px;
  font-size: 0.9rem;
  text-align: center;
  // word-break: break-all;
}
.copyButton {
  width: 100%; // 占满宽度
  background: #00ffff;
  color: #001a1a;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.9rem;
  text-align: center;
  margin: 1rem 0;

  &:hover {
    background: #00cccc;
  }
}

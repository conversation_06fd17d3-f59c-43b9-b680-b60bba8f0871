import { getMetalsDataFromOwnServer } from "@/actions/getMetalsDataFromOwnServer";
import { getNewsDataFromOwnServer } from "@/actions/getNewsDataFromOwnServer";
import MainPage from "@/components/MainPage/MainPage";

const page = async () => {
  const [metalsData, newsData] = await Promise.all([
    getMetalsDataFromOwnServer(),
    getNewsDataFromOwnServer(),
  ]);

  const mainPageProps = {
    metalsData,
    newsData,
  };

  return (
    <>
      {/* <MainPage /> */}
      <MainPage {...mainPageProps} />
    </>
  );
};

export default page;

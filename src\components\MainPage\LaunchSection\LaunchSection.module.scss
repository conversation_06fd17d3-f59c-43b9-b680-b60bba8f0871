@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  position: relative;
  width: 100%;
  height: 100%;
  //   border: 1px solid yellow;
  background: $color-black-transparent;
  backdrop-filter: blur(2px);
  @include row-center;
  align-items: flex-end;

  .hexagon:nth-child(1) {
    position: absolute;
    top: 58px;
    right: 14px;
  }
  .hexagon:nth-child(2) {
    position: absolute;
    top: 190px;
    right: 248px;
  }
  .hexagon:nth-child(3) {
    position: absolute;
    top: -80px;
    right: 248px;
  }
  .hexagon:nth-child(4) {
    position: absolute;
    top: 55px;
    right: 483px;
  }
  .hexagon:nth-child(5) {
    position: absolute;
    top: -80px;
    right: 717px;
  }

  .userProfileContainer {
    width: 1600px;
    height: 85%;
    @include row-center;
    // border: 1px solid green;

    .userProfileWrapper {
      width: 100%;
      height: 100%;
      // border: 1px solid yellow;
    }
  }
}

// import { useSnapshot } from "valtio";
import styles from "./LeaderBoardContent.module.scss";
import Leaderboard from "./Leaderboard/Leaderboard";
import Header from "./Header/Header";
import Summary from "./Summary/Summary";
import UserTask from "./UserTask/UserTask";
import DailyCheckin from "./DailyCheckin/DailyCheckin";
import Rewards from "./Rewards/Rewards";
import { pointsDataStore, PointsData, TUserTask } from "@/stores/pointsData";
import React, { useEffect, useState } from "react";
import { useAccount } from "wagmi";
import { useSnapshot } from "valtio";
import { buttonEffect } from "@/animations/animations";
import { motion } from "framer-motion";
import { connectWalletModalStore } from "@/stores/connectWalletModal";

const LeaderBoardContent = () => {
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const { isConnected, address } = useAccount();
  const { pointsData, userTasks } = useSnapshot(pointsDataStore);

  useEffect(() => {
    const fetchData = async () => {
      if (isConnected && address) {
        await Promise.all([
          pointsDataStore.fetchPointsData(address),
          pointsDataStore.fetchUserTasks(address),
        ]);
      }
    };

    fetchData();
  }, [isConnected, address]);

  return (
    <>
      <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>Points System</h1>
          {/* <h2 className={styles.subTitle}>Claim Your $ASTEROID Token</h2> */}
        </div>
        {/* <img src={dotDotDot.src} alt="dot dot dot icon" /> */}
      </div>
      <div className={styles.scrollArea}>
        <Header />
        {isConnected ? (
          <>
            <Summary pointsData={pointsData as PointsData} />
            {/* <Rewards /> */}
            <DailyCheckin />
            <UserTask userTasks={userTasks as TUserTask} />
            <Leaderboard pointsData={pointsData as PointsData} />
          </>
        ) : (
          <>
            <h2 className={styles.loginNote}>
              Please Log in to view more details
            </h2>
            <motion.div
              className={styles.loginButton}
              whileTap={buttonEffect.tap}
              onClick={() => {
                connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
              }}
            >
              Log in <span>&nbsp;or&nbsp;</span> Register Now
            </motion.div>
          </>
        )}
        {/* <UserTask userTasks={userTasks as TUserTask} /> */}
      </div>
    </>
  );
};

export default LeaderBoardContent;

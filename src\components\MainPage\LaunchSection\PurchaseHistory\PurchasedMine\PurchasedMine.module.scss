@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  flex: 1.3;
  width: 100%;
  height: 100%;
  @include row-between;
  justify-content: flex-start;
  gap: $spacing-md;

  img {
    width: 330px;
    height: 100%;
    object-fit: cover;
  }

  .detailsWrapper {
    width: 100%;
    height: 100%;
    @include col-center;
    align-items: flex-start;
    gap: $spacing-xs;

    .details {
      h5 {
        font-size: $font-size-lg;
        // color: $color-primary;
      }

      p {
        // font-weight: $font-weight-bold;
        font-size: $font-size-lg;
        color: $color-primary;
      }
    }
  }
}

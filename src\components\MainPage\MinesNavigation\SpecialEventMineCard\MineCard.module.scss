@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(-15deg) rotateY(0deg);
$card-width: 350px;

.rippleContainer {
  position: absolute;
}

.container {
  position: absolute;
  height: 68px;
  cursor: pointer;
  @include row-center;

  .overlay {
    background: black;
    opacity: 0.6;
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .closeButton {
    position: absolute;
    outline: none;
    border: none;
    border-radius: 100%;
    height: 25px;
    width: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: inherit;
    position: absolute;
    top: 16px;
    right: $padding-lg;
    cursor: pointer;
    z-index: 1;

    &:hover {
      background-color: $color-primary-transparent-contrast;
    }
  }

  .plusButton {
    position: absolute;
    z-index: $z-index-1;
    top: 50px;
    right: -39px;
    cursor: pointer;
  }

  .cardwrapper {
    height: 100%;
    // margin-left: $margin-sm;

    .cardHeader {
      width: $card-width;
      height: 100%;
      position: relative;
      background: $color-black-transparent;
      backdrop-filter: blur(20px);
      @include row-center;
      padding: $padding-sm;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%
      );
      // border: 1px solid yellow;

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary-contrast;
        clip-path: polygon(
          0 0,
          calc(100% - 32px) 0,
          100% 32px,
          100% 100%,
          0 100%,
          0 0,
          1px 1px,
          1px calc(100% - 1px),
          calc(100% - 1px) calc(100% - 1px),
          calc(100% - 1px) calc(32px + 0.41px),
          calc(100% - 32px - 0.41px) 1px,
          1px 1px
        );
      }

      .cardContentWrapper {
        padding: $padding-md;
      }
    }

    .checkDetailsButton {
      $margin-offset: 22px;
      width: $card-width;
      height: 22px;
      @include row-center;
      position: relative;
      -webkit-perspective: 100px;
      perspective: 100px;

      &::after {
        position: absolute;
        width: $card-width - 28px;
        height: 100%;
        background: $color-black-transparent;
        backdrop-filter: blur(20px);
        border: $border-width-2xs solid $color-primary;
        border-top: none;
        content: "";
        left: $margin-offset;
        top: 0;
        z-index: -1;
        -webkit-transform: $rotation-angle;
        transform: $rotation-angle;
      }

      .checkDetailsButtonText {
        font-size: $font-size-xs;
        font-weight: $font-weight-light;
        color: $color-primary-transparent-contrast;
        // offset left
        margin-left: $margin-offset;

        &:hover {
          color: $color-primary;
        }

        .auditedText {
          color: gray;
          font-style: italic;
          @include row-center;
          gap: 0.5rem;
        }
      }
    }
  }
}

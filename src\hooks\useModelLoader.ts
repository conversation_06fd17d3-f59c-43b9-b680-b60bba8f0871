import { useGLTF } from "@react-three/drei";
import { MODEL_CONFIG, type ModelKey } from "@/constants/models";
import { GLTF } from "three-stdlib";
import * as THREE from "three";

// 定义基础的 GLTF 类型
type GLTFResult<T = any> = GLTF & {
  nodes: Record<string, THREE.Mesh>;
  materials: Record<string, THREE.Material>;
} & T;

// 获取模型 URL
export const getModelUrl = (modelKey: ModelKey) => {
  return {
    CDN: `${MODEL_CONFIG.CDN_BASE_URL}${MODEL_CONFIG.MODELS[modelKey]}`,
    LOCAL: `/models${MODEL_CONFIG.MODELS[modelKey]}`,
  };
};

// 加载模型的 Hook
export const useModelLoader = <T = any>(modelKey: ModelKey) => {
  const urls = getModelUrl(modelKey);
  const model = useGLTF(urls.CDN) as GLTFResult<T>;

  // 预加载
  useGLTF.preload(urls.CDN);

  return {
    model,
    urls,
  };
};

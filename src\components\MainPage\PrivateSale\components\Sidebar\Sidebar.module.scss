@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  border-right: 1px solid grey;
  flex: 1;
}

.iconsGroup {
  width: 100%;
  height: 100%;
  @include col-between;
  padding: 32px 0;
}

.logoContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 10px;
}

.socialIcons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.iconWrapper {
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
}

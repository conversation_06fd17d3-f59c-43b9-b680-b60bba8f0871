import TokenPrice from "@/components/common/TokenPrice";
import useActiveListing from "./hooks/useActiveListing";
import styles from "./TradeDashboard.module.scss";
import usePurchaseAllListingAmount from "./hooks/usePurchaseAllListingAmount";
import { useState } from "react";
import useApprove from "./hooks/useApprove";
import BuyListingModal from "./TradeModals/BuyListingModal/BuyListingModal";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
import { mineCardStore } from "@/stores/mineCard";
import { is } from "@react-three/fiber/dist/declarations/src/core/utils";

const Listings = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const {
    allActiveListingSortedByPriceCombined,
    setFilterValue,
    allActiveListingSortedByPriceFiltered,
  } = useActiveListing();
  const { isPurchasingListing, isWaitingForPurchaseListing, listingId } =
    usePurchaseAllListingAmount();
  const { isApprovedForAll } = useApprove();
  const [isBuyListingModalOpen, setIsBuyListingModalOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={styles.listings}>
      <div
        className={styles.headerContainer}
        onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className={styles.mainHeader}>LISTINGS</h2>
        <span className={styles.toggleIcon}>{isOpen ? "-" : "+"}</span>
      </div>
      {isOpen && (
        <div className={styles.listingsTable}>
          <div
            className={styles.tableHeader}
            style={{ gridTemplateColumns: "1.66fr 1.66fr 1.66fr 1fr 100px" }}
          >
            <div>Price per Token</div>
            <div>Quantity</div>
            <div>Selling Price</div>
            {/* <div>Expiration</div> */}
            <div>Action</div>
          </div>

          <div className={styles.tableBody}>
            {allActiveListingSortedByPriceCombined &&
            allActiveListingSortedByPriceCombined.length > 0 ? (
              allActiveListingSortedByPriceCombined.map((listing, index) => (
                <div
                  className={styles.tableRow}
                  key={index}
                  style={{
                    gridTemplateColumns: "1.66fr 1.66fr 1.66fr 1fr 100px",
                  }}
                >
                  <div className={styles.price}>
                    <TokenPrice
                      amount={listing.pricePerToken}
                      tokenAddress={listing.paymentToken}
                      showCurrency={true}
                      currencySymbol={selectedMine.currency}
                    />
                  </div>
                  <div className={styles.quantity}>
                    {listing.amount.toString()}
                  </div>
                  <div className={styles.price}>
                    <TokenPrice
                      amount={listing.pricePerToken * listing.amount}
                      tokenAddress={listing.paymentToken}
                      showCurrency={true}
                      currencySymbol={selectedMine.currency}
                    />
                  </div>
                  {/* <div className={styles.expiration}>
                  {formatUnixTimestamp(Number(listing.expirationTime))}
                </div> */}
                  <div>
                    <button
                      className={`${styles.buyButton} ${
                        isPurchasingListing || isWaitingForPurchaseListing
                          ? styles.disabled
                          : ""
                      }`}
                      onClick={() => {
                        setFilterValue(listing.pricePerToken);
                        setIsBuyListingModalOpen(true);
                      }}
                      // onMouseEnter={() => handleMouseOver(index)}
                      disabled={
                        isPurchasingListing ||
                        isWaitingForPurchaseListing ||
                        !isApprovedForAll
                      }
                      style={{
                        opacity:
                          isPurchasingListing ||
                          isWaitingForPurchaseListing ||
                          !isApprovedForAll
                            ? 0.5
                            : 1,
                        pointerEvents:
                          isPurchasingListing ||
                          isWaitingForPurchaseListing ||
                          !isApprovedForAll
                            ? "none"
                            : "auto",
                      }}
                    >
                      {(isPurchasingListing || isWaitingForPurchaseListing) &&
                      listingId === listing.listingId
                        ? "Processing..."
                        : "BUY"}
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noListings}>
                No active listings available
              </div>
            )}
          </div>
        </div>
      )}
      <BuyListingModal
        isOpen={isBuyListingModalOpen}
        onClose={() => setIsBuyListingModalOpen(false)}
        allActiveListingSortedByPriceFiltered={
          allActiveListingSortedByPriceFiltered
        }
      />
    </div>
  );
};

export default Listings;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin actionButton() {
  width: 100px;
  padding: $padding-sm;
  border: $border-width-2xs solid $color-primary;
  font-size: $font-size-md;
  font-family: $font-family-saira;
  background: $color-black-transparent;
  color: $color-primary-contrast;
  cursor: pointer;

  &:hover {
    background: $color-primary-contrast;
    color: $color-primary;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0) translateX(-50%);
  }
  50% {
    transform: translateY(-10px) translateX(-50%);
  }
}

.container {
  width: 100%;
  // height: 100%

  &::-webkit-scrollbar {
    display: none;
  }
  .statement {
    position: absolute;
    width: 100%;
    height: 80%;
    overflow-y: auto;
    // border: $border-width-xs solid $color-primary;

    .title {
      font-weight: $font-weight-extrabold;
    }

    .lastUpdated {
      font-weight: $font-weight-light;
    }

    p {
      font-weight: $font-weight-extralight;
    }
  }

  .buttonGroup {
    @include row-center;
    gap: $spacing-md;
    padding-top: $padding-lg;
    padding: 10px 0;

    .cancelButton {
      @include actionButton();
      color: $color-danger;
      border: $border-width-2xs solid $color-danger;

      &:hover {
        background: $color-danger;
      }
    }
    .confirmButton {
      @include actionButton();
    }
  }
  .scrollButton {
    @include actionButton();
    @include row-center;
    position: fixed;
    bottom: $margin-md;
    // right: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: $color-black-transparent-medium;
    color: $color-primary;
    width: auto;
    padding: $padding-sm $padding-md;
    border-radius: $border-radius-md;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: bounce 1s infinite ease-in-out;

    &:hover {
      background: $color-primary-contrast;
      color: $color-primary;
      animation-play-state: paused;
    }
  }
}

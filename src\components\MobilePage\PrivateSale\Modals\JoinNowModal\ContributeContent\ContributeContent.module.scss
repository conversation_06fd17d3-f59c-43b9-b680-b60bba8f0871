@import "@/styles/mixins.scss";

.container {
  color: white;
}

.introText {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);

  .highlight {
    color: #00ffff;
  }
}

.rewardSection {
  margin-bottom: 2rem;
}

.sectionHeader {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #00ffff;
}

.sectionDescription {
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
}

.tokenText {
  color: #00ffff;
  font-weight: bold;
}

.inviteSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.inviteLink {
  background-color: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  padding: 12px;
  color: #00ffff;
  font-family: monospace;
  font-size: 18px;
  width: 100%;

  &::placeholder {
    color: rgba(0, 255, 255, 0.5);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.actionButton {
  font-size: 18px;
  background-color: #00ffff;
  border: none;
  color: #001a1a;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
  width: 100%;

  &:hover:not(:disabled) {
    background-color: #00cccc;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.loadingState {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.dots {
  animation: dotAnimation 1.5s infinite;
}

@keyframes dotAnimation {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}

.divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(0, 255, 255, 0.3),
    transparent
  );
  margin: 0.5rem 0; // 增加上下间距
  position: relative;

  &::before {
    // 添加发光效果
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      to right,
      transparent,
      rgba(0, 255, 255, 0.1),
      transparent
    );
    filter: blur(2px);
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin subtitleText() {
  font-size: $font-size-2xl;
  font-weight: $font-weight-light;
  letter-spacing: 0.2rem;
  color: $color-primary-contrast;
  text-transform: uppercase;
  text-align: center;
}

.container {
  // position: absolute;
  // bottom: 10%;
  // left: 50%;
  // transform: translate(-50%);
  position: relative;
  width: 100vw;
  height: 100vh;

  .textBeforeAnimation {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translate(-50%);
    // transition: bottom 2s;
    // transition-delay: 1s;

    @include titleText();

    @media screen and (max-width: 1279px) {
      font-size: $font-size-5xl;
    }
  }

  .tooltip {
    position: absolute;
    left: 50%;
    bottom: 50%;
    transform: translate(-50%, -50%);
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $color-primary-contrast;
    background: $color-black-transparent-medium;
    padding: $padding-md;
    // border: $border-width-2xs solid $color-primary;
  }
  .scrollDowninstruction {
    position: absolute;
    left: 50%;
    bottom: 21%;
    transform: translate(-50%);

    font-size: $font-size-2xl;
    font-weight: $font-weight-extralight;
    color: $color-primary;
  }

  .scrolldownIcon {
    width: 60px;
    height: 60px;
    border: 1px solid $color-primary;
    border-radius: 50%;
    background: $color-black-transparent;
    // backdrop-filter: blur(5px);
    position: absolute;
    left: 50%;
    bottom: 11%;
    transform: translate(-50%);
    animation: down 1.5s ease-in-out infinite;
    -webkit-animation: down 1.5s ease-in-out infinite;
    &::before {
      content: "";
      position: absolute;
      top: 15px;
      left: 18px;
      width: 18px;
      height: 18px;
      border-left: 1px solid $color-primary;
      border-bottom: 1px solid $color-primary;
      transform: rotate(-45deg);
    }
  }

  .slogan {
    position: absolute;
    left: 50%;
    bottom: 26%;
    transform: translate(-50%);

    // background: $color-black-transparent-medium;
    padding: $padding-md;
    text-transform: uppercase;
    font-size: $font-size-2xl;
    font-weight: $font-weight-extrabold;
    color: white;

    @media screen and (max-width: 1279px) {
      font-size: $font-size-xl;
      text-align: center;
    }
  }

  .textWrapperAfterAnimation {
    width: 100%;
    background: linear-gradient(
      to bottom,
      $color-black-transparent-medium,
      $color-black-transparent,
      $color-black-transparent-light
    );
    // backdrop-filter: blur(5px);
    padding: $padding-md;
    padding-bottom: $padding-lg;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
    // transition: bottom 2s;
    // transition-delay: 1s;
    .text {
      @include titleText();

      @media screen and (max-width: 1279px) {
        font-size: $font-size-5xl;
      }
    }

    .subText {
      line-height: 108%;
      // width: 700px;
      // background: $color-black-transparent-medium;
      // padding: $padding-sm $padding-md;
      // border-radius: $border-radius-sm;
      // @include row-center;

      .animatedText {
        @include subtitleText();

        @media screen and (max-width: 1279px) {
          line-height: 120%;
          font-size: $font-size-xl;
        }
      }

      .animatedTypedText {
        @include subtitleText();
        font-size: $font-size-4xl;
        font-weight: $font-weight-extrabold;
        color: $color-primary;
        // -webkit-text-stroke: 2px $color-primary;

        @media screen and (max-width: 1279px) {
          font-size: $font-size-3xl;
        }
      }
    }
  }
}

@keyframes background-pan {
  from {
    background-position: 0% center;
  }

  to {
    background-position: -200% center;
  }
}

@keyframes down {
  0% {
    transform: translate(-50%);
  }
  20% {
    transform: translate(-50%, 15px);
  }
  40% {
    transform: translate(-50%);
  }
}

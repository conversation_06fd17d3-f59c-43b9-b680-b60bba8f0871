@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);

.container {
  position: absolute;
  z-index: $z-index-1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  //   border: 1px solid yellow;
  background: $color-black-transparent-light;
  backdrop-filter: blur(22px);
  @include row-center;

  .modalFrame {
    width: 1734px;
    height: 849px;
    position: relative;
    background: $color-black-transparent;
    clip-path: polygon(
      0 0,
      calc(100% - 80px) 0,
      100% 80px,
      100% calc(100% - 48px),
      calc(100% - 48px) 100%,
      48px 100%,
      0 calc(100% - 48px)
    );

    .closeButton {
      position: absolute;
      width: 250px;
      height: 50px;
      // z-index: $z-index-1;
      top: 32px;
      right: -68px;
      cursor: pointer;
      border: $border-width-xs solid $color-primary;
      transform: rotate(45deg);

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(45deg);
      }

      &:hover {
        background: $color-primary-contrast;
      }
    }

    .sellMineOnline {
      width: 1200px;
      height: 50px;
      @include row-between;
      position: absolute;
      bottom: 0;
      left: 72px;
      -webkit-perspective: 100px;
      perspective: 100px;
      padding: $padding-xs $padding-3xl 0;

      h1 {
        font-size: $font-size-md;
        text-transform: uppercase;
        color: #045b61;
      }

      &::after {
        position: absolute;
        width: 100%;
        height: 100%;
        background: $color-primary-transparent;
        backdrop-filter: blur(5px);
        border: $border-width-xs solid $color-primary;
        border-bottom: none;
        border-right: none;
        content: "";
        left: 0;
        top: 0;
        z-index: -1;
        -webkit-transform: $rotation-angle;
        transform: $rotation-angle;
      }
    }

    .downloadButton {
      width: 600px;
      height: 150px;
      @include row-between;
      position: absolute;
      bottom: 0;
      right: -119px;
      -webkit-perspective: 150px;
      perspective: 150px;
      padding: $padding-xs $padding-4xl 0;
      cursor: pointer;

      h1 {
        font-size: $font-size-4xl;
        color: $color-primary;
        text-transform: uppercase;

        span {
          font-size: $font-size-xl;
          color: #616161;
        }
      }

      &:hover::after {
        background: $color-primary-contrast;
      }

      &::after {
        position: absolute;
        width: 100%;
        height: 100%;
        background: $color-black-transparent-light;
        backdrop-filter: blur(20px);
        border: $border-width-xs solid $color-primary;
        border-bottom: none;
        content: "";
        left: 0;
        top: 0;
        z-index: -1;
        -webkit-transform: $download-rotation-angle;
        transform: $download-rotation-angle;
      }
    }

    .contentWrapper {
      width: 100%;
      height: 94%;
      // border: 1px solid yellow;
      padding: $padding-3xl $padding-3xl 0;

      @include col-between;
      gap: $spacing-sm;
      align-items: flex-start;
    }

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary;
      clip-path: polygon(
        0 0,
        calc(100% - 81px) 0,
        100% 81px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px),
        0 0,
        1px 1px,
        1px calc(100% - 48px - 0.41px),
        calc(48px + 0.41px) calc(100% - 1px),
        calc(100% - 48px - 0.41px) calc(100% - 1px),
        calc(100% - 1px) calc(100% - 48px - 0.41px),
        calc(100% - 1px) calc(48px + 0.41px),
        calc(100% - 48px - 0.41px) 1px,
        1px 1px
      );
    }
  }
}

import { CameraC<PERSON><PERSON>s, Float, useCursor } from "@react-three/drei";
import Lighting from "./Lighting";
import { MutableRefObject, useState, useEffect } from "react";
import { hoverModelStore } from "@/stores/hoverModel";
import { useSnapshot } from "valtio";
import { universeAnimationStore } from "@/stores/universeAnimation";
import { useRouter } from "next/navigation";
import { scrollContainerStore } from "@/stores/scrollContainer";
import { useModelLoader } from "@/hooks/useModelLoader";

type AsteroidSceneProps = {
  cameraControlsRef: MutableRefObject<CameraControls | undefined>;
  replace: ReturnType<typeof useRouter>["replace"];
  scaleFactor: number;
};

const AsteroidScene = ({
  cameraControlsRef,
  replace,
  scaleFactor,
}: AsteroidSceneProps) => {
  const { model: asteroid } = useModelLoader("ASTEROID");
  const hoverModelSnapshot = useSnapshot(hoverModelStore);
  const universeAnimationSnapshot = useSnapshot(universeAnimationStore);
  const scrollContainerSnapshot = useSnapshot(scrollContainerStore);
  useCursor(hoverModelSnapshot.hovered);

  const handleFinalAnimation = async () => {
    universeAnimationSnapshot.setShowFinalAnimation(true);
    await cameraControlsRef.current?.setLookAt(0, 0, -193, 0, 0, -200, true);
    await cameraControlsRef.current?.rotate(-0.25, 0, true);
    cameraControlsRef.current?.setLookAt(-1.25, 0, -196, -1.25, 0, -200, true);

    replace("/main");
  };

  return (
    <Float
      speed={1} // Animation speed, defaults to 1
      rotationIntensity={10} // XYZ rotation intensity, defaults to 1
      floatIntensity={1} // Up/down float intensity, works like a multiplier with floatingRange,defaults to 1
      // floatingRange={[-5, 5]}
    >
      <mesh
        scale={scaleFactor}
        position={[0, 0, 0]}
        onDoubleClick={handleFinalAnimation}
        onPointerOver={() => hoverModelSnapshot.set(true)}
        onPointerOut={() => hoverModelSnapshot.set(false)}
      >
        <primitive object={asteroid.scene} />
      </mesh>
    </Float>
  );
};

export default AsteroidScene;

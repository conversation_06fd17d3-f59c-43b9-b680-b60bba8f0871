"use server";
import * as z from "zod";

export const getNewsDataFromOwnServer = async () => {
  let newsData: TNewsResponse["data"]["newsItems"] = [];
  const url = `https://asteroidx.io/api/news/list?pageNum=1&pageSize=20`;
  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TNewsResponse;
    newsData = data.data.newsItems.map((news) => {
      return {
        ...news,
      };
    });
  }

  return newsData;
};

// types
const NewsItemSchema = z.object({
  title: z.string(),
  description: z.string(),
  link: z.string(),
  source: z.string(),
  timestamp: z.coerce.date(),
});

const DataSchema = z.object({
  newsItems: z.array(NewsItemSchema),
});

const TNewsResponseSchema = z.object({
  code: z.number(),
  info: z.string(),
  data: DataSchema,
});
export type TNewsResponse = z.infer<typeof TNewsResponseSchema>;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.deepDetailsWrapper {
  padding: 0 $padding-lg;

  .deepDetailsContainer {
    // border: $border-width-2xs solid $color-primary;
    // background: $color-primary-contrast;
    // border-radius: 0.5rem;
    // margin-bottom: $margin-md;
    // padding: $padding-md 0;
    padding-bottom: $padding-sm;
    line-height: 180%;

    .deepDetails {
      @include row-between;

      h1 {
        font-size: $font-size-sm;
        text-transform: uppercase;
      }

      .totalShare {
        color: gray;
      }

      .shareSold {
        color: $color-warning;
      }

      .shareToPurchase {
        color: $color-warning;

        span {
          font-size: $font-size-md;
          text-transform: uppercase;
        }
      }
    }
  }
}

// Add these styles to your existing CSS module file

.investorAttractiveSection {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.6);
  }
}

.gradientText {
  background: linear-gradient(
    to right,
    #ffd700,
    /* Gold */ #f5a623,
    /* Amber */ #ff8c00,
    /* Dark Orange */ #f5a623,
    #ffd700
  );
  background-size: 200% auto;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientMove 3s linear infinite;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.returnValue {
  color: #ffd700;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  transition: all 0.3s ease;

  &:hover {
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
  }
}

@keyframes gradientMove {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

import { TypeAnimation } from "react-type-animation";
import styles from "./TitleText.module.scss";

const TitleText = ({ delay }: { delay: number }) => {
  return (
    <TypeAnimation
      sequence={[
        "",
        delay * 1000,
        "WELCOME",
        100,
        "WELCOME TO",
        100,
        "WELCOME TO ASTEROID",
        100,
      ]}
      className={styles.gradientText}
    />
  );
};

export default TitleText;

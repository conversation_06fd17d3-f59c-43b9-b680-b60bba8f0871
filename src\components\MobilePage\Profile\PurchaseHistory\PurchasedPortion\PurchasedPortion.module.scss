@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  // flex: 1.2;
  width: 100%;
  // height: 150%;
  // @include row-center;
  // border: 1px solid yellow;
}

.userBalanceSection {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: $padding-md;
  // margin-bottom: $margin-md;
  text-align: center;
}

.balanceLabel {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-bottom: $margin-sm;
}

.balanceValue {
  color: $color-primary;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: $margin-sm;
}

.balancePercentage {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.introText {
  font-size: 24px;
  margin-bottom: 20px;
  color: #ccc;
}

.highlight {
  color: #00b8d4;
}

.rewardSection {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
}

.sectionHeader {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #00b8d4;
  font-size: 32px;
  font-weight: bold;
}

.sectionNumber {
  margin-right: 8px;
  font-family: monospace;
}

.sectionDescription {
  font-size: 24px;
  margin-bottom: 15px;
  color: #ccc;
}

.tokenText {
  color: #00b8d4;
  font-weight: 500;
}

.inviteSection {
  @include row-between;

  .inviteLink {
    background-color: rgba(0, 184, 212, 0.1);
    border: 1px solid rgba(0, 184, 212, 0.3);
    border-radius: 4px;
    padding: 10px 15px;
    color: #00b8d4;
    font-family: monospace;
    font-size: 24px;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }

  .actionButton {
    font-size: 24px;
    background-color: transparent;
    border: 1px solid #00b8d4;
    color: #00b8d4;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    // align-self: flex-end;

    &:hover {
      background-color: rgba(0, 184, 212, 0.1);
    }
  }
}

.loadingState {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.loadingDot {
  width: 6px;
  height: 6px;
  background-color: currentColor;
  border-radius: 50%;
  display: inline-block;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.loadingDot:nth-child(1) {
  animation-delay: 0s;
}

.loadingDot:nth-child(2) {
  animation-delay: 0.2s;
}

.loadingDot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin actionButton() {
  width: 100px;
  padding: $padding-sm;
  border: $border-width-2xs solid $color-primary;
  font-size: $font-size-md;
  font-family: $font-family-saira;
  background: $color-black-transparent;
  color: $color-primary-contrast;
  cursor: pointer;

  &:hover {
    background: $color-primary-contrast;
    color: $color-primary;
  }
}

.container {
  width: 100%;

  .statement {
    width: 100%;
    height: 500px;
    overflow: scroll;
    // border: $border-width-xs solid $color-primary;
    &::-webkit-scrollbar {
      display: none;
    }

    .title {
      font-weight: $font-weight-extrabold;
    }

    .lastUpdated {
      font-weight: $font-weight-light;
    }

    p {
      font-weight: $font-weight-extralight;
    }
  }

  .buttonGroup {
    @include row-center;
    gap: $spacing-md;
    padding-top: $padding-lg;

    .cancelButton {
      @include actionButton();
      color: $color-danger;
      border: $border-width-2xs solid $color-danger;

      &:hover {
        background: $color-danger;
      }
    }
    .confirmButton {
      @include actionButton();
    }
  }
}

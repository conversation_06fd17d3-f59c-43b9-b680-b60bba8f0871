@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  margin-top: 2rem;
  background: $color-black-transparent;

  @media screen and (max-width: 1023px) {
    @include col-center;
    padding: 1rem;
    gap: 1rem;
  }

  .summarySection {
    @include row-center;
    gap: 2rem;

    .priceTag {
      color: $color-primary-contrast;
      font-size: 3rem;

      span {
        font-size: 1.25rem;
        color: $color-primary-contrast;
      }
    }

    .pointSection {
      width: 100%;

      .pointTitle {
        @include row-between;

        h2 {
          font-size: 2rem;
          color: $color-primary;
        }
      }
    }

    .divider {
      border: 0.5px solid white;
      height: 60px;

      @media screen and (max-width: 1023px) {
        width: 100%;
        height: 0;
      }
    }

    .tokenSection {
      width: 100%;
    }
  }
}

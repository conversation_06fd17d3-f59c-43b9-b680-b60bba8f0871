import { privateSaleABI } from "@/constants/abis/PrivateSaleABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState } from "react";
import {
  useAccount,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
  useWalletClient,
} from "wagmi";
import * as ethers from "ethers";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError } from "viem";
import { set } from "zod";
import { signContribution } from "@/actions/sign-contribution";

const useJoinNow = (onClose: () => void, openRewardsModal: () => void) => {
  const [privateSaleAddress, setPrivateSaleAddress] =
    useState<`0x${string}`>("0x");
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const [isContributing, setIsContributing] = useState(false);
  const [contributeHash, setContributeHash] = useState<`0x${string}`>("0x");
  const { data: walletClient } = useWalletClient();
  const SUPPORTED_NETWORKS = [133, 177];

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setPrivateSaleAddress(networkConfigs[chain.id].privateSaleAddress);
    } else {
      setPrivateSaleAddress("0x");
    }
  }, [chain]);

  const { isLoading: isWaitingForContribute } = useWaitForTransaction({
    confirmations: 3,
    hash: contributeHash,
    onSuccess: () => {
      showSuccess("Successfully !");
      setContributeHash("0x");
      setIsContributing(false);
      onClose();
      openRewardsModal();
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setContributeHash("0x");
      setIsContributing(false);
    },
    enabled: contributeHash !== "0x",
  });

  const contribute = async (contributeAmount: string, referralCode: string) => {
    try {
      if (
        !isConnected ||
        (chain?.id && !SUPPORTED_NETWORKS.includes(chain.id))
      ) {
        showError(`Not supported on ${chain?.name}`);
        return;
      }
      setIsContributing(true);
      const amount = ethers.parseEther(contributeAmount); // 认购 0.1 ETH
      const nonce = Math.floor(Math.random() * 1000000); // 随机 nonce

      if (!address || !chain?.id) {
        showError("Wallet not connected properly");
        setIsContributing(false);
        return;
      }

      // 直接调用服务器动作获取签名
      const signature = await signContribution({
        userAddress: address,
        amount: amount.toString(),
        referralCode,
        nonce,
        chainId: chain.id,
        contractAddress: privateSaleAddress,
      });

      if (!signature) {
        showError("Failed to get signature from server");
        setIsContributing(false);
        return;
      }

      const hash = await walletClient?.writeContract({
        address: privateSaleAddress,
        abi: privateSaleABI,
        functionName: "contribute",
        args: [
          amount,
          ethers.encodeBytes32String(referralCode) as `0x${string}`,
          BigInt(nonce),
          signature as `0x${string}`,
        ],
        value: amount,
      });

      if (hash) {
        setContributeHash(hash);
      } else {
        showError("Failed to contribute");
        setIsContributing(false);
      }
    } catch (error) {
      showError((error as BaseError).shortMessage ?? (error as Error).message);
      setIsContributing(false);
    }
  };

  return { contribute, isWaitingForContribute, isContributing };
};

export default useJoinNow;

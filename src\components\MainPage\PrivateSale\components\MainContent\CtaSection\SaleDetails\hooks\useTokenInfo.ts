import { privateSaleABI } from "@/constants/abis/PrivateSaleABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import * as ethers from "ethers";
import { formatEther } from "viem";

export type ContributionRecord = {
  contributor: string;
  amount: string;
  referralCode: string;
  timestamp: number;
};

const useTokenInfo = (referralCode: string) => {
  const [privateSaleAddress, setPrivateSaleAddress] =
    useState<`0x${string}`>("0x");
  const [rewardPoints, setRewardPoints] = useState("");
  const [contributions, setContributions] = useState<ContributionRecord[]>([]);
  const [totalContributions, setTotalContributions] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [userTotalPurchased, setUserTotalPurchased] = useState(0);
  const pageSize = 5;
  const HSK_2_DRILLX = 3.33;

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setPrivateSaleAddress(networkConfigs[chain.id].privateSaleAddress);
    } else {
      setPrivateSaleAddress("0x");
    }
  }, [chain]);

  // user contribution
  useContractRead({
    address: privateSaleAddress,
    abi: privateSaleABI,
    functionName: "getUserContribution",
    args: [address ?? `0x`],
    enabled:
      isConnected && privateSaleAddress !== "0x" && address !== undefined,
    watch: true,
    onSuccess: (data) => {
      setUserTotalPurchased(Number(data[0]) * HSK_2_DRILLX);
    },
  });

  // total contribution
  useContractRead({
    address: privateSaleAddress,
    abi: privateSaleABI,
    functionName: "getReferralInfo",
    args: [ethers.encodeBytes32String(referralCode) as `0x${string}`],
    enabled: isConnected && privateSaleAddress !== "0x" && referralCode !== "",
    watch: true,
    onSuccess: (data) => {
      const formattedAmount = formatEther(data.totalAmount);
      const value = parseFloat(formattedAmount) * 0.05;
      const truncated = Math.floor(value * 1000) / 1000;
      setRewardPoints(truncated.toFixed(3));
    },
  });

  // contributions
  useContractRead({
    address: privateSaleAddress,
    abi: privateSaleABI,
    functionName: "getReferralContributions",
    args: [
      ethers.encodeBytes32String(referralCode) as `0x${string}`,
      BigInt(currentPage * pageSize),
      BigInt(pageSize),
    ],
    enabled: isConnected && privateSaleAddress !== "0x" && referralCode !== "",
    watch: true,
    onSuccess: (data) => {
      const [records, total] = data as unknown as [
        {
          contributor: `0x${string}`;
          amount: bigint;
          referralCode: `0x${string}`;
          timestamp: bigint;
        }[],
        bigint,
      ];

      const formattedRecords = records.map((record) => ({
        contributor: record.contributor,
        amount: formatEther(record.amount),
        referralCode: ethers.decodeBytes32String(record.referralCode),
        timestamp: Number(record.timestamp),
      }));

      setContributions(formattedRecords);
      setTotalContributions(Number(total));
      setIsLoading(false);
    },
    onError: (error) => {
      //console.error("Error fetching contributions:", error);
      setIsLoading(false);
    },
  });

  const changePage = (newPage: number) => {
    if (newPage >= 0 && newPage * pageSize < totalContributions) {
      setIsLoading(true);
      setCurrentPage(newPage);
    }
  };

  const totalPages = Math.ceil(totalContributions / pageSize);

  return {
    rewardPoints,
    contributions,
    totalContributions,
    currentPage,
    totalPages,
    changePage,
    isLoading,
    pageSize,
    userTotalPurchased,
  };
};

export default useTokenInfo;

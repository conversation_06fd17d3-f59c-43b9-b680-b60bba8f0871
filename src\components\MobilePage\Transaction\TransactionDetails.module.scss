@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  @include row-between;
  margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 87%;
  height: 100%;
  overflow-y: auto;
  // padding-right: $padding-md;
  padding-bottom: $padding-lg;

  &::-webkit-scrollbar {
    // margin-left: $margin-sm;
    // width: 8px;
    // background-color: $color-black-transparent;
    display: none;
  }

  /* 为滚动条轨道设置样式 */
  // &::-webkit-scrollbar-track {
  //   border-left: 1px solid $color-primary;
  // }

  /* 为滚动条滑块设置样式 */
  &::-webkit-scrollbar-thumb {
    background-color: $color-primary;
    margin-left: $margin-sm;
    border-left: 2px solid black;
    border-right: 2px solid black;
  }

  .commoditiesDetailsWrapper {
    width: 100%;
    padding: $padding-md 0;
    border-bottom: 1px solid #e7e7e70f;
    @include row-between;

    .commoditiesName {
      font-size: $font-size-md;
      text-transform: uppercase;
    }

    .commoditiesPriceWrapper {
      line-height: 60%;
      @include col-center;
      // align-items: flex-end;
      margin-top: -25px;
    }
    .commoditiesPrice {
      font-size: $font-size-sm;
      color: $color-primary;
    }
  }

  .bodyWrapper {
    padding: $padding-sm 0;
    border-bottom: 1px solid #e7e7e70f;
    cursor: pointer;
    display: flex; /* 设置为 flex 容器 */
    align-items: center; /* 在交叉轴上居中对齐项目 */
    justify-content: space-between; /* 在主轴上将项目分散对齐，一个在左边一个在右边 */

    .label {
      width: fit-content;
      font-size: $font-size-xs;
      // font-weight: $font-weight-light;
      color: $color-primary;
      border-radius: $border-radius-sm;
    }
    .bodyText {
      font-size: $font-size-md;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }

    &:hover {
      .time,
      .bodyText {
        color: $color-primary;
      }
    }
  }
}

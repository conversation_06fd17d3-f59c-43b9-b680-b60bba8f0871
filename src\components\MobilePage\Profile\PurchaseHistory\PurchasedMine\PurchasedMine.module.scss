@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.bodyWrapper {
  width: 100%;
  margin-bottom: $margin-md;
  .titleContainer {
    width: 100%;
    // padding: $padding-sm $padding-sm 0 0;
    margin-bottom: $margin-sm;
    // border: 1px solid yellow;

    .title {
      font-size: $font-size-md;
      font-weight: $font-weight-semibold;
      color: $color-primary;
    }
  }

  .imgContainer {
    width: 100%;
    @include row-center;
    gap: $spacing-md;
    .imageFrame {
      width: 100%;
      height: 80%;
      position: relative;
      background: $color-primary-transparent;
      backdrop-filter: blur(20px);
      overflow: hidden;
      // padding: px;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%
      );
      // border: 1px solid yellow;

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        clip-path: polygon(
          0 0,
          calc(100% - 32px) 0,
          100% 32px,
          100% 100%,
          0 100%,
          0 0,
          1px 1px,
          1px calc(100% - 1px),
          calc(100% - 1px) calc(100% - 1px),
          calc(100% - 1px) calc(32px + 0.41px),
          calc(100% - 32px - 0.41px) 1px,
          1px 1px
        );
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        display: block;
      }
    }
  }
  .logoWrapper {
    width: 70%;
    // @include row-between;
    // border: 1px solid yellow;
    .detailsWrapper {
      width: 100%;
      height: 100%;
      @include col-center;
      align-items: flex-start;
      gap: $spacing-xs;

      .details {
        h5 {
          font-size: $font-size-sm;
          // color: $color-primary;
        }

        p {
          // font-weight: $font-weight-bold;
          font-size: $font-size-xs;
          color: $color-primary;
        }
      }
    }
  }
}

import variables from "@/styles/variables.module.scss";

const PrivateSaleIcon = ({ color = variables.colorPrimaryContrast }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Frame">
        <path
          id="Vector"
          d="M32 4L10 30H24L20 52L42 24H28L32 4Z"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export default PrivateSaleIcon;

@import "@/styles/variables.module.scss";
@import "@/styles/mixins.scss";

@mixin title($color) {
  font-size: $font-size-2xl;
  color: $color;
}

@mixin subtitle($color) {
  font-size: $font-size-sm;
  color: $color;
  font-weight: $font-weight-light;
}

.container {
  // width: 380px;
  @include row-center;
  padding: $padding-sm;
  gap: $spacing-lg;
  // border: 1px solid yellow;
  line-height: 107%;
  cursor: pointer;

  .title {
    @include title($color-primary-contrast);
  }

  .titleHover {
    @include title($color-primary);
  }
  .titleFocus {
    @include title($color-primary);
  }

  .subtitle {
    @include subtitle($color-primary-contrast);
  }

  .subtitleHover {
    @include subtitle($color-primary);
  }
  .subtitleFocus {
    @include subtitle($color-primary-transparent-contrast);
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(3deg) rotateY(0deg);

.container {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: $z-index-1;
  // border: 2px solid yellow;
  @include row-center;

  .trapezoid {
    width: 1544px;
    height: 185px;
    @include row-center;
    position: relative;
    -webkit-perspective: 100px;
    perspective: 100px;

    &::after {
      position: absolute;
      width: 100%;
      height: 100%;
      background: $color-primary-transparent;
      backdrop-filter: blur(20px);
      border: $border-width-xs solid $color-primary;
      border-bottom: none;
      content: "";
      left: 0;
      top: 0;
      z-index: -1;
      -webkit-transform: $rotation-angle;
      transform: $rotation-angle;
    }
  }
}

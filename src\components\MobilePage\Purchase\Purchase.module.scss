@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  height: 100%;
  width: 100%;
  // cursor: pointer;
  // padding: $padding-lg;
  @include row-center;

  .cardwrapper {
    width: 100%;
    height: 100%;
    // margin-left: $margin-sm;

    .cardHeader {
      width: 100%;
      height: 100%;
      position: relative;

      background: $color-black-transparent;
      backdrop-filter: blur(20px);
      // @include row-center;
      padding: $padding-sm;

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary-contrast;
        clip-path: polygon(
          0 2em,
          2em 2em,
          2em 0,
          calc(100% - 2em) 0,
          calc(100% - 2em) 2em,
          100% 2em,
          100% 100%,
          0 100%,
          0 2em,
          2px calc(2em + 2px),
          2px calc(100% - 2px),
          calc(100% - 2px) calc(100% - 2px),
          calc(100% - 2px) calc(2em + 2px),
          calc(100% - 2em - 2px) calc(2em + 2px),
          calc(100% - 2em - 2px) 2px,
          calc(2em + 2px) 2px,
          calc(2em + 2px) calc(2em + 2px),
          2px calc(2em + 2px)
        );
      }

      .cardContentWrapper {
        padding: $padding-md;
      }
    }
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  flex: 2.5;
  background: linear-gradient(to right, #00a2a9, #007a80, rgba(0, 0, 0, 0.7));
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);

  &:hover {
    background: linear-gradient(to right, #00b8c0, #008a90, rgba(0, 0, 0, 0.8));
    // transform: translateY(-2px);
  }

  &:active {
    // transform: translateY(1px);
  }
}

.content {
  text-align: center;
  color: white;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
}

.subtitle {
  font-size: 1rem;
  margin: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
}

.highlightedNumber {
  font-weight: bold;
  font-size: 2rem;
  color: #f7931a;
}

.comingSoonOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: inherit;

  span {
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
}

.container {
  position: relative;
  width: 100%;
  height: 100%;
  flex: 2.5;
  background: linear-gradient(to right, #00a2a9, #007a80, rgba(0, 0, 0, 0.7));
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);

  &:hover {
    background: linear-gradient(to right, #00b8c0, #008a90, rgba(0, 0, 0, 0.8));
    // transform: translateY(-2px);
  }

  &:active {
    // transform: translateY(1px);
  }
}

.content {
  text-align: center;
  color: white;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
}

.subtitle {
  font-size: 1rem;
  margin: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.7);
}

.highlightedNumber {
  font-weight: bold;
  font-size: 2rem;
  color: #f7931a;
}

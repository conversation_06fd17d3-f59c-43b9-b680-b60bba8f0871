// import { useSnapshot } from "valtio";
import styles from "./TradeModalContent.module.scss";
import React, { useEffect, useState } from "react";
import PurchasedMine from "@/components/MainPage/TradeModal/TradeModalMainContents/PurchasedMine/PurchasedMine"
import CurrentPrice from "./CurrentPrice";
import PriceHistory from "./PriceHistory";
import Listings from "./Listings";
import Offers from "./Offers";
import Activities from "./Activities";
const TradeModalContent = () => {
  return (
    <>
      <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>Mineral System</h1>
        </div>
      </div>
      <div className={styles.scrollArea}>
        {/* <InformationCard information={information} /> */}
        <PurchasedMine />
        <CurrentPrice />
        <PriceHistory />
        <Listings />
        <Offers />
        <Activities />
      </div>
    </>
  );
};

export default TradeModalContent;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
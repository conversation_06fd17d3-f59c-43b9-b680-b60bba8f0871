import { useLayoutEffect, useState } from "react";
import { useAccount, useBalance, useContractRead, useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";
import { usdtABI } from "@/constants/abis/UsdtABI";

const useUserBalance = () => {
  const { isConnected, address } = useAccount();
  const { data: nativeBalance } = useBalance({
    address,
    watch: true,
    enabled: isConnected && address !== undefined,
  });
  const { chain } = useNetwork();
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");
  const [usdtDecimals, setUsdtDecimals] = useState<number>(18);
  const [usdtBalance, setUsdtBalance] = useState(0n);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setUsdtAddress(networkConfigs[chain.id].usdtAddress);
    } else {
      setUsdtAddress("0x");
    }
  }, [chain]);

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "decimals",
    enabled: isConnected && usdtAddress !== "0x",
    onSuccess: (data) => {
      setUsdtDecimals(Number(data));
    },
    onError: () => {
      setUsdtDecimals(18);
    },
  });

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "balanceOf",
    args: [address ?? "0x"],
    enabled: isConnected && usdtAddress !== "0x",
    onSuccess: (data) => {
      setUsdtBalance(data);
    },
    onError: () => {
      setUsdtBalance(0n);
    },
  });
  return { nativeBalance, usdtBalance, usdtDecimals };
};

export default useUserBalance;

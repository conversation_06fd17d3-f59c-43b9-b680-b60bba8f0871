import React from "react";
import styles from "./UserTask.module.scss";
import { TUserTask } from "@/stores/pointsData";
import pointSystemIcon from "@/components/MainPage/PointSystem/assets/icons/asteroidX.png";
import titleIcon from "@/components/MainPage/PointSystem/assets/icons/titleIcon.png";

type Unpacked<T> = T extends (infer U)[] ? U : T;
const TaskCard = ({
  task,
  index,
}: {
  task: Unpacked<TUserTask>;
  index: number;
}) => (
  <div className={styles.taskCard}>
    {/* source */}
    <div className={styles.details}>
      {index === 0 ? <h3>Source</h3> : null}
      <p>{task.sourcePlatform}</p>
    </div>
    {/* description */}
    <div className={styles.details} style={{ flex: 2 }}>
      {index === 0 ? <h3>Description</h3> : null}
      <p>{task.taskName}</p>
    </div>
    {/* points */}
    <div className={styles.details}>
      {index === 0 ? <h3>Points</h3> : null}
      <p>{task.points}</p>
    </div>
  </div>
);

const UserTask = ({ userTasks }: { userTasks: TUserTask }) => {
  return (
    <div className={styles.container}>
      {/* user task */}
      <div className={styles.userTask}>
        <div className={styles.title}>
          <div>
            <img src={titleIcon.src} alt="title icon" />
          </div>
          <h1>COMPLETED TASKS</h1>
        </div>
        <div className={styles.taskCardContainer}>
          {userTasks.map((task, index) => (
            <TaskCard key={index} task={task} index={index} />
          ))}
        </div>
      </div>
      {/* Icon */}
      <div className={styles.icon}>
        <img src={pointSystemIcon.src} alt="point system icon" />
      </div>
    </div>
  );
};

export default UserTask;

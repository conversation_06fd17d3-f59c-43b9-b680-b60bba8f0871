import React from "react";
import { motion } from "framer-motion";
import { useSnapshot } from "valtio";
import styles from "./DownloadProgress.module.scss";
import { downloadModalStore } from "@/stores/downloadModal";

const DownloadProgress: React.FC = () => {
  const downloadModalSnapshot = useSnapshot(downloadModalStore);

  return (
    <div className={styles.progressContainer}>
      <div className={styles.progressHeader}>
        <h3>Downloading Files...</h3>
        <p>Your files are being downloaded individually</p>
      </div>

      <div className={styles.progressBar}>
        <motion.div
          className={styles.progressFill}
          initial={{ width: 0 }}
          animate={{ width: `${downloadModalSnapshot.downloadProgress}%` }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        />
      </div>

      <div className={styles.progressText}>
        {downloadModalSnapshot.downloadProgress}% Complete
      </div>

      <div className={styles.progressDetails}>
        <div className={styles.downloadingFiles}>
          <span>⬇️</span>
          <span>
            Downloading {downloadModalSnapshot.selectedFiles.size} files...
          </span>
        </div>
      </div>
    </div>
  );
};

export default DownloadProgress;

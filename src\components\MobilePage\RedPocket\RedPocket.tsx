import styles from "./RedPocket.module.scss";
import { delay, motion } from "framer-motion";
import { useState } from "react";
import redpacketIcon from "@/components/MainPage/RedPocket/assets/redpacketIcon.png";
import RedpacketClaimPage from "./RedpacketClaimPage/RedpacketClaimPage";
import { useAccount, useNetwork } from "wagmi";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";
import { ToastContainer, ToastContentProps, toast } from "react-toastify";
import { formatEther } from "viem";

const RedPocket = () => {
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const {
    participated,
    isClaimAvaliable,
    avaliableHskBalance,
    avaliableNftBalance,
  } = useRedpacket();

  const [isButtonClicked, setIsButtonClicked] = useState(false);

  const handleClick = () => {
    if (!isConnected) {
      toast.error("Please connect your wallet");
      return;
    }

    if (chain?.id && !redPocketDetails.supportedNetwork.includes(chain.id)) {
      toast.error("Please Switch to HSK Network");
      return;
    }

    if (!isClaimAvaliable) {
      toast.error("No more red packet available");
      return;
    }

    setIsButtonClicked(true);
  };

  return (
    <>
      <motion.div className={styles.container} style={{}} onClick={handleClick}>
        <motion.img
          src={redpacketIcon.src}
          alt="red packet icon"
          width={64}
          height={64}
          animate={{
            rotate: [0, -5, 5, -5, 0],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
        <div>
          <h3>Claim your red packet</h3>
          <h5>
            <span>{formatEther(avaliableHskBalance)}</span> HSK &{" "}
            <span>{avaliableNftBalance.toString()}</span> NFT Left
          </h5>
        </div>
      </motion.div>
      {isButtonClicked ? (
        <div className={styles.overlay}>
          <RedpacketClaimPage
            setIsButtonClicked={setIsButtonClicked}
            participated={participated}
          />
        </div>
      ) : null}
    </>
  );
};

export default RedPocket;

export const redPocketDetails = {
  supportedNetwork: [133, 177],
};

import { useSearchParams } from "next/navigation";
import styles from "./ContributeContent.module.scss";
import { useLayoutEffect, useState } from "react";
import { useAccount } from "wagmi";
import { getInvitationCodeFromOwnServer } from "@/actions/getInvitationCodeFromOwnServer";
import { showError } from "@/lib/notification";
import useJoinNow from "@/components/MainPage/PrivateSale/components/MainContent/CtaSection/JoinNowButton/hooks/useJoinNow";

type ContributeContentProps = {
  onClose: () => void;
  openRewardsModal: () => void;
};

const ContributeContent = ({
  onClose,
  openRewardsModal,
}: ContributeContentProps) => {
  const [amount, setAmount] = useState<string>("");
  const [shortCode, setShortCode] = useState("");
  const { contribute, isContributing, isWaitingForContribute } = useJoinNow(
    onClose,
    openRewardsModal,
  );
  const searchParams = useSearchParams();
  const referralCode = searchParams.get("invitation");
  const hasReferralCode = referralCode !== null && referralCode !== "";
  const { address } = useAccount();

  useLayoutEffect(() => {
    if (address) {
      getInvitationCodeFromOwnServer(address).then((shortCode) =>
        setShortCode(shortCode),
      );
    } else {
      setShortCode("");
      onClose();
    }
  }, [address]);

  const handleContribute = () => {
    if (!amount) {
      showError("Please enter a contribution amount");
      return;
    }

    if (!hasReferralCode) {
      contribute(amount, "0");
    } else if (shortCode === referralCode) {
      showError("You cannot join with your own referral code");
      return;
    } else {
      contribute(amount, referralCode ?? "");
    }
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (/^\d*\.?\d*$/.test(value)) {
      if (value === "" || parseFloat(value) >= 0.01) {
        setAmount(value);
      }
    }
  };

  const handleBlur = () => {
    if (amount && parseFloat(amount) < 0.01) {
      setAmount("0.01");
    }
  };

  return (
    <div className={styles.container}>
      <p className={styles.introText}>
        You're just one step away!{" "}
        <span className={styles.highlight}>
          Contribute HSK to receive DrillX at the best rate!{" "}
        </span>
      </p>

      <div className={styles.divider} />

      <div className={styles.rewardSection}>
        <div className={styles.sectionHeader}>Enter Contribution Amount</div>
        <p className={styles.sectionDescription}>
          For every contribution made by your invitees, you will receive{" "}
          <span className={styles.tokenText}>5%</span> of their HSK amount as a
          direct reward.
        </p>
        <div className={styles.inviteSection}>
          <input
            type="number"
            className={styles.inviteLink}
            value={amount}
            onChange={handleAmountChange}
            onBlur={handleBlur}
            min="0.01"
            step="0.01"
            inputMode="decimal"
            placeholder="0.1"
            disabled={isContributing || isWaitingForContribute}
          />
          <button
            className={styles.actionButton}
            onClick={handleContribute}
            disabled={isContributing || isWaitingForContribute}
          >
            {isContributing || isWaitingForContribute ? (
              <div className={styles.loadingState}>
                <span>Processing</span>
                <span className={styles.dots}>...</span>
              </div>
            ) : (
              "JOIN NOW"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContributeContent;

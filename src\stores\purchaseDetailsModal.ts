import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export const purchaseDetailsModal = proxy({
  isOpenPurchaseDetailsModal: false,
  setIsOpenPurchaseDetailsModal: (status: boolean) => {
    purchaseDetailsModal.isOpenPurchaseDetailsModal = status;
  },
  purchasePrice: 0n,
  setPurchasePrice: (price: bigint) => {
    purchaseDetailsModal.purchasePrice = price;
  },
  singlePrice: 0n,
  setSinglePrice: (price: bigint) => {
    purchaseDetailsModal.singlePrice = price;
  },
});

devtools(purchaseDetailsModal, {
  name: "purchaseDetailsModal",
  enabled: false,
});

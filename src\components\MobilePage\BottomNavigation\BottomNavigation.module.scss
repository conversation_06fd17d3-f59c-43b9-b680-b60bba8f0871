@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(30deg) rotateY(0deg);

.container {
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  // height: 10%;
  z-index: 100;
  @include row-center;

  .trapezoid {
    width: 90%;
    // height: 100%;
    @include row-center;
    position: relative;
    -webkit-perspective: 15rem;
    perspective: 15rem;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 120%;
      background: $color-black-transparent;
      backdrop-filter: blur(10px);
      border: $border-width-sm solid $color-primary;
      border-image: linear-gradient(
          to right,
          $color-primary-transparent,
          $color-primary,
          transparent
        )
        1 stretch;
      // border-image: linear-gradient(to center, $color-primary) 1 stretch;
      // border-image-slice: 1;
      // shape-outside: polygon(0 0, 100% 0, 85% 100%, 15% 100%);
      box-shadow:
        0 0 10px $color-primary,
        0 0 20px $color-primary-transparent;
      border-bottom: none;
      left: 0;
      top: 0;
      z-index: -1;
      -webkit-transform: $rotation-angle;
      transform: $rotation-angle;
    }
  }
}

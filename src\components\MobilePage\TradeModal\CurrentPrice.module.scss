@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$hightlightBackground: rgba($color-primary, 0.3);
@mixin offer-table-grid-columns {
  grid-template-columns:
    minmax(110px, 1fr) // Price per Token
    minmax(120px, 1fr) // Quantity
    minmax(70px, 1.2fr) // Selling Price
    minmax(100px, 1fr) // From
    minmax(120px, auto)
    minmax(120px, auto); // Action
}

@mixin listing-table-grid-columns {
  grid-template-columns: minmax(130px, 1fr) // Price per Token
    minmax(90px, 1fr) // Quantity
    minmax(100px, 1.2fr) // Selling Price
    minmax(70px, 1fr); // From
}

.container {
  width: 100%;
  // border: 1px solid $color-primary-contrast;
  .priceHeader {
    width: 100%;
    // color: rgba(white, 0.8);
    font-size: 1rem;
    margin: 2rem 0 1rem;
    padding: 1rem 0.5rem;
    // border-bottom: 1px solid rgba($color-primary, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba($color-primary, 0.2);
    position: relative;
    // animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(0, 224, 255, 0.5);
    border: 1px solid rgba(0, 224, 255, 0.8) !important;
    // transition: all 0.3s ease;
    letter-spacing: 1px;
    // background: $color-black-transparent-dark;
    color: $color-primary;
    border-radius: 0.5rem;
    overflow: hidden;
  }
  .mainHeader {
    width: 100%;
    // color: rgba(white, 0.8);
    font-size: 1rem;
    margin: 2rem 0 1rem;
    padding: 0.5rem;
    // border-bottom: 1px solid rgba($color-primary, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba($color-primary, 0.2);
    position: relative;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(0, 224, 255, 0.5);
    border: 1px solid rgba(0, 224, 255, 0.8) !important;
    transition: all 0.3s ease;
    letter-spacing: 1px;
    // background: $color-black-transparent-dark;
    color: $color-primary;
    border-radius: 0.5rem;
    overflow: hidden;

    &.expanded::after {
      content: "-";
      font-size: 1.5rem;
    }

    &.collapsed::after {
      content: "+";
      font-size: 1.5rem;
    }
  }
}

.detailsWrapper {
  width: 100%;
  @include row-between;
  margin: $margin-md 0;
}

.buttonWrapper {
  width: 100%;
  @include row-between;
  gap: 1rem;
}

.currentPrice {
  background: rgba($color-black-transparent-light, 0.05);
  border-radius: 8px;
  margin-bottom: 1rem;
  color: $color-primary;
}

.priceActions {
  padding: 0.5rem;
}

.buttons {
  width: 100%;
  @include row-between;
  gap: 1rem;
  // margin-bottom: 1rem;
  // border: 1px solid yellow;
  button {
    padding: 1rem 1rem;
    // border: none;
    border-radius: 4px;
    // font-weight: bold;
    cursor: pointer;
  }
}
.connectButton {
  justify-self: flex-end;
}

.sellButton {
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;
  margin-bottom: 1rem;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.buyButton {
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;
  @include row-center;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.priceInfo {
  width: 100%;
  //   margin-bottom: 1.5rem;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: $color-primary-contrast;
  font-size: 0.875rem;
  //   margin-bottom: 0.5rem;
}

.price {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;

  .amount {
    color: $color-primary;
    font-size: 2rem;
    font-weight: bold;
  }

  .usdPrice {
    color: $color-primary-contrast;
  }
}

.actions {
  width: 100%;
  @include col-between;
  align-items: flex-start;
  gap: 1rem;

  span {
    color: $color-primary-contrast;
  }
}

.maxPrice {
  @include row-between;
  gap: 0.5rem;

  input {
    width: 50%;
    background: $hightlightBackground;
    border: 1px solid rgba($color-primary, 0.3);
    color: $color-primary;
    padding: 0.5rem;
    border-radius: 4px;
  }
}

.buyNowButton {
  width: 100%;
  min-height: 3.5rem;
  background: $hightlightBackground;
  border: 1px solid rgba($color-primary, 0.3);
  color: $color-primary;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.makeOfferButton {
  width: 100%;
  min-height: 3.5rem;
  background: $color-black-transparent-dark;
  color: $color-primary;
  padding: 0.75rem 1.5rem;
  border: 1px solid $color-primary;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  span {
    color: $color-primary;
  }

  button {
    background: $color-black-transparent-dark;
    color: white;
    border: 1px solid rgba($color-primary, 0.3);
    width: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      color: $color-primary;
      background: $hightlightBackground;
    }
  }
}

.priceHistory {
  background: rgba($color-black-transparent-light, 0.05);
  border-radius: 8px;
  margin-bottom: 1rem;

  .graph {
    width: 100%;
    height: 300px;
    padding: 1rem 0;
    position: relative;

    // 优化移动端滚动体验
    :global(.price-chart-container) {
      // 隐藏滚动条但保持功能
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      // 移动端触摸滚动优化
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;

      // // 添加滚动提示阴影
      // &::before {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   right: 0;
      //   width: 20px;
      //   height: 100%;
      //   background: linear-gradient(to left, rgba(0, 196, 208, 0.1), transparent);
      //   pointer-events: none;
      //   z-index: 1;
      // }

      // &::after {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   left: 0;
      //   width: 20px;
      //   height: 100%;
      //   background: linear-gradient(to right, rgba(0, 196, 208, 0.1), transparent);
      //   pointer-events: none;
      //   z-index: 1;
      // }
    }
  }
}

.listings {
  background: rgba($color-black-transparent-light, 0.05);
  border-radius: 8px;
  margin-bottom: 2rem;
}

.listingsTable {
  // padding: 0 1rem 1rem;
  width: 100%;
  overflow-x: auto; // 关键：启用横向滚动
  scrollbar-width: thin; // Firefox滚动条瘦身
  scrollbar-color: $color-primary-transparent; // 滚动条颜色

  // 自定义webkit滚动条（覆盖90%移动端浏览器）
  &::-webkit-scrollbar {
    height: 6px; // 始终显示横向滚动条轨道
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    scrollbar-color: $color-primary-transparent; // 滚动条颜色
    border-radius: 3px;
  }

  .tableHeader,
  .listingTableHeader {
    display: grid;
    gap: 1rem;
    padding: 0.5rem 0;
    position: sticky;
    left: 0;
    border-bottom: 1px solid rgba($color-black-transparent-light, 0.1);
    color: rgba(white, 0.6);
    min-width: fit-content;
  }

  .tableHeader {
    @include offer-table-grid-columns;
  }

  .listingTableHeader {
    @include listing-table-grid-columns;
  }

  .tableBody {
    .tableRow {
      @include offer-table-grid-columns;
    }

    .listingTableRow {
      @include listing-table-grid-columns;
    }
    .tableRow,
    .listingTableRow {
      display: grid;
      gap: 1rem;
      padding: 1rem 0;
      border-bottom: 1px solid $color-primary-contrast;
      align-items: center;
      min-width: fit-content;

      // &:last-child {
      //   border-bottom: none;
      // }

      .price,
      .usdPrice,
      .quantity,
      .expiration,
      .from {
        color: $color-primary;
      }

      button.buyButton {
        padding: 0.5rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        width: 100%;
        font-weight: bold;
        border: 1px solid $color-primary;
        background: $color-black-transparent-dark;
        color: $color-primary;

        &:hover {
          background: $hightlightBackground;
        }

        &:disabled {
          background: $color-black-transparent-dark;
          color: $color-primary-contrast;
          border: 1px solid $color-primary-contrast;
          cursor: not-allowed;
        }
      }
    }
  }
}

.buyWrapper {
  display: flex;
  // gap: 8px;
  align-items: center;
}

.autoComplete {
  position: relative;
  width: 250px;
  height: 35px;
  padding: 0 8px;
  border-radius: 4px;
  border: 1px solid $color-primary;
  background-color: transparent;
  color: $color-primary;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: $margin-md;
  z-index: 1;
  &:focus {
    outline: none;
    border-color: $color-primary;
  }

  option {
    background-color: #fff;
    color: #000;
    z-index: 1;
  }
}
.loader {
  width: 16px;
  height: 16px;
  border: 1px solid #fff;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

.wrapper {
  width: 100%;
  height: 100%;
  background: $color-black-transparent-light;
  backdrop-filter: blur(10px);
  mask-image: linear-gradient(to bottom, black 85%, transparent);
  padding: $padding-lg;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    background: linear-gradient(
      to bottom,
      $color-primary,
      $color-black-transparent
    );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 2rem) 0,
      100% 2rem,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      0.3rem calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 0.2rem) calc(2rem + 0.83px),
      calc(100% - 2rem - 0.83px) 1px,
      1px 1px
    );
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 15px 0;
  // border-top: 1px solid #eee;
}

.pageButton {
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;
}

.pageButton:hover:not(:disabled) {
  background: $hightlightBackground;
}

.pageButton:disabled {
  background: $color-black-transparent-dark;
  color: $color-primary-contrast;
  border: 1px solid $color-primary-contrast;
  cursor: not-allowed;
}

.pageInfo {
  color: #666;
  font-size: 0.9em;
}
.pulseButton {
  position: relative;
  overflow: hidden;
  animation: pulse 2s infinite;
  box-shadow: 0 0 10px rgba(0, 224, 255, 0.5);
  border: 1px solid rgba(0, 224, 255, 0.8) !important;
  transition: all 0.3s ease;

  &:hover {
    animation: none;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.8);
  }

  &:disabled {
    animation: none;
    box-shadow: none;
  }
}
.sellButton {
  border: 1px solid $color-primary;
  background: $color-black-transparent-dark;
  color: $color-primary;

  &:hover {
    background: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

.barContainer {
  @include col-between;
  gap: 1rem;
}
.priceProgress {
  position: relative;
  width: 100%;
  height: 5px;
  background-color: #00ffff;
  border-radius: 10px;
  overflow: visible;
}

.progressBar {
  height: 100%;
  // background-color: #00ffff;
  border-radius: 3px;
  position: relative;
}

.priceMarker {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: white;
  border: 2px solid #00ffff;
  border-radius: 2px;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.details {
  @include row-between;
  width: 100%;
  color: #00ffff;
  font-size: 12px;
  // margin-top: 10px;

  span {
    opacity: 0.7;
  }

  .current {
    font-weight: bold;
    opacity: 1;
  }
}

.glowText {
  position: relative;
  z-index: 2;
  background: linear-gradient(90deg, #00e0ff, #ffffff, #00e0ff);
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shine 3s linear infinite;
}

.buttonHighlight {
  position: absolute;
  top: 0;
  left: -100%;
  // right: 100%;
  width: 100%;
  height: 100%;
  // border: 1px solid yellow;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 224, 255, 0.4),
    transparent
  );
  animation: sweep 3s infinite;
  z-index: 1;
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 5px rgba(0, 224, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 224, 255, 0.5);
  }
}

@keyframes shine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes sweep {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  20% {
    opacity: 1;
    transform: translateY(0);
  }
  80% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

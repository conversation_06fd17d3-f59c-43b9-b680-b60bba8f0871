@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  gap: 1rem;
  // padding: 2rem 0;
  @include row-center;

  @media screen and (max-width: 1023px) {
    @include col-center;
  }

  .description {
    width: 100%;
    gap: 1rem;
    .guide {
      color: yellow;
    }

    .rewardPool {
      font-size: $font-size-lg;
      font-weight: $font-weight-normal;
      color: $color-primary;
      text-transform: uppercase;
      font-family: "Garalama", sans-serif;
      letter-spacing: 2px;

      .rewardAmount {
        font-size: $font-size-lg;
        font-weight: $font-weight-light;
        color: $color-primary-transparent-contrast;
        margin-bottom: $margin-md;
      }
    }
    h3 {
      font-size: $font-size-sm;
      font-weight: $font-weight-extralight;
      color: $color-primary-transparent-contrast;
      font-family: $font-family-poppins;
    }
  }
}

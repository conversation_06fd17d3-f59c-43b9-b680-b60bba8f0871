import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export const scrollContainerStore = proxy({
  containerHeight: 0,
  setContainerHeight: (height: number) => {
    scrollContainerStore.containerHeight = height;
  },
  exploreContainerHeight: 0,
  setExploreContainerHeight: (height: number) => {
    scrollContainerStore.exploreContainerHeight = height;
  },
});

devtools(scrollContainerStore, {
  name: "scrollContainerStore",
  enabled: false,
});

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  flex: 4.5;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.9),
    rgba(0, 40, 50, 0.4)
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 2rem;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header {
  margin-bottom: 1.5rem;
  text-align: left;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 0;
  font-family: "Courier New", monospace; // Adjust to match the futuristic font in the image
}

.detailsTable {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.column {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.columnTitle {
  font-size: 1.25rem;
  font-weight: 800;

  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.columnValue {
  font-size: 1.25rem;
  margin: 0;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
}

.pagination {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 1rem;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);

  &[data-active="true"] {
    background-color: white;
  }
}

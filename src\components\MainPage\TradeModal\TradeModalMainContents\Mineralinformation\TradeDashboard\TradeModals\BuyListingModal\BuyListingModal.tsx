import { FC, useEffect, useState } from "react";
import { ErrorMonitorComponent } from "@/utils/errorMonitor";
import styles from "./BuyListingModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import TokenPrice from "@/components/common/TokenPrice";
import useCancelOffer from "../../hooks/useCancelOffer";
import useActiveListing from "../../hooks/useActiveListing";
import usePurchaseAllListingAmount from "../../hooks/usePurchaseAllListingAmount";
import useApprove from "../../hooks/useApprove";
import { useAccount, useNetwork } from "wagmi";
import useCancelListing from "../../hooks/useCancelListing";
import { networkConfigs } from "@/constants/networkConfigs";
import useApproveUsdt from "../../hooks/useApproveUsdt";

interface BuyListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  allActiveListingSortedByPriceFiltered: ReturnType<
    typeof useActiveListing
  >["allActiveListingSortedByPriceFiltered"];
}

const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const BuyListingModal: FC<BuyListingModalProps> = ({
  isOpen,
  onClose,
  allActiveListingSortedByPriceFiltered,
}) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { formatUnixTimestamp } = useActiveListing();
  const {
    isPurchasingListing,
    isWaitingForPurchaseListing,
    purchaseListing,
    setAmount,
    setListingId,
    setPricePerToken,
    listingId,
    value: usdtValue,
    refresh,
    setRefresh,
  } = usePurchaseAllListingAmount();
  const { address } = useAccount();
  const { isApprovedForAll } = useApprove();
  const {
    cancelListing,
    isCancelingListing,
    isWaitingForCancelListing,
    setSelectedListingId,
    selectedListingId,
  } = useCancelListing();
  const { chain } = useNetwork();
  const [isHandlingPurchasing, setIsHandlingPurchasing] = useState(false);
  const {
    isApprovingUsdt,
    isWaitingForApproveUsdt,
    usdtAllowance,
    handleApproveUsdt,
  } = useApproveUsdt({
    usdtValue,
    purchaseListing,
    handleDecimals: false,
    refresh,
  });

  useEffect(() => {
    setIsHandlingPurchasing(
      isPurchasingListing ||
        isWaitingForPurchaseListing ||
        isApprovingUsdt ||
        isWaitingForApproveUsdt,
    );
  }, [
    isPurchasingListing,
    isWaitingForPurchaseListing,
    isApprovingUsdt,
    isWaitingForApproveUsdt,
  ]);

  // 安装错误监控器，只在开发环境下启用
  // useEffect(() => {
  //   // 在开发环境或测试环境中启用错误监视器
  //   if (process.env.NODE_ENV !== "production") {
  //     console.log("[BuyListingModal] 安装错误监控器");
  //     const cleanup = ErrorMonitorComponent();
  //     return () => {
  //       // 清理错误监控器
  //       if (cleanup) cleanup();
  //     };
  //   }
  // }, []);

  useEffect(() => {
    const hasChanged =
      isHandlingPurchasing && usdtAllowance >= usdtValue && usdtValue > 0;

    if (hasChanged) {
      setRefresh((prev) => prev + 1);
    }
  }, [isHandlingPurchasing, usdtAllowance, usdtValue, setRefresh]);

  const handleBuyAllAmount = () => {
    if (usdtAllowance < usdtValue) {
      handleApproveUsdt();
    } else {
      purchaseListing();
    }
  };

  const handlePurchaseListing = (listing: any) => {
    // Set listing data before purchase
    setAmount(listing.amount);
    setPricePerToken(listing.pricePerToken);
    setListingId(listing.listingId);
    // Execute purchase
    handleBuyAllAmount();
  };

  const handleMouseOver = (index: number, isOwner: boolean) => {
    if (allActiveListingSortedByPriceFiltered) {
      if (isOwner) {
        setSelectedListingId(
          allActiveListingSortedByPriceFiltered[index].listingId,
        );
      } else {
        setAmount(allActiveListingSortedByPriceFiltered[index].amount);
        setPricePerToken(
          allActiveListingSortedByPriceFiltered[index].pricePerToken,
        );
        setListingId(allActiveListingSortedByPriceFiltered[index].listingId);
      }
    }
  };

  const handleCancelListing = () => {
    cancelListing();
  };

  if (!isOpen) return null;
  if (allActiveListingSortedByPriceFiltered?.length === 0) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalFrame}>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>

        <div className={styles.contentWrapper}>
          <h2 className={styles.header}>BUY LISTING</h2>
          <div className={styles.scrollArea}>
            <div className={styles.contentFrame}>
              <div className={styles.derlordInfo}>
                <h3>{selectedMine.name}</h3>
              </div>
              <div className={styles.listingsTable}>
                <div className={styles.tableHeader}>
                  <div>Address</div>
                  <div>Price per Token</div>
                  <div>Quantity</div>
                  <div>Selling Price</div>
                  <div>Expiration</div>
                  <div>Action</div>
                </div>
                <div className={styles.tableBodyContainer}>
                  <div className={styles.tableBody}>
                    {allActiveListingSortedByPriceFiltered &&
                    allActiveListingSortedByPriceFiltered.length > 0 ? (
                      allActiveListingSortedByPriceFiltered.map(
                        (listing, index) => (
                          <div
                            className={`${styles.tableRow} ${
                              listing.seller === address
                                ? styles.ownListing
                                : ""
                            }`}
                            key={index}
                          >
                            <div>
                              <a
                                href={
                                  chain &&
                                  networkConfigs[chain.id].etherscanAddress +
                                    listing.seller
                                }
                                target="_blank"
                                style={{ color: "#4895ef" }}
                              >
                                {trimWalletAddress(listing.seller)}
                              </a>
                            </div>
                            <div className={styles.price}>
                              <TokenPrice
                                amount={listing.pricePerToken}
                                tokenAddress={listing.paymentToken}
                                showCurrency={true}
                                currencySymbol={selectedMine.currency}
                              />
                            </div>
                            <div className={styles.quantity}>
                              {listing.amount.toString()}
                            </div>
                            <div className={styles.price}>
                              <TokenPrice
                                amount={listing.pricePerToken * listing.amount}
                                tokenAddress={listing.paymentToken}
                                showCurrency={true}
                                currencySymbol={selectedMine.currency}
                              />
                            </div>
                            <div className={styles.expiration}>
                              {formatUnixTimestamp(
                                Number(listing.expirationTime),
                              )}
                            </div>
                            <div>
                              <button
                                className={`${styles.buyButton} ${
                                  listing.seller === address
                                    ? styles.cancelButton
                                    : ""
                                } ${
                                  isHandlingPurchasing ||
                                  isCancelingListing ||
                                  isWaitingForCancelListing
                                    ? styles.disabled
                                    : ""
                                }`}
                                onClick={() =>
                                  listing.seller === address
                                    ? handleCancelListing()
                                    : handlePurchaseListing(listing)
                                }
                                onMouseEnter={() =>
                                  handleMouseOver(
                                    index,
                                    listing.seller === address,
                                  )
                                }
                                disabled={
                                  isHandlingPurchasing ||
                                  isCancelingListing ||
                                  isWaitingForCancelListing ||
                                  !isApprovedForAll
                                }
                                style={{
                                  opacity:
                                    isHandlingPurchasing ||
                                    isCancelingListing ||
                                    isWaitingForCancelListing ||
                                    !isApprovedForAll
                                      ? 0.5
                                      : 1,
                                  pointerEvents:
                                    isHandlingPurchasing ||
                                    isCancelingListing ||
                                    isWaitingForCancelListing ||
                                    !isApprovedForAll
                                      ? "none"
                                      : "auto",
                                }}
                              >
                                {/* Show Processing... when the specific action is happening on this listing */}
                                {(isHandlingPurchasing &&
                                  listingId === listing.listingId &&
                                  listing.seller !== address) ||
                                ((isCancelingListing ||
                                  isWaitingForCancelListing) &&
                                  listing.listingId === selectedListingId &&
                                  listing.seller === address)
                                  ? "Processing..."
                                  : listing.seller === address
                                  ? "CANCEL LISTING"
                                  : "BUY"}
                              </button>
                            </div>
                          </div>
                        ),
                      )
                    ) : (
                      <div className={styles.noListings}>
                        No active listings available
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyListingModal;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.tableWrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.tableContainer {
  width: 100%;
  overflow-y: auto;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;

  th,
  td {
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  th {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: black;
    font-weight: 600;
  }

  tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}

.currentUser {
  background-color: rgba(255, 215, 0, 0.1);
  font-weight: 600;
}

.youLabel {
  color: #ffd700;
  font-weight: bold;
  margin-left: 4px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.pageButton {
  padding: 8px 16px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pageInfo {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

import styles from "./MineNavBar.module.scss";
import React, { useState } from "react";
import MinePreviewBody from "./MinePreviewCard/MinePreviewBody";
import PurchaseContent from "../Purchase/PurchaseContent/PurchaseContent";
import { plusModalStore } from "@/stores/plusModal";
import { useSnapshot } from "valtio";
import { minesDetails } from "@/constants/mineDetails";
// import SpecialEventMineCard from "./SpecialEventMineCard/MineCard";
import { mineCardStore } from "@/stores/mineCard";
import MineBody from "./SpecialEventMineCard/MineBody/MineBody";

type onSelectType = (selectedComponent: string) => void;

interface NavbarProps {
  onSelect: onSelectType;
  selected: string;
}

const NavBar: React.FC<NavbarProps> = ({ onSelect, selected }) => {
  const plusModalSnapshot = useSnapshot(plusModalStore);


  const buttons = [
    { label: "Overview", component: "component1" },
    { label: "Purchase", component: "component2" },
    {
      label: "PlusModel",
      component: "component3",
      action: () => plusModalSnapshot.setIsOpenPlusModal(true),
    },
  ];

  return (
    <div className={styles.navbar}>
      {buttons.map(({ label, component, action }, index) => (
        <React.Fragment key={component}>
          <div
            onClick={() => {
              onSelect(component);
              if (action) action();
            }}
            className={`${styles.navButton} ${selected === component ? styles.active : ""
              }`}
          >
            {label}
          </div>
          {index !== buttons.length - 1 && (
            <span className={styles.divider}>/</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

const ParentComponent: React.FC = () => {
  // 设置默认选中的组件为 "component1"
  const [selectedComponent, setSelectedComponent] =
    useState<string>("component1");
    const mineCardSnapshot = useSnapshot(mineCardStore);

    // First find the selected mine details
    const selectedMineDetails = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine
    )[0];
    
    // Check if it's a special event
    const isSpecialEvent = selectedMineDetails?.shortName === "hskreward";
    
    return (
      <div className={styles.wrapper}>
        <NavBar onSelect={setSelectedComponent} selected={selectedComponent} />
        {selectedComponent === "component1" && (
          isSpecialEvent ? (
            <MineBody setSelectedComponent={setSelectedComponent} />
          ) : (
            <MinePreviewBody />
          )
        )}
        {selectedComponent === "component2" && <PurchaseContent />}
      </div>
    );
};

export default ParentComponent;

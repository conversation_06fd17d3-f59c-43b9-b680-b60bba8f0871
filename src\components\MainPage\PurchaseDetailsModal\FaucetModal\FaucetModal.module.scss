@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin actionButton() {
  width: 100px;
  padding: $padding-xs;
  border: $border-width-2xs solid $color-primary;
  font-size: $font-size-sm;
  font-family: $font-family-saira;
  background: $color-black-transparent;
  color: $color-primary-contrast;
  cursor: pointer;

  &:hover {
    background: $color-primary-contrast;
    color: $color-primary;
  }
}

.container {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: $z-index-2;
  @include row-center;
  background: $color-black-transparent-medium;

  .faucetModal {
    width: 600px;
    // height: 600px;
    border: $border-width-xs solid $color-primary;
    background: $color-black-transparent-dark;
    padding: $padding-md;
    // border-radius: 1rem;

    .title {
      color: $color-primary;
      text-align: center;
    }

    .supportedNetwork {
      text-align: center;
      text-transform: uppercase;
      color: $color-primary-contrast;
    }

    .walletAddressWrapper {
      padding: $padding-md;
      width: 100%;
      @include row-center;
      .walletAddress {
        width: 90%;
        text-align: center;
        padding: $padding-sm;
        background: $color-black-transparent;
        border: $border-width-xs solid $color-primary-contrast;
        color: $color-primary;
        font-size: $font-size-lg;
        font-weight: $font-weight-extrabold;
      }
    }

    .actionButtonWrapper {
      @include row-center;
      gap: $spacing-md;

      .cancelButton {
        @include actionButton();
        color: $color-danger;
        border: $border-width-2xs solid $color-danger;

        &:hover {
          background: $color-danger;
        }
      }
      .confirmButton {
        @include actionButton();
      }
    }
  }
}

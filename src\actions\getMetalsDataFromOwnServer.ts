"use server";
import * as z from "zod";

export const getMetalsDataFromOwnServer = async () => {
  const url = `https://asteroidx.io/api/metals/list?pageNum=1&pageSize=10`;
  const response = await fetch(url, { cache: "no-cache" });

  if (response.ok) {
    const data = (await response.json()) as TMetalsResponse;
    return {
      ...data.data,
    };
  }

  return null;
};

// types
const MetalSchema = z.object({
  currentPrice: z.number(),
  change: z.number(),
  changePercent: z.number(),
  timestamp: z.coerce.date(),
  dateItems: z.array(z.string()),
  priceItems: z.array(z.number()),
});

const DataSchema = z.object({
  silver: MetalSchema,
  zinc: MetalSchema,
  palladium: MetalSchema,
  platinum: MetalSchema,
  nickel: MetalSchema,
  copper: MetalSchema,
  lead: MetalSchema,
  gold: MetalSchema,
  aluminum: MetalSchema,
  rhodium: MetalSchema,
});

const TMetalsResponseSchema = z.object({
  code: z.number(),
  info: z.string(),
  data: DataSchema,
});
export type TMetalsResponse = z.infer<typeof TMetalsResponseSchema>;

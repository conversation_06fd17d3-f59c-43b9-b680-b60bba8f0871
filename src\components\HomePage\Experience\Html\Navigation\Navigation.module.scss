@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.navigation {
  width: 100%;
  height: 40px;
  position: absolute;
  // border: 1px solid yellow;
  bottom: 0;
  left: 0;
  background: $color-black-transparent-dark;
  // transform: translate(-50%);
  padding: $padding-xl;
  // z-index: 100;
  @include row-center;

  img {
    width: 40px;
    height: 40px;
    padding: $padding-xs;
    // background: $color-primary;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      // background: $color-primary-contrast;
      transform: scale(1.5);
    }
  }
}

.installPrompt {
  position: absolute;
  bottom: 160px;
  left: 50%;
  width: 90%;
  transform: translate(-50%);
  padding: 1rem;
  background-color: $color-black-transparent-dark;
  border: 1px solid green;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;

  .icon {
    width: 24px;
    height: 24px;
    object-fit: cover;
  }

  .instruction {
    width: 100%;
    text-align: center;
  }

  &::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%);
    border-width: 15px;
    border-style: solid;
    border-color: green transparent transparent transparent;
  }
}

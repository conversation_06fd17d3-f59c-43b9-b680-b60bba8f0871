import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { mineCardStore } from "@/stores/mineCard";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError } from "viem";
import { minesDetails } from "@/constants/mineDetails";
import { extractErrorType } from "@/utils/errorHandling";

type Offer = {
  offerId: bigint;
  buyer: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
  pricePerToken: bigint;
  expirationTime: bigint;
  active: boolean;
  timestamp: bigint;
  paymentToken: `0x${string}`;
};

type PaginatedOffersResponse = [Offer[], bigint];

const useCancelOffer = (onClose?: () => void) => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [limit, setLimit] = useState(50n);
  const [userOffersForToken, setUserOffersForToken] = useState<Offer[]>([]);
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0); // Token ID
  const [selectedOfferId, setSelectedOfferId] = useState(0n);
  const [isCancelingOffer, setIsCancelingOffer] = useState(false);
  const [totalUserOffersForToken, setTotalUserOffersForToken] = useState(0n);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const { getSelectedMineId } = useMineTokenId();
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const formatUnixTimestamp = (timestamp: number): string => {
    if (!timestamp) return "";
    const date = new Date(timestamp * 1000);
    return date.toLocaleString("en-AU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketplaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine, getSelectedMineId]);

  const { data: userTokenOffersData, refetch: refetchUserTokenOffers } =
    useContractRead({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "getActiveOffersByBuyerAndTokenPaginated",
      args: [address ?? "0x", BigInt(asteroidMineNumber), 0n, limit],
      enabled:
        isConnected &&
        marketplaceAddress !== "0x" &&
        address !== undefined &&
        asteroidMineNumber !== 0,
      watch: true,
      onSuccess: (data: PaginatedOffersResponse) => {
        if (data) {
          // Data is already filtered by buyer, tokenId, and for active/non-expired status by the contract.
          // Sort if needed
          const sortedOffers = [...data[0]].sort((a: Offer, b: Offer) =>
            Number(b.pricePerToken - a.pricePerToken),
          );
          setUserOffersForToken(sortedOffers);
          setTotalUserOffersForToken(data[1]);
        } else {
          setUserOffersForToken([]);
          setTotalUserOffersForToken(0n);
        }
      },
    }) as { data?: PaginatedOffersResponse; refetch: () => void };

  const { config: cancelOfferConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "cancelOffer",
      args: [selectedOfferId],
      enabled:
        isConnected && marketplaceAddress !== "0x" && selectedOfferId !== 0n,
    });

  const { write: cancelOfferWrite, data: cancelOfferData } = useContractWrite({
    ...cancelOfferConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsCancelingOffer(false);
    },
  });

  const { isLoading: isWaitingForCancelOffer } = useWaitForTransaction({
    confirmations: 5,
    hash: cancelOfferData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Cancelled!");
      setIsCancelingOffer(false);
      refetchUserTokenOffers();
      onClose?.();
    },
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsCancelingOffer(false);
    },
  });

  const cancelOffer = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (!cancelOfferWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      }
      return;
    }

    setIsCancelingOffer(true);
    try {
      cancelOfferWrite?.();
    } catch (error) {
      setIsCancelingOffer(false);
      showError(extractErrorType(error) || "Failed to cancel offer");
    }
  };

  return {
    allActiveOfferSortedByPrice: userOffersForToken,
    formatUnixTimestamp,
    cancelOffer,
    isCancelingOffer,
    isWaitingForCancelOffer,
    setSelectedOfferId,
    selectedOfferId,
    totalUserOffers: totalUserOffersForToken,
    setLimit,
  };
};

export default useCancelOffer;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  @include col-center;
  gap: 1rem;

  .summaryContainer {
    width: 100%;
    border: 1px solid $color-primary;
    border-radius: 2rem;
    background-color: black;
    padding: 1rem;
    @include col-center;
    justify-content: flex-start;
    z-index: 2;

    .redpacketClaimed {
      @include row-center;
      gap: 1rem;
      font-size: $font-size-lg;
      font-weight: 600;
      color: white;
      text-align: center;

      .claimDetails {
        span {
          font-size: $font-size-lg;
          font-weight: 800;
          color: $color-warning;
        }
      }
    }

    .divider {
      width: 100%;
      height: 1px;
      background-color: grey;
      margin: 1rem 0;
    }
  }

  .closeButton {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
    background-color: #ff4444;
    color: white;
    backdrop-filter: blur(4px);

    &:hover {
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }

    &:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      transform: none;
      opacity: 0.7;

      &:hover {
        transform: none;
      }

      &:active {
        transform: none;
      }
    }
  }
}

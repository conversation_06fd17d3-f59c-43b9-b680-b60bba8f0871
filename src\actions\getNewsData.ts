"use server";
import z from "zod";

export const getNewsData = async ({
  keywords = "mining",
  countries = "au",
  categories = "business",
  limit = 10,
}) => {
  let newsData = [] as NewsData[];
  const accessKey = ""; //"process.env.NEWS_API_KEY;"
  const url = `http://api.mediastack.com/v1/news?access_key=${accessKey}&keywords=${keywords}&countries=${countries}&categories=${categories}&limit=${limit}`;
  const response = await fetch(url, { cache: "no-cache" });

  // if (!response.ok) {
  //   throw new Error("Failed to fetch news data");
  // }

  const data = await response.json();

  if (!data.error) {
    newsData = (data as NewsDataResponse).data.map((news) => {
      return { headline: news.title, publishedAt: news.published_at };
    });
  }

  return newsData;
};

//types

interface NewsData {
  headline: string;
  publishedAt: string;
}

const PaginationSchema = z.object({
  limit: z.number(),
  offset: z.number(),
  count: z.number(),
  total: z.number(),
});

const DatumSchema = z.object({
  author: z.union([z.null(), z.string()]),
  title: z.string(),
  description: z.string(),
  url: z.string(),
  source: z.string(),
  image: z.union([z.null(), z.string()]),
  category: z.string(),
  language: z.string(),
  country: z.string(),
  published_at: z.string(),
});

const NewsDataResponseSchema = z.object({
  pagination: PaginationSchema,
  data: z.array(DatumSchema),
});
type NewsDataResponse = z.infer<typeof NewsDataResponseSchema>;

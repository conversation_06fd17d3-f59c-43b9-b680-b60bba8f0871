import CountUp from "react-countup";
import styles from "./HeroText.module.scss";

const HeroText = () => {
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <h3 className={styles.joinText}>Join Now to</h3>
        <h1 className={styles.mainTitle}>Be Among the First to Own $DrillX</h1>
        <p className={styles.deadline}>UNTIL DECEMBER 3, 2025</p>

        <div className={styles.tokenInfo}>
          <div className={styles.tokenEquation}>
            <span className={styles.tokenValue}>1 HSK</span>
            <span className={styles.tokenSymbol}>≈</span>
            <div className={styles.priceGroup}>
              <span className={styles.priceToken}>
                <CountUp end={3.33} duration={3} decimals={2} />
              </span>
              <span className={styles.priceUnit}>DrillX</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroText;

import {
  getHskPriceFromOwnServer,
  THskPriceResponse,
} from "@/actions/getHskPriceFromOwnServer";
import React, { useLayoutEffect, useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const HskPriceChart = () => {
  const [data, setData] = useState<THskPriceResponse["data"]["data"]>([]);

  useLayoutEffect(() => {
    getHskPriceFromOwnServer()
      .then((res) => {
        setData(res);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  return (
    <>
      <h3>Hsk Trends Price</h3>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart width={500} height={300} data={data}>
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey="price" stroke="#00c4d0" dot={false} />
        </LineChart>
      </ResponsiveContainer>
    </>
  );
};

export default HskPriceChart;

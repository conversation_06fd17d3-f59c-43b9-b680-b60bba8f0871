import { useTexture } from "@react-three/drei";
import { useThree } from "@react-three/fiber";
import { memo } from "react";

const GalaxyScene = () => {
  const { scene } = useThree();
  const galaxy = useTexture("/textures/milkyV2.jpeg");
  //   scene.background = galaxy;
  return (
    <>
      <mesh>
        <sphereGeometry attach="geometry" args={[250, 32, 32]} />
        <meshBasicMaterial attach="material" map={galaxy} side={2} />
      </mesh>
    </>
  );
};

export default memo(GalaxyScene);

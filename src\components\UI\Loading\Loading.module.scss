@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.container {
  width: $displayWidth;
  height: $displayHeight;
  @include row-center;

  // @media screen and (min-width: 1280px) {
  //   width: $displayWidth * $zoom-level-1280-offset;
  //   height: $displayHeight * $zoom-level-1280-offset;
  // }

  // @media screen and (min-width: 1920px) {
  //   width: $displayWidth * $zoom-level-1920-offset;
  //   height: $displayHeight * $zoom-level-1920-offset;
  // }
}

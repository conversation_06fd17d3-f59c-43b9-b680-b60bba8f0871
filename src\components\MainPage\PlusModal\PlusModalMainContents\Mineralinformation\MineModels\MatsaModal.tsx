import { Center, OrbitControls, Html } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";
import { Suspense, memo } from "react";
import { useModelLoader } from "@/hooks/useModelLoader";

const MatsaModal = () => {
  const Loader = () => <Html center>loading modal</Html>;

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Canvas
        shadows
        camera={{ position: [0, 0, 320], fov: 25 }}
        gl={{ preserveDrawingBuffer: true }}
      >
        <ambientLight intensity={1} color="white" />
        <directionalLight color="white" position={[0, 0, -20]} intensity={1} />
        <directionalLight color="white" position={[0, 0, 20]} intensity={1} />
        <directionalLight color="white" position={[20, 0, 0]} intensity={1} />
        <directionalLight color="white" position={[-20, 0, 0]} intensity={1} />
        <directionalLight color="white" position={[0, 20, 0]} intensity={1} />
        <directionalLight color="white" position={[0, -20, 0]} intensity={1} />
        <Center>
          <Suspense fallback={<Loader />}>
            <Matsa />
          </Suspense>
        </Center>
        <OrbitControls
          autoRotate
          autoRotateSpeed={0.5}
          makeDefault
          enableZoom={false}
          minPolarAngle={Math.PI / 2.2}
          maxPolarAngle={Math.PI / 2.2}
          // minAzimuthAngle={-Math.PI / 2.1}
          // maxAzimuthAngle={Math.PI / 2.3}
        />
      </Canvas>
    </div>
  );
};

function Matsa(props: JSX.IntrinsicElements["group"]) {
  const { model } = useModelLoader("MATSA");
  const { nodes, materials } = model;
  return (
    <group {...props} dispose={null}>
      <group
        position={[-1516.741, 8587.764, 6820.786]}
        rotation={[-1.573, -0.001, 3.14]}
        scale={0.005}
      >
        <group rotation={[Math.PI / 2, 0, 0]}>
          <group rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh
              castShadow
              receiveShadow
              geometry={nodes.Model001_Material001_0.geometry}
              material={materials["Material.001"]}
            />
            <mesh
              castShadow
              receiveShadow
              geometry={nodes.Model001_Material001_0_1.geometry}
              material={materials["Material.001"]}
            />
          </group>
        </group>
      </group>
    </group>
  );
}

export default memo(MatsaModal);

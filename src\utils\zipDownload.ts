/**
 * Utility functions for handling file downloads and ZIP creation
 * This provides a lightweight solution without external dependencies
 */

export interface DownloadableFile {
  name: string;
  url: string;
}

/**
 * Download a single file
 */
export const downloadSingleFile = (file: DownloadableFile): void => {
  const link = document.createElement("a");
  link.href = file.url;
  link.download = file.name;
  link.target = "_blank";
  link.rel = "noopener noreferrer";

  // Append to body, click, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Download multiple files individually with a delay
 * This is the most reliable cross-browser solution
 */
export const downloadMultipleFiles = async (
  files: DownloadableFile[],
  onProgress?: (progress: number) => void,
  delay: number = 500,
): Promise<void> => {
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const progress = ((i + 1) / files.length) * 100;

    // Download the file
    downloadSingleFile(file);

    // Update progress
    if (onProgress) {
      onProgress(progress);
    }

    // Add delay between downloads to prevent browser blocking
    if (i < files.length - 1) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
};

/**
 * Create a simple ZIP-like download experience
 * Downloads files individually but provides user feedback
 */
export const downloadAsArchive = async (
  files: DownloadableFile[],
  archiveName: string,
  onProgress?: (progress: number) => void,
): Promise<void> => {
  if (files.length === 0) {
    throw new Error("No files to download");
  }

  if (files.length === 1) {
    // Single file - direct download
    downloadSingleFile(files[0]);
    if (onProgress) onProgress(100);
    return;
  }

  // Multiple files - download individually
  await downloadMultipleFiles(files, onProgress);
};

/**
 * Validate if a file URL is accessible
 */
export const validateFileUrl = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: "HEAD" });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Get file extension from URL
 */
export const getFileExtension = (url: string): string => {
  const pathname = new URL(url, window.location.origin).pathname;
  const extension = pathname.split(".").pop()?.toLowerCase() || "";
  return extension;
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Create a download notification
 * This integrates with the existing toast system
 * Note: This function should be called from components that have access to the notification system
 */
export const showDownloadNotification = (
  message: string,
  type: "info" | "success" | "error" = "info",
): void => {
  // Log for debugging
  console.log(`[Download ${type.toUpperCase()}] ${message}`);

  // This is a placeholder - the actual notification will be handled in the component
  // that calls this function, using the proper showSuccess/showError imports
};

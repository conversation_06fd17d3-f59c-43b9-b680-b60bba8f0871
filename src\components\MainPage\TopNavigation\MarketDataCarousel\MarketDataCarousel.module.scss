@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 400px;
  // padding: $padding-xs;
  .wrapper {
    @include row-between;
    border-left: $border-width-xs solid #474d57;
    font-size: $font-size-sm;
    padding: 0 $padding-sm;
    font-weight: $font-weight-bold;

    .title {
      color: $color-warning;
    }
    .priceChangeUp {
      color: $color-primary;
    }
    .priceChangeDown {
      color: $color-danger;
    }
    .price {
      color: white;
    }
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #0a1a1f;
  border: 1px solid #00b8d4;
  border-radius: 4px;
  width: 100%;
  max-width: 1200px;
  color: white;
  overflow: hidden;
}

.modalHeader {
  background-color: #00b8d4;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 32px;
    font-weight: 500;
    text-transform: uppercase;
  }
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 32px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.modalBody {
  padding: 20px;
}

.introText {
  font-size: 24px;
  margin-bottom: 20px;
  color: #ccc;
}

.highlight {
  color: #00b8d4;
}

.rewardSection {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
}

.sectionHeader {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #00b8d4;
  font-size: 32px;
  font-weight: bold;
}

.sectionNumber {
  margin-right: 8px;
  font-family: monospace;
}

.sectionDescription {
  font-size: 24px;
  margin-bottom: 15px;
  color: #ccc;
}

.tokenText {
  color: #00b8d4;
  font-weight: 500;
}

.inviteSection {
  @include row-between;

  .inviteLink {
    background-color: rgba(0, 184, 212, 0.1);
    border: 1px solid rgba(0, 184, 212, 0.3);
    border-radius: 4px;
    padding: 10px 15px;
    color: #00b8d4;
    font-family: monospace;
    font-size: 24px;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }

  .actionButton {
    font-size: 24px;
    background-color: transparent;
    border: 1px solid #00b8d4;
    color: #00b8d4;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    // align-self: flex-end;

    &:hover {
      background-color: rgba(0, 184, 212, 0.1);
    }
  }
}

.contributionsTable {
  margin-top: 20px;
  width: 100%;

  .tableHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      font-family: inherit;
    }

    .totalInfo {
      font-size: 16px;
      font-weight: 600;
      font-family: inherit;
      color: #00b8d4;
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 16px;
    font-weight: normal;
    font-family: inherit;
    letter-spacing: 0.25px;

    th,
    td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    th {
      font-weight: 600;
      color: #fff;
      background-color: rgba(255, 255, 255, 0.05);
    }

    .amountColumn {
      color: #00b8d4;
    }

    td {
      font-weight: normal;
    }

    .amountValue {
      color: #00b8d4;
    }

    tr:hover {
      background-color: rgba(255, 255, 255, 0.03);
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  gap: 15px;

  .paginationButton {
    padding: 6px 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
      background-color: rgba(255, 255, 255, 0.2);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .pageInfo {
    font-size: 14px;
  }
}

import { getMarketData } from "@/actions/getMarketData";
import { proxy } from "valtio";
import { devtools } from "valtio/utils";
type MarketDataStore = Awaited<ReturnType<typeof getMarketData>>;

export const marketDataStore = proxy({
  marketData: [] as MarketDataStore,
  setMarketData: (newMarketData: MarketDataStore) => {
    marketDataStore.marketData = newMarketData;
  },
});

devtools(marketDataStore, {
  name: "marketDataStore",
  enabled: false,
});

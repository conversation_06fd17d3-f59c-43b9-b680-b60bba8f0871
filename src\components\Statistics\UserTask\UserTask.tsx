import React from "react";
import styles from "./UserTask.module.scss";
import { TUserTask } from "@/stores/pointsData";

const UserTask = ({ userTasks }: { userTasks: TUserTask }) => {
  return (
    <div className={styles.container}>
      <h2 className={styles.title}>Completed Tasks</h2>
      <table className={styles.completedTasks}>
        <thead>
          <tr>
            <th>Source</th>
            <th>Description</th>
            <th>Points</th>
          </tr>
        </thead>
        <tbody>
          {userTasks.map((task, index) => (
            <tr key={index}>
              <td>{task.sourcePlatform}</td>
              <td>{task.taskName}</td>
              <td>{task.points}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default UserTask;

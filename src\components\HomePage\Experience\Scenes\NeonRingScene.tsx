import * as THREE from "three";
import React, { memo, useRef } from "react";
import { useAnimations } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";
import { useModelLoader } from "@/hooks/useModelLoader";

type NeonRingGLTF = {
  nodes: {
    Object_4: THREE.Mesh;
    Object_6: THREE.Mesh;
  };
  materials: {
    ["Material.001"]: THREE.MeshStandardMaterial;
    ["Material.002"]: THREE.MeshStandardMaterial;
  };
  animations: THREE.AnimationClip[];
};

const NeonRingScene = (props: JSX.IntrinsicElements["group"]) => {
  const group = useRef<THREE.Group>(null);

  const { model } = useModelLoader<NeonRingGLTF>("NEON_RING");
  const { nodes, materials, animations } = model;

  const { actions } = useAnimations(animations, group);
  useFrame((state, delta) => {
    // materials["Material.002"].color.set(new THREE.Color("#00C4D0"));
    // materials["Material.002"].opacity = 0.1;
    actions["Torus.002Action"]?.setDuration(0.5).play();
  });

  return (
    <group
      ref={group}
      {...props}
      dispose={null}
      scale={[260, 260, 260]}
      rotation={[0, -Math.PI / 2, 0]}
    >
      <group name="Sketchfab_Scene">
        <group name="Sketchfab_model" rotation={[-Math.PI / 2, 0, 0]}>
          <group name="root">
            <group name="GLTF_SceneRootNode" rotation={[Math.PI / 2, 0, 0]}>
              <group name="Torus001_2" rotation={[0, 0, -Math.PI / 2]}>
                <mesh
                  name="Object_4"
                  castShadow
                  receiveShadow
                  geometry={nodes.Object_4.geometry}
                  material={materials["Material.001"]}
                />
              </group>
              <group
                name="Torus002_3"
                rotation={[0, 0, -Math.PI / 2]}
                scale={0.97}
              >
                <mesh
                  name="Object_6"
                  castShadow
                  receiveShadow
                  geometry={nodes.Object_6.geometry}
                  material={materials["Material.002"]}
                />
              </group>
            </group>
          </group>
        </group>
      </group>
    </group>
  );
};

export default memo(NeonRingScene);

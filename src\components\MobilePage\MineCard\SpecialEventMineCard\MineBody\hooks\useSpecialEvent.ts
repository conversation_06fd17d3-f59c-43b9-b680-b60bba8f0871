import { rewardAsteroidXAbi } from "@/constants/abis/RewardAsteroidXABI";
import { hashkey, hashkeyTestnet } from "@/constants/customChains";
import { networkConfigs } from "@/constants/networkConfigs";
import React, { useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";

const useSpecialEvent = () => {
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const [rewardAddress, setRewardAddress] = useState<`0x${string}`>("0x");
  const [totalParticipants, setTotalParticipants] = useState(0n);
  const [totalReward, setTotalReward] = useState(0n);
  const [participateTime, setParticipateTime] = useState(0n);
  const [endTime, setEndTime] = useState(0n);
  const [claimableReward, setClaimableReward] = useState(0n);
  const [claimStartTime, setClaimStartTime] = useState(0n);
  const [purchaseTime, setPurchaseTime] = useState(0n);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setRewardAddress(networkConfigs[chain.id].rewardAddress);
    } else {
      setRewardAddress("0x");
    }
  }, [chain]);

  const { data: currentPeriodId } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "currentPeriodId",
    enabled:
      isConnected &&
      address !== "0x" &&
      !chain?.unsupported &&
      rewardAddress !== "0x",
    watch: true,
  });

  useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "periodInfo",
    args: [currentPeriodId ?? BigInt(0)],
    enabled:
      rewardAddress !== "0x" &&
      currentPeriodId !== undefined &&
      isConnected &&
      address !== "0x" &&
      !chain?.unsupported,
    watch: true,
    onSuccess: (data) => {
      setTotalParticipants(data.participantsCount);
      setTotalReward(data.totalReward);
      setParticipateTime(data.startTime);
      setEndTime(data.endTime);
      setClaimStartTime(data.claimStartTime);
    },
  });

  useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "userRewardInfo",
    args: [address ?? "0x"],
    enabled:
      rewardAddress !== "0x" &&
      isConnected &&
      address !== "0x" &&
      !chain?.unsupported,
    watch: true,
    onSuccess: (data) => {
      setClaimableReward(data.claimableReward);
      setPurchaseTime(data.timestamp);
    },
  });

  return {
    totalParticipants,
    totalReward,
    participateTime,
    claimableReward,
    endTime,
    claimStartTime,
    purchaseTime,
  };
};

export default useSpecialEvent;

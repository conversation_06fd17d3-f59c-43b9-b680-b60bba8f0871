import AliceCarousel from "react-alice-carousel";
import "react-alice-carousel/lib/alice-carousel.css";
import styles from "./MarketDataCarousel.module.scss";
import { useLayoutEffect, useState } from "react";

export interface ICryptoData {
  symbol: string;
  lastPrice: string;
  priceChangePercent: string;
}

const MarketDataCarousel = () => {
  const [cryptoData, setCryptoData] = useState<ICryptoData[]>([]);

  useLayoutEffect(() => {
    const eventSource = new EventSource(
      "https://asteroidx.io/api/crypto/stream",
    );

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setCryptoData(data);
    };

    eventSource.onerror = (error) => {
      console.error("EventSource failed:", error);
    };

    return () => {
      eventSource.close();
    };
  }, []);

  // const marketDataSnapshot = useSnapshot(marketDataStore);
  const carouselItems = cryptoData.map((item) => {
    const priceChange = Number(item.priceChangePercent);
    return (
      <div
        key={(item.symbol + "usd").toUpperCase()}
        className={styles.container}
      >
        <div className={styles.wrapper}>
          <div className={styles.title}>{item.symbol.toUpperCase()}</div>
          <div
            className={
              priceChange < 0 ? styles.priceChangeDown : styles.priceChangeUp
            }
          >
            {priceChange < 0
              ? priceChange.toFixed(2)
              : `+${priceChange.toFixed(2)}`}
          </div>
          <div className={styles.price}>
            ${Number(Number(item.lastPrice).toFixed(2))}
          </div>
        </div>
      </div>
    );
  });

  return (
    <div style={{ width: "100%" }}>
      <AliceCarousel
        items={carouselItems}
        autoWidth
        autoHeight
        autoPlay
        autoPlayStrategy="none"
        autoPlayInterval={1000}
        // animationDuration={10000}
        animationType="fadeout"
        infinite
        touchTracking={false}
        disableDotsControls
        disableButtonsControls
      />
    </div>
  );
};

export default MarketDataCarousel;

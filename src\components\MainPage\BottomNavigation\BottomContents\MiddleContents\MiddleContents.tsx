import { mineCardStore } from "@/stores/mineCard";
import styles from "./MiddleContents.module.scss";
import { minesDetails } from "@/constants/mineDetails";
import { useSnapshot } from "valtio";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";

const MiddleContents = () => {
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [currencyUnit, setCurrencyUnit] = useState("");
  const { chain } = useNetwork();
  const { isConnected } = useAccount();

  const mineCardSnapshot = useSnapshot(mineCardStore);
  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const { data: minePrice } = useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "tokenMinAmounts",
    args: [BigInt(asteroidMineNumber)],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      asteroidMineNumber !== 0,
  });

  const getSelectedMineAddress = (chainId: number) => {
    return networkConfigs[chainId].asteroidAddress;
  };

  const getSelectedMineId = (chainId: number) => {
    const selectedMine = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0].name;

    switch (selectedMine) {
      case minesDetails[0].name:
        return networkConfigs[chainId].assetIds.mt;
      case minesDetails[1].name:
        return networkConfigs[chainId].assetIds.matsa;
      case minesDetails[2].name:
        return networkConfigs[chainId].assetIds.zephyr;
      default:
        break;
    }
  };

  useEffect(() => {
    if (chain?.unsupported) {
      setLaunchPadAddress("0x");
      setAsteroidAddress("0x");
    } else {
      if (chain) {
        setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
        setAsteroidAddress(getSelectedMineAddress(chain.id) ?? "0x");
        setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
        setCurrencyUnit(networkConfigs[chain.id].currencyUnit);
      }
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Purchase Number</h1>
        <h2 className={styles.subtitle}>Buy A Mine</h2>
      </div>
      <div className={styles.mineDetails}>
        <h1 className={styles.mineRegion}>
          Mine: <span className={styles.mineName}>{information.mineZone}</span>
        </h1>
        {asteroidMineNumber && isConnected ? (
          <h1 className={styles.mineNumber}>ID #{asteroidMineNumber}</h1>
        ) : null}
      </div>
      <div className={styles.mineDetails}>
        <h1 className={styles.mineMineral}>
          Mineral:{" "}
          <span className={styles.mineral}>{information.mineMineral}</span>
        </h1>
        {minePrice !== undefined && isConnected ? (
          <h1 className={styles.price}>
            {Number(minePrice)}{" "}
            <span style={{ fontSize: "14px" }}>{currencyUnit}</span>
          </h1>
        ) : null}
      </div>
    </div>
  );
};

export default MiddleContents;

// colors
$color-primary: rgba(0, 196, 208, 1);
$color-primary-contrast: rgba(0, 101, 107, 1);
$color-primary-transparent: rgba(0, 196, 208, 0.1);
$color-primary-transparent-contrast: rgba(0, 196, 208, 0.6);

$color-black-transparent: rgba(0, 0, 0, 0.5);
$color-black-transparent-light: rgba(0, 0, 0, 0.1);
$color-black-transparent-medium: rgba(0, 0, 0, 0.75);
$color-black-transparent-dark: rgba(0, 0, 0, 0.95);

$color-danger: #f6465d;
$color-warning: rgb(208, 208, 13);

// font families
$font-family-poppins: var(--font-poppins);
$font-family-saira: var(--font-saira);
$font-family-maven-pro: var(--font-maven-pro);
$font-family-orbitron: var(--font-orbitron);

// zoom level
$zoom-level-1280: 0.6;
$zoom-level-1280-offset: 1 / $zoom-level-1280;
$zoom-level-1920: 1;
$zoom-level-1920-offset: 1 / $zoom-level-1920;

// font sizes
$font-size-2xs: 0.625rem;
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-md: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-2xl: 1.5rem;
$font-size-3xl: 1.875rem;
$font-size-4xl: 2rem;
$font-size-5xl: 3rem;
$font-size-6xl: 4rem;

// font weights
$font-weight-thin: 100;
$font-weight-extralight: 200;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

// paddings
$padding-xs: 0.25rem;
$padding-sm: 0.5rem;
$padding-md: 1rem;
$padding-lg: 1.5rem;
$padding-xl: 2rem;
$padding-2xl: 2.5rem;
$padding-3xl: 3rem;
$padding-4xl: 3.5rem;
$padding-5xl: 4rem;

// margins
$margin-sm: 0.5rem;
$margin-md: 1rem;
$margin-lg: 1.5rem;

// border widths
$border-width-2xs: 0.03125rem;
$border-width-xs: 0.0625rem;
$border-width-sm: 0.125rem;
$border-width-md: 0.25rem;
$border-width-lg: 0.5rem;

// spacings
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-2xl: 2.5rem;
$spacing-3xl: 3rem;

// z indexes
$z-index-1: 1;
$z-index-2: 2;

// border radiuss
$border-radius-xs: 0.125rem;
$border-radius-sm: 0.25rem;
$border-radius-md: 0.5rem;
$border-radius-lg: 1rem;

// export for javascript use
:export {
  colorPrimary: $color-primary;
  colorPrimaryContrast: $color-primary-contrast;
  colorPrimaryTransparent: $color-primary-transparent;
  colorPrimaryTransparentContrast: $color-primary-transparent-contrast;
  colorBlackTransparent: $color-black-transparent;
  colorBlackTransparentLight: $color-black-transparent-light;
  colorBlackTransparentMedium: $color-black-transparent-medium;
  colorDanger: $color-danger;
}

// gradients
// $gradient-primary: linear-gradient(
//   270deg,
//   $color-primary-light 0%,
//   transparent 100%
// );

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100%;
$displayHeight: 100%;

@mixin detailsWrapper {
  width: 100%;
  margin-bottom: $margin-lg;

  h1 {
    color: $color-primary;
    text-decoration: underline;
  }
  p {
    color: $color-primary-contrast;
    font-size: $font-size-3xl;
  }
}

.canvasScreen {
  width: $displayWidth;
  height: $displayHeight;

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
    // zoom: $zoom-level-1280;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
    // zoom: $zoom-level-1920;
  }
}

.loadingText {
  width: 100%;
  height: 100%;
  @include col-center;
  font-size: $font-size-5xl;
  color: $color-primary;
}

.container {
  width: 100%;
  height: 100%;
  @include col-between;
  align-items: flex-start;

  .scrollArea {
    width: 100%;
    height: 888px;
    overflow: scroll;
    // border: 1px solid green;

    &::-webkit-scrollbar {
      display: none;
    }

    .companyDetails {
      width: 100%;
      height: 100%;
    }

    .companyDetailsComingSoon {
      width: 100%;
      height: 100%;
      @include col-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .companyDetailsWrapper {
      @include detailsWrapper;

      img {
        width: 100%;
        // height: 100%;
        object-fit: cover;
      }
    }

    .investorZoneComingSoon {
      width: 100%;
      height: 100%;
      @include row-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .investorZone {
      width: 100%;
      height: 100%;
    }

    .investorZoneWrapper {
      @include detailsWrapper;
    }

    .imageFrame {
      width: 100%;
      height: 100%;
      position: relative;
      background: $color-black-transparent;
      clip-path: polygon(
        0 0,
        calc(100% - 80px) 0,
        100% 80px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px)
      );

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        clip-path: polygon(
          0 0,
          calc(100% - 81px) 0,
          100% 81px,
          100% calc(100% - 48px),
          calc(100% - 48px) 100%,
          48px 100%,
          0 calc(100% - 48px),
          0 0,
          1px 1px,
          1px calc(100% - 48px - 0.41px),
          calc(48px + 0.41px) calc(100% - 1px),
          calc(100% - 48px - 0.41px) calc(100% - 1px),
          calc(100% - 1px) calc(100% - 48px - 0.41px),
          calc(100% - 1px) calc(48px + 0.41px),
          calc(100% - 48px - 0.41px) 1px,
          1px 1px
        );
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .pointSystemScrollArea {
        width: 100%;
        height: 100%;
        overflow: scroll;
        position: absolute;
        scroll-behavior: smooth;

        &::-webkit-scrollbar {
          display: none;
        }

        .detailsContainer {
          width: 90%;
          padding: 2rem 2rem 0;
          min-height: calc(100% - 180px);
        }
      }
    }
  }

  .mainTitle {
    width: 100%;
    // height: 180px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
    position: absolute;
    bottom: 70px;

    .rewardSection {
      width: 50%;
      min-height: 120px;
      background: rgba(0, 0, 0, 0.4);
      border-top: 1px solid rgba(0, 255, 255, 0.1);
      border-bottom: 1px solid rgba(0, 255, 255, 0.1);
      padding: 1rem 2rem;
      display: flex;
      align-items: center;
      // gap: 40px;
      margin-left: 30px;

      .rewardInfo {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        flex: 1;

        .rewardRow {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .label {
            color: $color-primary-transparent-contrast;
            font-size: $font-size-xs;
            white-space: nowrap;
          }

          .value {
            color: $color-primary;
            font-size: $font-size-sm;
            font-weight: $font-weight-semibold;
          }
        }
      }

      .claimButton {
        width: 160px;
        text-align: center;
        padding: 12px;
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        color: #454545;
        border: none;
        cursor: pointer;
        text-transform: uppercase;
        font-family: $font-family-saira;
        position: relative;
        margin-right: 0;
        flex-shrink: 0;

        background: radial-gradient(
          circle at 100%,
          #b2a8fd,
          #8678f9 50%,
          #c7d2fe 75%,
          #9a8dfd 75%
        );
        background-size: 200% auto;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: animatedTextGradient 1.5s linear infinite;

        &:disabled {
          cursor: not-allowed;
        }

        // Button background layers
        &::before {
          content: "";
          position: absolute;
          top: -0.5px;
          left: -0.5px;
          right: -0.5px;
          bottom: -0.5px;
          background: linear-gradient(90deg, #000000 0%, $color-primary 100%);
          border-radius: 6px;
          z-index: -2;
          opacity: 1;
        }

        &::after {
          content: "";
          position: absolute;
          top: 0.5px;
          left: 0.5px;
          right: 0.5px;
          bottom: 0.5px;
          background: #000;
          border-radius: 6px;
          z-index: -1;
        }

        &:not(:disabled):hover::before {
          box-shadow: 0 0 15px $color-primary-transparent-contrast;
        }

        // &:hover {
        //   transform: scale(1.05);
        // }
      }
    }

    .topTitle {
      align-self: center;
      @include row-center;

      .mineral {
        background: rgba(255, 255, 255, 0.07);
        padding: 0 $padding-xs;
        border-radius: $border-radius-sm;
        font-size: $font-size-md;
        color: #454545;
        font-weight: $font-weight-medium;
      }

      img {
        margin-left: $spacing-sm;
      }

      .location {
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        color: #454545;
      }
    }

    .bottomTitle {
      // margin-top: 1rem;
      font-size: $font-size-6xl;
      font-weight: $font-weight-light;
      color: #454545;
      background: transparent;
      border: none;
      font-family: $font-family-saira;
      padding: 0 2rem;
    }

    .bottomTitleActive {
      background: radial-gradient(
        circle at 100%,
        #b2a8fd,
        #8678f9 50%,
        #c7d2fe 75%,
        #9a8dfd 75%
      );
      font-size: $font-size-6xl;
      font-weight: $font-weight-light;
      color: #454545;
      border: none;
      background-size: 200% auto;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: animatedTextGradient 1.5s linear infinite;
      cursor: pointer;

      &:hover {
        transform: scale(1.05);
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }
}

@keyframes animatedTextGradient {
  to {
    background-position: 200% center;
  }
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  // @include row-between;
  @include row-center;
  margin-bottom: $margin-md;
  position: relative;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-family: poppins;
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      letter-spacing: 0.1em;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 90%;
  height: 90%;
  overflow: scroll;
  padding-right: $padding-md;
  padding-bottom: $padding-lg;
  gap: 2rem;

  &::-webkit-scrollbar {
    display: none;
  }

  
}

.container {
  padding: 24px;
  background: #1A1A1A;
  border-radius: 12px;
  max-width: 500px;
  color: white;
  display: grid;
  gap: 24px;
}

/* Mineral Info Section */
.mineralSection {
  border-bottom: 1px solid #333;
  padding-bottom: 24px;
}

.mainTitle {
  font-size: 18px;
  font-weight: 600;
  color: #9CA3AF;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.mineralHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.subTitle {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.mineralId {
  color: #6B7280;
  font-size: 14px;
  margin: 4px 0 0;
}

.ethValue {
  font-size: 24px;
  font-weight: 700;
  color: #10B981;
}

.location {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #9CA3AF;
  font-size: 14px;
  margin-bottom: 24px;
}

.divider {
  color: #4B5563;
}

/* Details Grid */
.detailsGrid {
  display: grid;
  gap: 16px;
}

.detailItem {
  background: #252525;
  padding: 16px;
  border-radius: 8px;
}

.detailLabel {
  color: #9CA3AF;
  font-size: 12px;
  margin-bottom: 6px;
  text-transform: uppercase;
}

.detailValue {
  font-size: 14px;
  line-height: 1.4;
}

/* Price Section */
.priceSection {
  background: #2D2D2D;
  border-radius: 8px;
  padding: 20px;
}

.priceHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
}

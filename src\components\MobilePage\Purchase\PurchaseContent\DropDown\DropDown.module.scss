@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.SelectTrigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 13px;
  line-height: 1;
  height: 35px;
  gap: 5px;
  background-color: $color-black-transparent-light;
  color: $color-primary;
  // box-shadow: 0 2px 10px $color-primary;
}
.SelectTrigger:hover {
  // background-color: var(--mauve-3);
  background-color: $color-black-transparent-medium;
}
.SelectTrigger:focus {
  box-shadow: 0 0 0 2px black;
  background-color: $color-black-transparent-light;
}
.SelectTrigger[data-placeholder] {
  color: $color-primary;
}

.SelectIcon {
  color: $color-primary;
}

.SelectContent {
  font-size: 13px;
  color: $color-primary;
  z-index: 10;
  overflow: hidden;
  background-color: white;
  border-radius: 6px;
  border-color: 2px solid $color-primary-contrast;
  box-shadow:
    0px 10px 38px -10px rgba(22, 23, 24, 0.35),
    0px 10px 20px -15px rgba(22, 23, 24, 0.2);
}

.SelectViewport {
  padding: 5px;
  // background-color: yellow;
  z-index: 10;
}

.SelectItem {
  cursor: pointer;
  // background-color: yellow;
  line-height: 1;
  border-radius: 3px;
  display: flex;
  align-items: center;
  height: 25px;
  z-index: 10;
  padding: 0 35px 0 25px;
  position: relative;
  user-select: none;
}
.SelectItem[data-highlighted] {
  outline: none;
  background-color: $color-primary;
  color: $color-primary;
}

.SelectItemIndicator {
  position: absolute;
  left: 0;
  width: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

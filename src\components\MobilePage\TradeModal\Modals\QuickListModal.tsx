import { FC, useEffect, useState } from "react";
import styles from "./QuickListModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import useCreateListing from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useCreateListing";
import useMaxAmount from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useMaxAmount";
import { formatEther } from "viem";
import { ToastContainer, ToastContentProps, toast } from "react-toastify";

interface MakeOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const QuickListModal: FC<MakeOfferModalProps> = ({ isOpen, onClose }) => {
  const [duration, setDuration] = useState("1 MONTH");
  const [expirationDate, setExpirationDate] = useState("");
  const [inputValue, setInputValue] = useState("");

  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const {
    avaliableNfts,
    setAmount,
    setExpirationDateUnixTime,
    setNumberOfItem,
    numberOfItem,
    amount,
    totalPaymentValue,
    createListing,
    isCreatingListing,
    isWaitingForCreateListing,
  } = useCreateListing(onClose);
  const { maxAmountPerListing, maxPricePerToken } = useMaxAmount();

  useEffect(() => {
    const calculateExpirationDate = () => {
      const now = new Date();
      let futureDate = new Date(now);

      if (duration === "1 MONTH") {
        futureDate.setMonth(now.getMonth() + 1);
      } else if (duration === "3 MONTHS") {
        futureDate.setMonth(now.getMonth() + 3);
      } else if (duration === "6 MONTHS") {
        futureDate.setMonth(now.getMonth() + 6);
      }

      const formattedDate = futureDate.toISOString().slice(0, 16);
      setExpirationDate(formattedDate);
      setExpirationDateUnixTime(getUnixTimestamp(formattedDate));
    };

    calculateExpirationDate();
  }, [duration]);

  const getUnixTimestamp = (isoDateString: string): number => {
    if (!isoDateString) return 0;
    return Math.floor(new Date(isoDateString).getTime() / 1000);
  };

  const handleListing = () => {
    // Check if number of items is valid
    if (numberOfItem <= 0) {
      toast.error("Please enter a valid number of items");
      return;
    }
    // Check if number of items exceeds available NFTs
    if (numberOfItem > Number(avaliableNfts)) {
      toast.error("Number of items cannot exceed available NFTs");
      return;
    }
    if (numberOfItem > maxAmountPerListing) {
      toast.error(`You can only list ${maxAmountPerListing} items at a time`);
      return;
    }

    // Check if amount is valid
    if (amount <= 0) {
      toast.error("Please enter a valid price amount");
      return;
    }
    if (amount > Number(formatEther(maxPricePerToken))) {
      toast.error(
        `You can only list ${formatEther(maxPricePerToken)} ${
          selectedMine.currency
        } per token`,
      );
      return;
    }
    // If all checks pass, proceed with listing
    try {
      createListing();
    } catch (error) {
      console.error("Error creating listing:", error);
      toast.error("Error creating listing");
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.wrapper}>
      <div className={styles.headerWrapper}>
        <h3 className={styles.header}>QUICK LIST</h3>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>
      </div>

      <div className={styles.contentWrapper}>
        <div className={styles.scrollArea}>
          {/* <div className={styles.contentFrame}> */}
          {/* <div className={styles.derlordInfo}>
              <h3>{selectedMine.name}</h3>
            </div> */}
          <div className={styles.itemCount}>
            <h3># OF ITEMS</h3>
            <div className={styles.inputContainer}>
              {/* <input
                type="number"
                value={numberOfItem}
                min={0}
                max={Number(avaliableNfts)}
                onChange={(e) => {
                  const value = Math.floor(Number(e.target.value));
                  setNumberOfItem(value >= 0 ? value : 0);
                }}
                onBlur={(e) => {
                  const value = Math.floor(Number(e.target.value));
                  setNumberOfItem(value >= 0 ? value : 0);
                }}
              /> */}
              <input
                type="number"
                placeholder="#Items"
                min={1}
                max={Number(avaliableNfts)}
                step={1}
                value={numberOfItem === 0 ? "" : numberOfItem}
                onChange={(e) => {
                  if (e.target.value === "") {
                    setNumberOfItem(0);
                  } else {
                    // 获取输入值并转换为整数
                    let value = parseInt(e.target.value, 10);

                    // 确保是有效的数字（非NaN）
                    if (isNaN(value)) value = 0;

                    // 限制在1到200之间
                    if (value < 0) value = 0;
                    if (value > avaliableNfts) value = Number(avaliableNfts);

                    setNumberOfItem(value);
                  }
                }}
                // 防止用户输入小数
                onKeyDown={(e) => {
                  // 阻止小数点、逗号、负号、加号
                  if (
                    e.key === "." ||
                    e.key === "," ||
                    e.key === "-" ||
                    e.key === "+" ||
                    e.key === "e"
                  ) {
                    e.preventDefault();
                  }
                }}
              />
              <span className={styles.available}>
                {avaliableNfts.toString()} AVAILABLE
              </span>
            </div>
          </div>

          <div className={styles.priceSection}>
            <h3>SET A PRICE PER ITEM</h3>
            {/* <h4>STARTING PRICE</h4> */}
            <div className={styles.priceInput}>
              {/* <input
                type="number"
                placeholder="AMOUNT"
                min={0.01}
                step={0.01}
                value={amount}
                onChange={(e) => setAmount(Number(e.target.value))}
              /> */}
              <input
                type="text" // 使用text而不是number以获得更好的控制
                placeholder="Price per token"
                value={inputValue}
                onChange={(e) => {
                  const newValue = e.target.value;
                  // 仅允许数字和小数点
                  if (
                    newValue === "" ||
                    newValue === "0" ||
                    /^[1-9][0-9]*\.?[0-9]{0,2}$/.test(newValue) ||
                    /^0\.[0-9]{0,2}$/.test(newValue)
                  ) {
                    setInputValue(newValue);

                    // 更新实际数值
                    if (newValue === "" || newValue === ".") {
                      setAmount(0);
                    } else {
                      const numValue = parseFloat(newValue);
                      setAmount(numValue);
                    }
                  }
                }}
              />
              <span className={styles.eth}>{selectedMine.currency}</span>
            </div>
          </div>

          <div className={styles.durationSection}>
            <h3>DURATION</h3>
            <div className={styles.durationControls}>
              <select
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
              >
                <option value="1 MONTH">1 MONTH</option>
                <option value="3 MONTHS">3 MONTHS</option>
                <option value="6 MONTHS">6 MONTHS</option>
              </select>
              <input type="datetime-local" value={expirationDate} readOnly />
            </div>
          </div>

          <div className={styles.summary}>
            <div className={styles.summaryRow}>
              <span>TOTAL PRICE</span>
              <span>
                {totalPaymentValue} {selectedMine.currency}
              </span>
            </div>
            {/* <div className={styles.summaryRow}>
              <span>OPENSEA FEE</span>
              <span>2.5%</span>
            </div>
            <div className={styles.summaryRow}>
              <span>CREATOR EARNINGS</span>
              <span>0%</span>
            </div>
            <div className={styles.summaryRow} style={{ color: "#00C4D0" }}>
              <span>TOTAL POTENTIAL EARNINGS</span>
              <span>-- ETH</span>
            </div> */}
          </div>

          <button
            className={styles.completeButton}
            onClick={handleListing}
            disabled={isCreatingListing || isWaitingForCreateListing}
          >
            {isCreatingListing || isWaitingForCreateListing ? (
              <div className={styles.loadingIndicator}>
                <span className={styles.loader}></span>
                Processing...
              </div>
            ) : (
              "COMPLETE LISTING"
            )}
          </button>
          <p
            style={{
              fontSize: "0.8rem",
              color: "yellow",
              textAlign: "center",
              marginBottom: "2rem",
            }}
          >
            0.4% trading fee for every transaction
          </p>
        </div>
      </div>
      {/* <ToastContainer
        style={{ fontSize: "0.875rem" }}
        position="top-left"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        theme="dark"
      /> */}
    </div>
  );
};

export default QuickListModal;

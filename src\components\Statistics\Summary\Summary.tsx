import React, { useEffect, useState } from "react";
import styles from "./Summary.module.scss";
import Link from "next/link";
import { pointSystemData } from "@/constants/pointSystem/final";
import { useAccount } from "wagmi";
import { PointsData } from "@/stores/pointsData";
import { showError, showSuccess } from "@/lib/notification";

interface SummaryProps {
  pointsData: PointsData;
}

const Summary: React.FC<SummaryProps> = ({ pointsData }) => {
  const { address } = useAccount();
  const [userPoints, setUserPoints] = useState(0);
  const [totalCounts, setTotalCounts] = useState(0);
  const [rank, setRank] = useState("");
  const [percent, setPercent] = useState(0);

  useEffect(() => {
    setUserPoints(pointsData.userPoints);
    setTotalCounts(pointsData.total);
    setRank(pointsData.rank);
    setPercent(pointsData.percent);
  }, [address, pointsData, totalCounts]);

  return (
    <div className={styles.container}>
      <div className={styles.summarySection}>
        <div className={styles.pointSection}>
          <div className={styles.pointTitle}>
            <h2>Current Total Participants</h2>
            {/* <Link href="#">Points History</Link> */}
          </div>
          <h1 className={styles.priceTag}>
            {totalCounts}
            <span> users</span>
          </h1>
        </div>
        <div className={styles.divider} />
        <div className={styles.pointSection}>
          <div className={styles.pointTitle}>
            <h2>Your AsteroidX Points</h2>
            {/* <Link href="#">Points History</Link> */}
          </div>
          <h1 className={styles.priceTag}>
            {userPoints}
            <span> points</span>
          </h1>
        </div>
        <div className={styles.divider} />
        <div className={styles.pointSection}>
          <div className={styles.pointTitle}>
            <h2>Your Current Rank</h2>
            {/* <Link href="#">Points History</Link> */}
          </div>
          <h1 className={styles.priceTag}>
            {rank}
            <span></span>
          </h1>
        </div>
        <div className={styles.divider} />
        <div className={styles.tokenSection}>
          <div className={styles.pointTitle}>
            <h2>Your Estimated Rewards</h2>
          </div>
          <h1 className={styles.priceTag}>
            {percent > 0 ? `${percent}%` : "< 0.01%"}
            {/* <span> $ASTEROID</span> */}
          </h1>
        </div>
      </div>
    </div>
  );
};

export default Summary;

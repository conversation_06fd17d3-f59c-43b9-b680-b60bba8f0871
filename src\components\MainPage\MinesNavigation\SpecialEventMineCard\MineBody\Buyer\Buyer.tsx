import styles from "./Buyer.module.scss";
import { MineCardProps } from "../../MineCard";
import { minesDetails } from "@/constants/mineDetails";
import { useState } from "react";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { useAccount, useNetwork, useSwitchNetwork } from "wagmi";
import { BaseError } from "viem";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { showError, showSuccess } from "@/lib/notification";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import useSpecialEvent from "../hooks/useSpecialEvent";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";

type ContractList = {
  address: `0x${string}`;
  abi: typeof asteroidAddressABI;
  functionName: string;
  args: BigInt[];
};

const formatTimestamp = (timestamp: bigint | undefined): string => {
  if (!timestamp) return "N/A";
  const date = new Date(Number(timestamp) * 1000);
  return date
    .toLocaleString("en-AU", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(",", "");
};

const Buyer = ({ information }: Pick<MineCardProps, "information">) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  // const {
  //   handleClaim,
  //   isClaiming,
  //   isWaitingForClaim,
  //   userRewardInfo,
  //   userReward,
  //   claimStartTime,
  // } = useSpecialEvent();
  const { handleClaim, isClaiming, participated, isWaitingForClaim } =
    useRedpacket();
  const { chain } = useNetwork();
  const { switchNetwork, chains } = useSwitchNetwork({
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      showSuccess("Switched to supported network");
    },
  });
  const { isConnected } = useAccount();
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const supportedChain = chains.filter((x) =>
    information.supportedNetwork.includes(x.id),
  )[0]?.name;

  return (
    <div className={styles.container}>
      <div className={styles.loginButtonWrapper}>
        {isConnected ? (
          <motion.button
            className={styles.loginButton}
            whileTap={buttonEffect.tap}
            disabled={isClaiming || isWaitingForClaim || participated}
            // disabled={BigInt(Math.floor(Date.now() / 1000)) >= claimStartTime}
            onClick={() => {
              // showConfetti();
              // purchaseNft();
              if (chain && chain.unsupported) {
                showError("Please Switch Network");
                return;
              }
              if (chain && selectedMine.supportedNetwork.includes(chain?.id)) {
                // purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                //   true,
                // );
                handleClaim();
              } else {
                switchNetwork?.(
                  chain?.testnet
                    ? selectedMine.supportedNetwork[0]
                    : selectedMine.supportedNetwork[1],
                );
              }
            }}
          >
            {chain && selectedMine.supportedNetwork.includes(chain?.id)
              ? "Claim Now"
              : `Switch to ${supportedChain}`}
          </motion.button>
        ) : (
          <motion.div
            className={styles.loginButton}
            whileTap={buttonEffect.tap}
            onClick={() => {
              connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
            }}
          >
            Log in <span>&nbsp;or&nbsp;</span> Register Now
          </motion.div>
        )}
        {/* {BigInt(Math.floor(Date.now() / 1000)) >= claimStartTime ? (
          <motion.button
            className={styles.loginButton}
            whileTap={
              userRewardInfo?.hasClaimed || (userReward ?? 0n) <= 0n
                ? undefined
                : buttonEffect.tap
            }
            style={{
              marginTop: "1rem",
              cursor:
                userRewardInfo?.hasClaimed || (userReward ?? 0n) <= 0n
                  ? "not-allowed"
                  : "pointer",
            }}
            onClick={handleClaim}
            disabled={
              isClaiming ||
              isWaitingForClaim ||
              userRewardInfo?.hasClaimed ||
              (userReward ?? 0n) <= 0n
            }
          >
            {isClaiming || isWaitingForClaim
              ? "Claiming..."
              : userRewardInfo?.hasClaimed
              ? "Already Claimed"
              : (userReward ?? 0n) <= 0n
              ? "No Rewards"
              : "Claim Rewards"}
          </motion.button>
        ) : (
          <button
            className={styles.loginButton}
            style={{ marginTop: "1rem", cursor: "not-allowed", color: "gray" }}
            disabled
          >
            Claim start at {formatTimestamp(claimStartTime)}
          </button>
        )} */}
      </div>
    </div>
  );
};

export default Buyer;

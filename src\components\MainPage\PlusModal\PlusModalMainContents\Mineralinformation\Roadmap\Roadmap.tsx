import styles from "./Roadmap.module.scss";
import variables from "@/styles/variables.module.scss";
import {
  VerticalTimeline,
  VerticalTimelineElement,
} from "react-vertical-timeline-component";
import "react-vertical-timeline-component/style.min.css";
import { CSSProperties } from "react";
import AsteroidIcon from "./AsteroidIcon/AsteroidIcon";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";

interface TimelineStyle {
  lineColor: string;
  contentStyle: CSSProperties;
  contentArrowStyle: CSSProperties;
  contentIconStyle: CSSProperties;
}

const timelineStyle: TimelineStyle = {
  lineColor: variables.colorPrimaryTransparent,
  contentStyle: {
    background: variables.colorBlackTransparentMedium,
    border: `1px solid ${variables.colorPrimaryContrast}`,
  },
  contentArrowStyle: {
    borderRight: `12px solid ${variables.colorPrimaryContrast}`,
  },
  contentIconStyle: {
    background: variables.colorBlackTransparentMedium,
  },
};

const Roadmap = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  return (
    <div className={styles.container}>
      <div className={styles.titleWrapper}>
        <h1>Roadmap</h1>
        <h2>Details about the project</h2>
      </div>
      <VerticalTimeline lineColor={timelineStyle.lineColor}>
        {selectedMine.roadmap.map((stage, index) => (
          <VerticalTimelineElement
            visible={true}
            key={index}
            contentStyle={timelineStyle.contentStyle}
            contentArrowStyle={timelineStyle.contentArrowStyle}
            date={`phase ${index + 1}`}
            dateClassName={styles.dateClassName}
            iconStyle={timelineStyle.contentIconStyle}
            icon={<AsteroidIcon />}
          >
            <h4>
              Valuation: <span>{stage.valuation}</span>
            </h4>
            <h4>
              Financing: <span>{stage.financing}</span>
            </h4>
            <p>{stage.description}</p>
          </VerticalTimelineElement>
        ))}
      </VerticalTimeline>
    </div>
  );
};

export default Roadmap;

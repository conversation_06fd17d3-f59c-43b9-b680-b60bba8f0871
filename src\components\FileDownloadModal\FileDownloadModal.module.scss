@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  @include col-center;
  z-index: 1000;
}

.modal {
  background: $color-black-transparent-dark;
  border: 2px solid $color-primary;
  border-radius: $border-radius-lg;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    width: 95%;
    max-height: 85vh;
  }
}

.header {
  background: linear-gradient(135deg, $color-primary, $color-primary-contrast);
  padding: $padding-lg $padding-xl;
  @include row-between;
  border-bottom: 1px solid $color-primary;

  .titleSection {
    h2 {
      font-family: $font-family-orbitron;
      font-size: $font-size-2xl;
      font-weight: $font-weight-bold;
      color: white;
      margin: 0;
      text-transform: uppercase;

      @media (max-width: 768px) {
        font-size: $font-size-xl;
      }
    }

    p {
      font-family: $font-family-saira;
      font-size: $font-size-md;
      color: rgba(255, 255, 255, 0.8);
      margin: $margin-sm 0 0 0;

      @media (max-width: 768px) {
        font-size: $font-size-sm;
      }
    }
  }

  .closeButton {
    background: none;
    border: 2px solid white;
    border-radius: $border-radius-sm;
    padding: $padding-sm;
    cursor: pointer;
    @include col-center;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: white;
      transform: scale(1.05);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    img {
      width: 16px;
      height: 16px;
      filter: invert(1);
    }
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: $padding-xl;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 196, 208, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-primary;
    border-radius: 4px;
  }

  @media (max-width: 768px) {
    padding: $padding-lg;
  }
}

.controls {
  @include row-between;
  margin-bottom: $margin-lg;
  padding-bottom: $padding-md;
  border-bottom: 1px solid rgba(0, 196, 208, 0.2);

  .selectAllButton {
    background: transparent;
    border: 1px solid $color-primary;
    color: $color-primary;
    padding: $padding-sm $padding-lg;
    border-radius: $border-radius-sm;
    font-family: $font-family-saira;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: $color-primary;
      color: white;
    }
  }

  .selectionCount {
    font-family: $font-family-saira;
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.7);
    font-weight: $font-weight-medium;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: $spacing-md;
    align-items: flex-start;
  }
}

.fileGroups {
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
}

.fileGroup {
  .categoryTitle {
    font-family: $font-family-orbitron;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $color-primary;
    margin: 0 0 $margin-md 0;
    text-transform: uppercase;
    letter-spacing: 1px;

    @media (max-width: 768px) {
      font-size: $font-size-md;
    }
  }

  .fileList {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }
}

.footer {
  padding: $padding-lg $padding-xl;
  border-top: 1px solid rgba(0, 196, 208, 0.2);
  @include row-between;
  gap: $spacing-md;

  .cancelButton {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.7);
    padding: $padding-md $padding-lg;
    border-radius: $border-radius-sm;
    font-family: $font-family-saira;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(255, 255, 255, 0.6);
      color: white;
    }
  }

  .downloadButton {
    background: linear-gradient(
      135deg,
      $color-primary,
      $color-primary-contrast
    );
    border: none;
    color: white;
    padding: $padding-md $padding-xl;
    border-radius: $border-radius-sm;
    font-family: $font-family-saira;
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover:not(.disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 196, 208, 0.4);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: rgba(255, 255, 255, 0.1);
    }
  }

  @media (max-width: 768px) {
    padding: $padding-lg;
    flex-direction: column;

    .cancelButton,
    .downloadButton {
      width: 100%;
      text-align: center;
    }
  }
}

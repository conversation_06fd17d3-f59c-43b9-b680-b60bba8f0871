import React, { Dispatch, SetStateAction } from "react";
import { CountdownCircleTimer } from "react-countdown-circle-timer";
import styles from "./CountdownTimer.module.scss";

type TRenderTime =
  | string
  | number
  | boolean
  | React.ReactElement<any, string | React.JSXElementConstructor<any>>
  | Iterable<React.ReactNode>
  | React.PromiseLikeOfReactNode
  | null
  | undefined;

type TCountdownTimerProps = {
  remainingTime: number;
  setIsCheckinAvaliable: Dispatch<SetStateAction<boolean>>;
};

const minuteSeconds = 60;
const hourSeconds = 3600;
const daySeconds = 86400;

const timerProps = {
  isPlaying: true,
  size: 120,
  strokeWidth: 6,
};

const renderTime = (dimension: TRenderTime, time: TRenderTime) => {
  return (
    <div className={styles.timeWrapper}>
      <div className={styles.time}>{time}</div>
      <div>{dimension}</div>
    </div>
  );
};

const getTimeSeconds = (time: number) => (minuteSeconds - time) | 0;
const getTimeMinutes = (time: number) =>
  ((time % hourSeconds) / minuteSeconds) | 0;
const getTimeHours = (time: number) => ((time % daySeconds) / hourSeconds) | 0;

export default function CountdownTimer({
  remainingTime,
  setIsCheckinAvaliable,
}: TCountdownTimerProps) {
  return (
    <>
      {remainingTime > 0 ? (
        <>
          <h1 className={styles.title}>Next Checkin Time</h1>
          <div className={styles.container}>
            <CountdownCircleTimer
              {...timerProps}
              duration={daySeconds}
              initialRemainingTime={remainingTime % daySeconds}
              colors={"#FF4ECD"}
              onComplete={(totalElapsedTime) => ({
                shouldRepeat: remainingTime - totalElapsedTime > hourSeconds,
              })}
              onUpdate={(remainingTime) => {
                if (remainingTime === 0) {
                  setIsCheckinAvaliable(true);
                }
              }}
            >
              {({ elapsedTime, color }) => (
                <span style={{ color }}>
                  {renderTime("hours", getTimeHours(daySeconds - elapsedTime))}
                </span>
              )}
            </CountdownCircleTimer>
            <CountdownCircleTimer
              {...timerProps}
              duration={hourSeconds}
              initialRemainingTime={remainingTime % hourSeconds}
              colors={"#006FEE"}
              onComplete={(totalElapsedTime) => ({
                shouldRepeat: remainingTime - totalElapsedTime > minuteSeconds,
              })}
            >
              {({ elapsedTime, color }) => (
                <span style={{ color }}>
                  {renderTime(
                    "minutes",
                    getTimeMinutes(hourSeconds - elapsedTime),
                  )}
                </span>
              )}
            </CountdownCircleTimer>
            <CountdownCircleTimer
              {...timerProps}
              duration={minuteSeconds}
              initialRemainingTime={remainingTime % minuteSeconds}
              colors={"#17C964"}
              onComplete={(totalElapsedTime) => ({
                shouldRepeat: remainingTime - totalElapsedTime > 0,
              })}
            >
              {({ elapsedTime, color }) => (
                <span style={{ color }}>
                  {renderTime("seconds", getTimeSeconds(elapsedTime))}
                </span>
              )}
            </CountdownCircleTimer>
          </div>
        </>
      ) : null}
    </>
  );
}

import { poppins, saira, mavenPro } from "@/styles/fonts";
import "./globals.scss";
import type { Metadata } from "next";
import { WagmiProvider } from "./WagmiProvider";
import GoogleTags from "@/components/Analytics/GoogleTags";

export const metadata: Metadata = {
  manifest: "/mainfest.json",
  title: "AsteroidX",
  description:
    "AsteroidX is an incubation center that offers physical asset digitization, pegging and equity trading services for early-stage physical mining projects, leveraging web3 technology",
};

export const viewport = {
  themeColor: "#FFFFFF",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${saira.className} ${saira.variable} ${mavenPro.variable}`}
    >
      <head>
        {/* Twitter conversion tracking base code */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','omqqw');
            `,
          }}
        />
        {/* End Twitter conversion tracking base code */}
      </head>
      <body>
        <WagmiProvider>{children}</WagmiProvider>
      </body>
      <GoogleTags />
    </html>
  );
}

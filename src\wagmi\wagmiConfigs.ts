import {
  hashkey,
  hashkeyTestnet,
  BNB,
  BNBTestnet,
} from "@/constants/customChains";
import { configureChains, createConfig } from "wagmi";
import {
  mainnet,
  sepolia,
  bsc,
  bscTestnet,
  polygon,
  polygonMumbai,
} from "wagmi/chains";
import { CoinbaseWalletConnector } from "wagmi/connectors/coinbaseWallet";
import { InjectedConnector } from "wagmi/connectors/injected";
import { MetaMaskConnector } from "wagmi/connectors/metaMask";
import { WalletConnectConnector } from "wagmi/connectors/walletConnect";
import { alchemyProvider } from "wagmi/providers/alchemy";
import { infuraProvider } from "wagmi/providers/infura";

import { publicProvider } from "wagmi/providers/public";

// 类型增强：直接在当前文件中声明Window扩展
declare global {
  interface Window {
    okxwallet?: any;
    trustwallet?: any;
    BinanceChain?: any;
    bitkeep?: {
      ethereum?: any;
    };
  }
}

export const supportedNetwork = [
  ...(process.env.NEXT_PUBLIC_MODE === "development"
    ? [hashkeyTestnet, BNBTestnet]
    : [hashkey, BNB]),
];

const { chains, publicClient, webSocketPublicClient } = configureChains(
  supportedNetwork,
  [
    // infuraProvider({ apiKey: process.env.NEXT_PUBLIC_INFURA_API_KEY }),
    alchemyProvider({ apiKey: process.env.NEXT_PUBLIC_ALCHEMY_API_KEY }),
    publicProvider(),
  ],
);

// Project ID for WalletConnect v2
const projectId =
  process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || "default-project-id";

export const config = createConfig({
  autoConnect: true,
  connectors: [
    new MetaMaskConnector({ chains }),
    new CoinbaseWalletConnector({
      chains,
      options: {
        appName: "AsteroidX",
      },
    }),
    // OKX Wallet connector
    new InjectedConnector({
      chains,
      options: {
        name: "OKX Wallet",
        getProvider: () =>
          typeof window !== "undefined" ? window.okxwallet : undefined,
      },
    }),
    // WalletConnect v2 connector
    new WalletConnectConnector({
      chains,
      options: {
        projectId,
        metadata: {
          name: "AsteroidX",
          description: "AsteroidX Web3 Platform",
          url: "https://asteroidx.io",
          icons: ["https://asteroidx.io/logo.png"],
        },
      },
    }),
    // Trust Wallet connector
    new InjectedConnector({
      chains,
      options: {
        name: "Trust Wallet",
        getProvider: () =>
          typeof window !== "undefined" ? window.trustwallet : undefined,
      },
    }),
    // Rabby Wallet connector
    new InjectedConnector({
      chains,
      options: {
        name: "Rabby Wallet",
        getProvider: () =>
          typeof window !== "undefined" && window.ethereum?.isRabby
            ? window.ethereum
            : undefined,
      },
    }),
    // Binance Wallet connector
    new InjectedConnector({
      chains,
      options: {
        name: "Binance Wallet",
        getProvider: () =>
          typeof window !== "undefined" ? window.BinanceChain : undefined,
      },
    }),
    // Bitget Wallet connector
    new InjectedConnector({
      chains,
      options: {
        name: "Bitget Wallet",
        getProvider: () =>
          typeof window !== "undefined" ? window.bitkeep?.ethereum : undefined,
      },
    }),
  ],
  publicClient,
  webSocketPublicClient,
});

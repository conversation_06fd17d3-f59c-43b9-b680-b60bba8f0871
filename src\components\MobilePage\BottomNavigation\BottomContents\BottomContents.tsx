import styles from "./BottomContents.module.scss";
import NavbarButton from "../NavbarButton/NavbarButton";
import ListedIcon from "../components/ListedIcon";
import DepositIcon from "../components/DepositIcon";
import PurchaseIcon from "../components/PurchaseIcon";
import LaunchIcon from "../components/LaunchIcon";
import MineIcon from "../components/MineIcon";
import PrivateSaleIcon from "../components/PrivateSaleIcon";
import { useState } from "react";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import { useAccount } from "wagmi";

export const navbarButtonDetails = [
  {
    icon: (color: string) => <ListedIcon color={color} />,
    title: "overview",
    subTitle: "",
  },
  {
    icon: (color: string) => <PrivateSaleIcon color={color} />,
    title: "presale",
    subTitle: "",
  },
  {
    icon: (color: string) => <MineIcon color={color} />,
    title: "Mines",
    subTitle: "",
  },
  {
    icon: (color: string) => <PurchaseIcon color={color} />,
    title: "Trade",
    subTitle: "",
  },
  {
    icon: (color: string) => <LaunchIcon color={color} />,
    title: (isConnected: boolean) => (isConnected ? "profile" : "login"),
    subTitle: "",
  },
];

const NavbarContent = () => {
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const { isConnected } = useAccount();
  const navbarButtonProps = {
    selectedButton: navbarButtonSnapshot.selectedButton,
    setSelectedButton: navbarButtonSnapshot.setSelectedButton,
    isInitialAnimationDone: navbarButtonSnapshot.isInitialAnimationDone,
    setIsInitialAnimationDone: navbarButtonSnapshot.setIsInitialAnimationDone,
  };

  return (
    <div className={styles.container}>
      {navbarButtonDetails.map((buttonDetails, index) => (
        <NavbarButton
          key={index}
          buttonDetails={{
            ...buttonDetails,
            title:
              typeof buttonDetails.title === "function"
                ? buttonDetails.title(isConnected)
                : buttonDetails.title,
          }}
          {...navbarButtonProps}
        />
      ))}
    </div>
  );
};

export default NavbarContent;

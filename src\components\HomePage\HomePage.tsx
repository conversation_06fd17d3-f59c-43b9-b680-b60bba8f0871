"use client";

import { useRouter } from "next/navigation";
import styles from "./HomePage.module.scss";
import { motion } from "framer-motion";
import { bottomUpFadeIn, buttonEffect, fadeIn } from "@/animations/animations";
import TitleText from "./TitleText/TitleText";
import { Canvas } from "@react-three/fiber";
import Experience from "./Experience/Experience";
import { useLayoutEffect } from "react";

const HomePage = () => {
  const { replace } = useRouter();
  useLayoutEffect(() => {
    if (window.localStorage.getItem("asteroidx-user-visited") !== null) {
      replace("/main");
    }
  }, []);
  return (
    <div className={styles.canvasContainer}>
      <Experience />
    </div>

    // <motion.div
    //   className={styles.container}
    //   variants={fadeIn(2)}
    //   initial="hidden"
    //   animate="visible"
    // >
    //   <div className={styles.wrapper}>
    //     <TitleText delay={2} />
    //     <motion.button
    //       variants={bottomUpFadeIn(2, 3)}
    //       whileHover={buttonEffect.hover}
    //       whileTap={buttonEffect.tap}
    //       className={styles.exploreButton}
    //       onClick={() => {
    //         replace("/main");
    //       }}
    //     >
    //       START EXPLORE
    //     </motion.button>
    //   </div>
    // </motion.div>
  );
};

export default HomePage;

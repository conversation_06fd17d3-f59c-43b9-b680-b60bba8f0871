@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);
$hightlightBackground: rgba($color-primary, 0.3);

.wrapper {
  position: fixed;
  top: 52%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px; /* 可以根据实际需求调整最大宽度 */
  height: 85%;
  max-height: 600px; /* 可以根据实际需求调整最大高度 */
  z-index: 1000;
  background: $color-black-transparent;
  backdrop-filter: blur(10px);
  // mask-image: linear-gradient(to bottom, black 85%, transparent);
  padding: $padding-md;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    // background: linear-gradient(
    //   to bottom,
    //   $color-primary,
    //   $color-black-transparent
    // );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }

  .header {
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    letter-spacing: 1px;
    width: 100%;
    color: #00e0ff;
  }
}

.headerWrapper {
  @include row-between;
  // margin-bottom: 1rem;
  gap: 1rem;
  
  .closeButton {
    margin-top: 0.5rem;
  }
}

.contentFrame {
  position: absolute;
  width: 90%;
  height: 90%;
  padding: 1rem;
  overflow: scroll;
  scroll-behavior: smooth;
  gap: 1rem;

  &::-webkit-scrollbar {
    display: none;
  }
}

// Add these new styles to your existing file

.mineSelectionGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;
  margin-bottom: 1.5rem;
  overflow-y: auto;
  // border: 1px solid yellow;
}

.mineCard {
  // position: relative;
  border: 1px solid rgba(0, 224, 255, 0.3);
  border-radius: 8px;
  padding: 1.3rem;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 224, 255, 0.1), transparent);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.2);

    &::before {
      opacity: 1;
    }
  }

  &.selected {
    border: 2px solid rgba(0, 224, 255, 1);
    box-shadow: 0 0 15px rgba(0, 224, 255, 0.4);
  }

  &.hovered {
    border: 1px solid rgba(0, 224, 255, 0.6);
  }
}

.mineCardContent {
  position: relative;
  z-index: 1;
  text-align: center;
}

.mineName {
  color: #00e0ff;
  font-size: 1rem;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
}

.currentSelection {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(0, 224, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 224, 255, 0.3);

  p {
    color: #00e0ff;
    margin: 0;
    font-size: 0.9rem;
  }
}

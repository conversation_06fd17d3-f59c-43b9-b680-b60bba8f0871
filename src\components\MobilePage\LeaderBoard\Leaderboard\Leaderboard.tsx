import React, { useEffect } from "react";
import styles from "./Leaderboard.module.scss";
import { pointSystemData } from "@/constants/pointSystem/final";
import { PointsData } from "@/stores/pointsData";
import table from "@/assets/images/table.jpg";
import Image from "next/image";
// import avatarIcon from "@/components/Statistics/assets/pointSystemIcon.png";

interface LeaderboardProps {
  pointsData: PointsData;
}

const leaderboardData = pointSystemData.slice(0, 10);
const trimWalletAddress = (address: string) => {
  return address.slice(0, 6) + "..." + address.slice(-4);
};

const Leaderboard: React.FC<LeaderboardProps> = ({ pointsData }) => {
  return (
    <div className={styles.leaderboardContainer}>
      <div className={styles.title}>
        <Image src={table} alt="table" className={styles.table} />
        <div>LeaderBoard</div>
      </div>
      <div className={styles.leaderboardList}>
        {pointsData.pointsItems.map((entry, index) => (
          <div key={index} className={styles.leaderboardRow}>
            <div className={styles.positionWrapper}>
              <div className={styles.positionLabel}>#{index + 1}</div>
            </div>
            <div className={styles.pointsValue}>
              <div className={styles.addressValue}>
                {trimWalletAddress(entry.userAddress)}
              </div>
              <div>{entry.totalPoints} Points </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Leaderboard;

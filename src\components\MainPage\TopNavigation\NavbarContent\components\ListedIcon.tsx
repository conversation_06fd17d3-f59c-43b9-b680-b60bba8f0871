import variables from "@/styles/variables.module.scss";
const ListedIcon = ({ color = variables.colorPrimaryContrast }) => {
  return (
    <svg
      width="56"
      height="56"
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Frame">
        <path
          id="Vector"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M24.8333 11L44.6667 46H5L24.8333 11Z"
          stroke={color}
          strokeWidth="3"
          strokeLinejoin="round"
        />
        <path
          id="Vector_2"
          d="M42.9166 46H51.6666L42.3333 29.6667L38.8333 35.5"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export default ListedIcon;

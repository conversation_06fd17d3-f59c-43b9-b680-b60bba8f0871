@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 4rem;
  @include row-between;
  // margin-top: $margin-sm;
  // justify-content: flex-start;
  // gap: $spacing-sm;
  // border: 1px solid yellow;

  .leftContainer {
    width: 100%;
    @include row-between;
    justify-content: flex-start;
  }
  .subtitle {
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-xs;
    // border: 3px solid yellow;

    .mineral {
      background: rgba(255, 255, 255, 0.02);
      // padding: 0 $padding-xs;
      border-radius: $border-radius-sm;
      font-size: $font-size-2xs;
      // color: $color-primary-transparent-contrast;
      color: yellow;
      font-weight: $font-weight-medium;
    }

    .location {
      font-size: $font-size-2xs;
      font-weight: $font-weight-bold;
      color: $color-primary;
    }
  }

  .title {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $color-primary;
  }

  .rightImage {
    align-self: flex-end;
    // border: 3px solid yellow;
    flex-shrink: 1;
    // margin-right: $margin-sm;
    cursor: pointer;
  }
}

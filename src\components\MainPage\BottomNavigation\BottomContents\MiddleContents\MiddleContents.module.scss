@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin subText {
  font-size: $font-size-sm;
  color: $color-primary-transparent-contrast;
  font-weight: $font-weight-light;
}

@mixin highlight {
  font-size: $font-size-md;
  color: $color-primary;
  font-weight: $font-weight-bold;
}
.container {
  width: 100%;
  height: 100%;
  // border: 1px solid yellow;
  flex: 4;
  @include col-between;
  align-items: flex-start;
  padding: 0 $padding-md;

  .header {
    // line-height: 108%;
    .title {
      font-size: $font-size-md;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subtitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }

  .mineDetails {
    height: 40px;
    width: 100%;
    border: $border-width-xs solid $color-primary-transparent-contrast;
    border-radius: $border-radius-sm;
    background: $color-primary-transparent;
    backdrop-filter: blur(20px);
    padding: 0 $padding-md;
    @include row-between;

    .mineRegion {
      @include subText;
      .mineName {
        @include highlight();
        color: white;
      }
    }
    .mineNumber {
      @include subText;
    }
    .mineMineral {
      @include subText;
      .mineral {
        @include highlight();
        color: white;
      }
    }
    .price {
      // @include subText;
      @include highlight();
    }
  }
}

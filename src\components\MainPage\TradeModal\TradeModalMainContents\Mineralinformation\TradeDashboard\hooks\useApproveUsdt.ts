import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError } from "viem";
import { parseTokenAmount } from "@/utils/tokenPriceFormatter";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import { extractErrorType } from "@/utils/errorHandling";
import { usdtABI } from "@/constants/abis/UsdtABI";

type ApproveUsdtProps = {
  usdtValue?: bigint;
  purchaseListing?: () => void;
  handleDecimals?: boolean;
  refresh?: number;
};

const useApproveUsdt = ({
  purchaseListing,
  usdtValue,
  handleDecimals = false,
  refresh = 0,
}: ApproveUsdtProps = {}) => {
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [usdtAllowance, setUsdtAllowance] = useState(0n);
  const [isApprovingUsdt, setIsApprovingUsdt] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [tokenDecimals, setTokenDecimals] = useState<number>(18);
  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setUsdtAddress(networkConfigs[chain.id].usdtAddress);
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
    } else {
      setUsdtAddress("0x");
      setMarketplaceAddress("0x");
    }
  }, [chain]);

  useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "decimals",
    enabled: isConnected && usdtAddress !== "0x",
    onSuccess: (data) => {
      setTokenDecimals(Number(data));
    },
    onError: () => {
      setTokenDecimals(18);
    },
  });

  const { refetch: refetchAllowance } = useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "allowance",
    args: [address ?? "0x", marketplaceAddress],
    enabled:
      isConnected &&
      usdtAddress !== "0x" &&
      marketplaceAddress !== "0x" &&
      address !== undefined,
    watch: true,
    cacheTime: 0, // Don't cache the result
    onSuccess: (data) => {
      //console.log("[USDT] Current allowance:", data.toString());
      setUsdtAllowance(data);
    },
    onError: () => {
      setUsdtAllowance(0n);
    },
  });

  const { config: approveUsdtConfig, error: prepareError } =
    usePrepareContractWrite({
      address: usdtAddress,
      abi: usdtABI,
      functionName: "approve",
      args: [
        marketplaceAddress,
        // Set unlimited approval amount (max uint256 value)
        // 2^256 - 1 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
        0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn,
      ],
      enabled:
        isConnected &&
        usdtAddress !== "0x" &&
        marketplaceAddress !== "0x" &&
        usdtValue !== undefined &&
        (usdtValue ?? 0n) > 0n,
      onError: (error: any) => {
        if (error.cause?.reason) {
          setErrorMessage(error.cause.reason);
        } else {
          setErrorMessage(error.shortMessage || error.message);
        }
      },
    });

  const { write: approveUsdtWrite, data: approveUsdtData } = useContractWrite({
    ...approveUsdtConfig,
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
      setIsApprovingUsdt(false);
    },
  });

  const { isLoading: isWaitingForApproveUsdt } = useWaitForTransaction({
    confirmations: 6,
    hash: approveUsdtData?.hash,
    onSuccess: async () => {
      showSuccess("Successfully approved USDT");

      // Force refresh the allowance data from the blockchain
      try {
        const result = await refetchAllowance();
        //console.log("[USDT] Updated allowance after approval:", result.data?.toString());

        // Only proceed with purchase after we've confirmed the updated allowance
        if (result.data && result.data > 0n && purchaseListing && refresh > 0) {
          //console.log("[USDT] Allowance confirmed, proceeding with purchase...");
          purchaseListing();
        }
      } catch (error) {
        //console.error("[USDT] Failed to refresh allowance:", error);
      }

      setIsApprovingUsdt(false);
    },
    onError: (error) => {
      showError(
        extractErrorType(error) ||
          (error as BaseError).shortMessage ||
          error.message,
      );
      setIsApprovingUsdt(false);
    },
  });

  const handleApproveUsdt = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (errorMessage) {
      showError(errorMessage);
      return;
    }

    if (!approveUsdtWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      }
      return;
    }

    setIsApprovingUsdt(true);
    try {
      approveUsdtWrite?.();
    } catch (error) {
      setIsApprovingUsdt(false);
      showError(extractErrorType(error) || "Failed to approve USDT");
    }
  };

  return {
    usdtAllowance,
    handleApproveUsdt,
    isApprovingUsdt,
    isWaitingForApproveUsdt,
    tokenDecimals,
  };
};

export default useApproveUsdt;

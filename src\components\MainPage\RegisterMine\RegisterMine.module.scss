@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  background: $color-primary-transparent;
  backdrop-filter: blur(30px);
  @include row-center;
  // align-items: flex-end;

  .formContainer {
    position: relative;
    width: 40%;
    height: 86%;
    border-radius: 1rem;
    padding: 1rem 3rem;
    margin-top: 6rem;
    background: $color-black-transparent;
    @include col-center;

    .loadingOverlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: $color-black-transparent;
      border-radius: 1rem;
      backdrop-filter: blur(30px);
      @include col-center;
    }

    .formDataGroup {
      width: 100%;
      @include row-center;
      gap: 6rem;
    }

    h1 {
      text-align: center;
    }

    .divider {
      margin: 1rem 0;
      width: 100%;
      height: 1px;
      background: grey;
    }

    .formDataContainer {
      width: 100%;
      height: 100%;
      // border: 1px solid yellow;
      overflow: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .submitButton {
        width: 100%;
        margin: 1rem 0;
        padding: 0.5rem;
        background: $color-primary-contrast;
        color: white;
        border: none;
        border-radius: 1rem;
        text-transform: uppercase;
        cursor: pointer;

        &:hover {
          background: #00000090;
        }
      }

      .formSection {
        @include col-center;
        align-items: flex-start;
        width: 100%;
        margin-bottom: 1rem;

        label {
          color: $color-primary;
          font-weight: $font-weight-semibold;
          font-size: $font-size-xl;
        }

        input,
        select {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid grey;
          border-radius: 0.25rem;
          background: transparent;
          color: white;

          &:focus {
            outline: $color-warning solid 2px;
          }
        }

        p {
          color: $color-danger;
          font-size: $font-size-sm;
        }

        input::file-selector-button {
          text-transform: uppercase;
          font-weight: bold;
          color: dodgerblue;
          padding: 0.5em;
          border: 0px solid grey;
          border-radius: 3px;
          background: black;
          cursor: pointer;
        }

        .currentValuation {
          width: 100%;
          @include row-between;

          span {
            margin-right: 0.5rem;
            font-size: $font-size-xl;
          }
        }
      }
    }
  }
}

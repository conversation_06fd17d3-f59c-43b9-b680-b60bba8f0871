import { Chart } from "react-google-charts";
import variables from "@/styles/variables.module.scss";
import { minesDetails } from "@/constants/mineDetails";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;

export default function Barchart3d({
  data,
  backgroundColor = "transparent",
}: {
  data: (string | number)[][];
  backgroundColor?: string;
}) {
  return (
    <Chart
      chartType="PieChart"
      data={data}
      options={{
        backgroundColor,
        is3D: true,
        legend: {
          position: "right",
          textStyle: { color: variables.colorPrimary, fontSize: 16 },
        },
      }}
      width={"100%"}
      height={"100%"}
    />
  );
}

import { Chart } from "react-google-charts";
import variables from "@/styles/variables.module.scss";
import { minesDetails } from "@/constants/mineDetails";
import { useMediaQuery } from "usehooks-ts";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;

export default function Barchart3d({
  data,
  backgroundColor = "transparent",
}: {
  data: Unpacked<Unpacked<typeof minesDetails>["investmentDetails"]>["data"];
  backgroundColor?: string;
}) {
  const isMobile = useMediaQuery("(max-width: 1279px)");

  if (isMobile) {
    // 移动端
    return (
      <Chart
        chartType="PieChart"
        data={data}
        options={{
          backgroundColor,
          is3D: true,
          legend: "none",
          pieSliceText: "label",
          chartArea: { left: 0, top: 0, width: "100%", height: "100%" },
        }}
        width={"100%"}
        height={"100%"}
      />
    );
  }

  return (
    <Chart
      chartType="PieChart"
      data={data}
      options={{
        backgroundColor,
        is3D: true,
        legend: {
          position: "right",
          textStyle: { color: variables.colorPrimary, fontSize: 16 },
        },
      }}
      width={"100%"}
      height={"100%"}
    />
  );
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.title {
  width: 100%;
  @include titleText();
  // height: 100vh;

  @media screen and (max-width: 1279px) {
    font-size: $font-size-5xl;
  }
}

.teamCardsContainer {
  @include row-center;
  padding: $padding-md;
  gap: $spacing-3xl;
  flex-wrap: wrap;

  .parallaxEffect {
    // @include background;
    @include col-center;
    padding: $padding-5xl;
    width: 400px;
    // height: 500px;
    background-color: $color-black-transparent-medium;
    border: $border-width-md solid $color-primary-transparent;
    // border-radius: 20px;

    transform-style: preserve-3d;

    @media screen and (max-width: 1279px) {
      padding: $padding-lg;
    }

    .innerElement {
      // width: 100%;
      // @include col-center;
      // height: 580px;
      transform: translateZ(80px);
      // background: yellow;

      @media screen and (max-width: 1279px) {
        transform: translateZ(0);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        margin-bottom: $margin-md;

        @media screen and (max-width: 1279px) {
          height: 200px;
        }
      }
      h1 {
        font-size: $font-size-2xl;
        font-weight: $font-weight-extrabold;
        color: $color-primary;
        text-align: center;
      }
      h2 {
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $color-primary;
        text-align: center;
        text-decoration: underline;
      }
      h3 {
        font-size: $font-size-sm;
        font-weight: $font-weight-extralight;
        color: $color-primary;
      }
    }
  }
}

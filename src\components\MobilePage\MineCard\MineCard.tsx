import React from "react";
import styles from "./MineCard.module.scss";
import MinePreviewHeader from "./MinePreviewCard/MinePreviewHeader";
import MinePreviewBody from "./MinePreviewCard/MinePreviewBody";
import ParentComponent from "./MineNavBar";

export interface MineCardProps {
  onBack: () => void;
}

const MineCard = ({ onBack }: MineCardProps) => {
  return (
    <div className={styles.container}>
      <div className={styles.cardwrapper}>
        <div className={styles.cardHeader}>
          <MinePreviewHeader onBack={onBack} />
          <ParentComponent />
          {/* <MinePreviewBody /> */}
        </div>
        {/* <MineFooter /> */}
      </div>
    </div>
  );
};

export default MineCard;

import TokenPrice from "@/components/common/TokenPrice";
import useActiveListing from "./hooks/useActiveListing";
import styles from "./TradeDashboard.module.scss";
import usePurchaseAllListingAmount from "./hooks/usePurchaseAllListingAmount";
import { useState } from "react";
import useApprove from "./hooks/useApprove";
import useActiveOffer from "./hooks/useActiveOffer";
import useAcceptOffer from "./hooks/useAcceptOffer";
import { useAccount, useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";

const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const Offers = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { allActiveOfferSortedByPrice, formatUnixTimestamp } = useActiveOffer();
  const { chain } = useNetwork();
  const {
    acceptOffer,
    isAcceptingOffer,
    isWaitingForAcceptOffer,
    offerId,
    setOfferId,
  } = useAcceptOffer();
  const { isApprovedForAll } = useApprove();
  const [isOpen, setIsOpen] = useState(false);
  const { address } = useAccount();

  const handleAcceptOffer = () => {
    acceptOffer();
  };

  const handleMouseOver = (index: number) => {
    if (allActiveOfferSortedByPrice) {
      setOfferId(allActiveOfferSortedByPrice[index].offerId);
    }
  };

  return (
    <div className={styles.listings}>
      <div
        className={styles.headerContainer}
        onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className={styles.mainHeader}>OFFERS</h2>
        <span className={styles.toggleIcon}>{isOpen ? "-" : "+"}</span>
      </div>
      {isOpen && (
        <div className={styles.listingsTable}>
          <div className={styles.tableHeader}>
            <div>Address</div>
            <div>Price per Token</div>
            <div>Quantity</div>
            <div>Total Amount</div>
            <div>Expiration</div>
            <div>Action</div>
          </div>

          <div className={styles.tableBody}>
            {allActiveOfferSortedByPrice &&
            allActiveOfferSortedByPrice.length > 0 ? (
              allActiveOfferSortedByPrice.map((offer, index) => (
                <div className={styles.tableRow} key={index}>
                  <div>
                    <a
                      href={
                        chain &&
                        networkConfigs[chain.id].etherscanAddress + offer.buyer
                      }
                      target="_blank"
                      style={{ color: "#4895ef" }}
                    >
                      {trimWalletAddress(offer.buyer)}
                    </a>
                  </div>
                  <div className={styles.price}>
                    <TokenPrice
                      amount={offer.pricePerToken}
                      tokenAddress={offer.paymentToken}
                      showCurrency={true}
                      currencySymbol={selectedMine.currency}
                    />
                  </div>
                  <div className={styles.quantity}>
                    {offer.amount.toString()}
                  </div>
                  <div className={styles.price}>
                    <TokenPrice
                      amount={offer.pricePerToken * offer.amount}
                      tokenAddress={offer.paymentToken}
                      showCurrency={true}
                      currencySymbol={selectedMine.currency}
                    />
                  </div>
                  <div className={styles.expiration}>
                    {formatUnixTimestamp(Number(offer.expirationTime))}
                  </div>
                  <div>
                    <button
                      className={`${styles.buyButton} ${
                        isAcceptingOffer || isWaitingForAcceptOffer
                          ? styles.disabled
                          : ""
                      }`}
                      onClick={handleAcceptOffer}
                      onMouseEnter={() => handleMouseOver(index)}
                      disabled={
                        isAcceptingOffer ||
                        isWaitingForAcceptOffer ||
                        !isApprovedForAll ||
                        offer.buyer.toLowerCase() === address?.toLowerCase()
                      }
                      style={{
                        opacity:
                          isAcceptingOffer ||
                          isWaitingForAcceptOffer ||
                          !isApprovedForAll ||
                          offer.buyer.toLowerCase() === address?.toLowerCase()
                            ? 0.5
                            : 1,
                        pointerEvents:
                          isAcceptingOffer ||
                          isWaitingForAcceptOffer ||
                          !isApprovedForAll ||
                          offer.buyer.toLowerCase() === address?.toLowerCase()
                            ? "none"
                            : "auto",
                      }}
                    >
                      {(isAcceptingOffer || isWaitingForAcceptOffer) &&
                      offerId === offer.offerId
                        ? "Processing..."
                        : offer.buyer.toLowerCase() === address?.toLowerCase()
                        ? "YOUR OFFER"
                        : "ACCEPT"}
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noListings}>
                No active offering available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Offers;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  @include row-center;
  // border: 1px solid yellow;
  overflow: hidden;
}

.wrapper {
  width: 100%;
  height: 100%;
}

.buttonWrapper {
  position: relative;
  // margin: 0 $margin-sm;
  cursor: pointer;

  .buttonArrow {
    position: absolute;
    top: 85px;
    left: 0;
  }
}

.leftButtonWrapper {
  @extend .buttonWrapper;
}

.leftButtonWrapper:active {
  transform: scale(0.9);
}

.rightButtonWrapper {
  @extend .buttonWrapper;
  transform: scaleX(-1);
}

.rightButtonWrapper:active {
  transform: scaleX(-1) scale(0.9);
}

.indicatorWrapper {
  @include row-center;
  width: 100%;
}

.indicator {
  height: 0.5rem;
  width: 0.5rem;
  margin: $margin-sm;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
}

.active {
  background-color: $color-primary;
}

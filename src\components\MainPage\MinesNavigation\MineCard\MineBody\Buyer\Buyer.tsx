import * as Tooltip from "@radix-ui/react-tooltip";
import styles from "./Buyer.module.scss";
import { buyerInformation } from "@/sampleAssets/constants/buyerInformation";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import locationLightIcon from "@/assets/icons/minesNavigation/locationLightIcon.png";
import { MineCardProps } from "../../MineCard";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { useEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import {
  useAccount,
  useContractEvent,
  useContractRead,
  useContractReads,
  useNetwork,
  useSwitchNetwork,
} from "wagmi";
import { BaseError, Log } from "viem";
import { launchPadAbi } from "@/constants/abis/launchPadAbi";
import { creator<PERSON><PERSON>bi } from "@/constants/abis/creatorNF<PERSON>bi";
import { bigint } from "zod";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { useMediaQuery } from "usehooks-ts";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { showError, showSuccess } from "@/lib/notification";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import { connectWalletModalStore } from "@/stores/connectWalletModal";

type ContractList = {
  address: `0x${string}`;
  abi: typeof asteroidAddressABI;
  functionName: string;
  args: BigInt[];
};

const Buyer = ({ information }: Pick<MineCardProps, "information">) => {
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [contractList, setContractList] = useState<ContractList[]>([]);
  const [asteroidId, setAsteroidId] = useState<number>(0);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);

  const { chain } = useNetwork();
  const { switchNetwork, chains } = useSwitchNetwork({
    onError: (error) => {
      showError((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      showSuccess("Switched to supported network");
    },
  });
  const { isConnected } = useAccount();
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const supportedChain = chains.filter((x) =>
    information.supportedNetwork.includes(x.id),
  )[0]?.name;

  return (
    <div className={styles.container}>
      <div className={styles.loginButtonWrapper}>
        {isConnected ? (
          <motion.button
            className={styles.loginButton}
            whileTap={buttonEffect.tap}
            disabled={
              chain && !selectedMine.supportedNetwork.includes(chain?.id)
            }
            // disabled={!write}
            onClick={() => {
              // showConfetti();
              // purchaseNft();
              if (chain && chain.unsupported) {
                showError("Please Switch Network");
                return;
              }
              if (chain && selectedMine.supportedNetwork.includes(chain?.id)) {
                purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                  true,
                );
              } else {
                switchNetwork?.(
                  chain?.testnet
                    ? selectedMine.supportedNetwork[0]
                    : selectedMine.supportedNetwork[1],
                );
              }
            }}
          >
            {chain && selectedMine.supportedNetwork.includes(chain?.id)
              ? "Purchase This Mine"
              : supportedChain
              ? `Switch to ${supportedChain}`
              : "NOT AVALIABLE"}
          </motion.button>
        ) : (
          <motion.div
            className={styles.loginButton}
            whileTap={buttonEffect.tap}
            onClick={() => {
              connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
            }}
          >
            Log in <span>&nbsp;or&nbsp;</span> Register Now
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Buyer;

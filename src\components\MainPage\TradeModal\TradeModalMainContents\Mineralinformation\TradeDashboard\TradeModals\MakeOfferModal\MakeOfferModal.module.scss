@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  height: 100%;

  .modalFrame {
    margin-top: 6rem;
    width: 576px;
    max-height: 750px;
    position: relative;
    background: $color-black-transparent-dark;
    clip-path: polygon(
      0 0,
      calc(100% - 80px) 0,
      100% 80px,
      100% calc(100% - 48px),
      calc(100% - 48px) 100%,
      48px 100%,
      0 calc(100% - 48px)
    );

    .closeButton {
      position: absolute;
      width: 250px;
      height: 50px;
      top: 32px;
      right: -68px;
      cursor: pointer;
      border: $border-width-xs solid $color-primary;
      transform: rotate(45deg);

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(45deg);
      }

      &:hover {
        background: $color-primary-contrast;
      }
    }

    .contentWrapper {
      width: 100%;
      height: 100%;
      padding: 1.5rem;
      @include col-center;
      gap: 1rem;

      .header {
        width: 100%;
        color: $color-primary;
      }

      .scrollArea {
        width: 100%;
        height: 100%;
        overflow: scroll;
        scroll-behavior: smooth;
        // border: 1px solid yellow;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary;
      clip-path: polygon(
        0 0,
        calc(100% - 81px) 0,
        100% 81px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px),
        0 0,
        1px 1px,
        1px calc(100% - 48px - 0.41px),
        calc(48px + 0.41px) calc(100% - 1px),
        calc(100% - 48px - 0.41px) calc(100% - 1px),
        calc(100% - 1px) calc(100% - 48px - 0.41px),
        calc(100% - 1px) calc(48px + 0.41px),
        calc(100% - 48px - 0.41px) 1px,
        1px 1px
      );
    }
  }
}

.contentFrame {
  padding: 1rem 1rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  // height: 100%;
  position: relative;
  background: $color-black-transparent-dark;
  clip-path: polygon(
    0 0,
    calc(100% - 80px) 0,
    100% 80px,
    100% calc(100% - 48px),
    calc(100% - 48px) 100%,
    48px 100%,
    0 calc(100% - 48px)
  );

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    clip-path: polygon(
      0 0,
      calc(100% - 81px) 0,
      100% 81px,
      100% calc(100% - 48px),
      calc(100% - 48px) 100%,
      48px 100%,
      0 calc(100% - 48px),
      0 0,
      1px 1px,
      1px calc(100% - 48px - 0.41px),
      calc(48px + 0.41px) calc(100% - 1px),
      calc(100% - 48px - 0.41px) calc(100% - 1px),
      calc(100% - 1px) calc(100% - 48px - 0.41px),
      calc(100% - 1px) calc(48px + 0.41px),
      calc(100% - 48px - 0.41px) 1px,
      1px 1px
    );
  }
}

.derlordInfo {
  h3 {
    color: #00e0ff;
    font-size: 24px;
  }

  span {
    display: block;
    color: #00e0ff;
  }

  p {
    color: #00e0ff;
    font-size: 14px;
  }
}

.priceInput {
  label {
    display: block;
    color: #00e0ff;
    margin-bottom: 8px;
  }

  input {
    border-radius: 0.5rem;
    width: 100%;
    background: rgba(0, 224, 255, 0.1);
    border: 1px solid $color-primary-contrast;
    padding: 12px;
    color: #00e0ff;
    margin-bottom: 8px;

    &::placeholder {
      color: #00e0ff;
      opacity: 0.5;
    }
  }
}

.totalOffer {
  color: gray;
  text-align: right;
}

.quantitySection {
  @include row-between;
  .quantityLabel {
    display: flex;
    justify-content: space-between;
    color: #00e0ff;
    flex-direction: column;

    .available {
      color: gray;
    }
  }

  .quantityControls {
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    background: rgba(0, 224, 255, 0.1);
    border: 1px solid $color-primary-contrast;
    // padding: 8px;
    border-radius: 0.5rem;

    input {
      border-radius: 0.5rem;
      width: 100%;
      background: rgba(0, 224, 255, 0.1);
      border: none;
      padding: 12px;
      color: #00e0ff;
      // margin-bottom: 8px;
  
      &::placeholder {
        color: #00e0ff;
        opacity: 0.5;
      }
    }

    .quantityBtn {
      color: #00e0ff;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 20px;

      &:hover {
        color: white;
      }
    }

    span {
      color: #00e0ff;
    }
  }
}

.durationSection {
  span {
    display: block;
    color: #00e0ff;
    margin-bottom: 8px;
  }

  .durationControls {
    display: flex;
    gap: 16px;

    select {
      border-radius: 0.5rem;
      background: rgba(0, 224, 255, 0.1);
      border: 1px solid $color-primary-contrast;
      color: #00e0ff;
      padding: 8px 16px;
    }

    .dateDisplay {
      border-radius: 0.5rem;
      flex: 1;
      background: rgba(0, 224, 255, 0.1);
      border: 1px solid $color-primary-contrast;
      color: #00e0ff;
      padding: 8px 16px;
    }
  }
}

.completeButton {
  border-radius: 0.5rem;
  width: 100%;
  background: rgba(0, 224, 255, 0.2);
  border: 1px solid $color-primary-contrast;
  color: #00e0ff;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 224, 255, 0.3);
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }
}

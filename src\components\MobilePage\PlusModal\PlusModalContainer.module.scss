@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$card-width: 100%;
.container {
  // position: absolute;
  width: $card-width;
  height: 100%;
  backdrop-filter: blur(10px);
  @include row-center;
  mask-image: linear-gradient(to bottom, black 85%, transparent);
  // border: 1px solid yellow;

  &::-webkit-scrollbar {
    display: none;
  }

  .cardwrapper {
    width: 100%;
    height: 100%;
    // margin-left: $margin-sm;

    .cardHeader {
      width: $card-width;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      background: $color-black-transparent;
      backdrop-filter: blur(10px);
      // mask-image: linear-gradient(to bottom, black 65%, transparent);
      padding: $padding-sm;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%
      );
      // border: 1px solid yellow;

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        background: linear-gradient(
          to bottom,
          $color-primary,
          $color-black-transparent
        );

        clip-path: polygon(
          0 0,
          calc(100% - 2rem) 0,
          100% 2rem,
          100% 100%,
          0 100%,
          0 0,
          1px 1px,
          0.3rem calc(100% - 1px),
          calc(100% - 1px) calc(100% - 1px),
          calc(100% - 0.2rem) calc(2rem + 0.83px),
          calc(100% - 2rem - 0.83px) 1px,
          1px 1px
        );
      }

      .cardContentWrapper {
        padding: $padding-md;
      }
    }
  }
}

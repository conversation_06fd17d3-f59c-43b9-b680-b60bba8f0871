import { minesDetails } from "@/constants/mineDetails";
import styles from "./PurchaseMineCard.module.scss";
import buyButton from "./icons/buyButton.png";
import { useState } from "react";
import { motion } from "framer-motion";
import { useSnapshot } from "valtio";
import { tradeModalStore } from "@/stores/tradeModal";
import { mineCardStore } from "@/stores/mineCard";

type Unpacked<T> = T extends (infer U)[] ? U : never;
type PurchaseMineCardProps = Unpacked<typeof minesDetails>;
const PurchaseMineCard = ({ mine }: { mine: PurchaseMineCardProps }) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const tradeModalSnapshot = useSnapshot(tradeModalStore);
  const [showOverlay, setShowOverlay] = useState(false);

  return (
    <div className={styles.container}>
      <div
        className={styles.innerContainer}
        onMouseEnter={() => setShowOverlay(true)}
        onMouseLeave={() => setShowOverlay(false)}
      >
        {showOverlay ? (
          <div className={styles.buyOverlay}>
            <h1>{mine.name}</h1>
            <h3>${mine.minePrice}</h3>
            <motion.img
              src={buyButton.src}
              alt="buy button"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => {
                tradeModalSnapshot.setIsOpenTradeModal(true);
                tradeModalSnapshot.setMinePrice(mine.minePrice);
                mineCardSnapshot.setSelectedMine(mine.name);
              }}
            />
          </div>
        ) : null}
        <img src={mine.mineImages[1]} alt="purchase mine card" />
      </div>
    </div>
  );
};

export default PurchaseMineCard;

"use server";
import z from "zod";

/**
 * Retrieves market data from the Coingecko API.
 *
 * @return {Promise<Array<{symbol: string, price: number, priceChangePercentage24h: number}>>} An array of market data objects, each containing the symbol, price, and 24-hour price change percentage.
 */
export const getMarketData = async (): Promise<Array<MarketData>> => {
  let marketData: MarketData[] = [];
  const currency = "usd";
  const url =
    "https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=10&page=1&sparkline=false&locale=en";
  const response = await fetch(url, { cache: "no-cache" });

  // if (!response.ok) {
  //   throw new Error("Failed to fetch market data");
  // }

  const data = await response.json();

  if (!data.status) {
    marketData = (data as MarketDataResponse).map((market) => ({
      symbol: (market.symbol + currency).toUpperCase(),
      price: market.current_price,
      priceChangePercentage24h: market.price_change_percentage_24h,
    }));
  }

  return marketData;
};

// types
interface MarketData {
  symbol: string;
  price: number;
  priceChangePercentage24h: number;
}

const RoiSchema = z.object({
  times: z.number(),
  currency: z.string(),
  percentage: z.number(),
});

const MarketDataResponseElementSchema = z
  .object({
    id: z.string(),
    symbol: z.string(),
    name: z.string(),
    image: z.string(),
    current_price: z.number(),
    market_cap: z.number(),
    market_cap_rank: z.number(),
    fully_diluted_valuation: z.number(),
    total_volume: z.number(),
    high_24h: z.number(),
    low_24h: z.number(),
    price_change_24h: z.number(),
    price_change_percentage_24h: z.number(),
    market_cap_change_24h: z.number(),
    market_cap_change_percentage_24h: z.number(),
    circulating_supply: z.number(),
    total_supply: z.number(),
    max_supply: z.union([z.number(), z.null()]),
    ath: z.number(),
    ath_change_percentage: z.number(),
    ath_date: z.string(),
    atl: z.number(),
    atl_change_percentage: z.number(),
    atl_date: z.string(),
    roi: z.union([RoiSchema, z.null()]),
    last_updated: z.string(),
  })
  .array();

type MarketDataResponse = z.infer<typeof MarketDataResponseElementSchema>;

import styles from "./NavbarContent.module.scss";
import NavbarButton from "./NavbarButton/NavbarButton";
import ListedIcon from "./components/ListedIcon";
import DepositIcon from "./components/DepositIcon";
import PurchaseIcon from "./components/PurchaseIcon";
import LaunchIcon from "./components/LaunchIcon";
import { useState } from "react";
import { useSnapshot } from "valtio";
import { navbarButtonStore } from "@/stores/navbarButton";
import PrivateSaleIcon from "./components/PrivateSaleIcon";

export const navbarButtonDetails = [
  {
    icon: (color: string) => <ListedIcon color={color} />,
    title: "listed minesites",
    subTitle: "mines avaliable for purchase",
  },
  {
    icon: (color: string) => <PrivateSaleIcon color={color} />,
    title: "private sales",
    subTitle: "support us",
  },
  {
    icon: (color: string) => <DepositIcon color={color} />,
    title: "points system",
    subTitle: "check your points",
  },
  {
    icon: (color: string) => <PurchaseIcon color={color} />,
    title: "trade",
    subTitle: "trade your assets",
  },
  {
    icon: (color: string) => <LaunchIcon color={color} />,
    title: "my profile",
    subTitle: "check your assets",
  },
];

const NavbarContent = () => {
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);

  const navbarButtonProps = {
    selectedButton: navbarButtonSnapshot.selectedButton,
    setSelectedButton: navbarButtonSnapshot.setSelectedButton,
    isInitialAnimationDone: navbarButtonSnapshot.isInitialAnimationDone,
    setIsInitialAnimationDone: navbarButtonSnapshot.setIsInitialAnimationDone,
  };

  return (
    <div className={styles.container}>
      {navbarButtonDetails.map((buttonDetails, index) => (
        <NavbarButton
          key={index}
          buttonDetails={buttonDetails}
          {...navbarButtonProps}
        />
      ))}
    </div>
  );
};

export default NavbarContent;

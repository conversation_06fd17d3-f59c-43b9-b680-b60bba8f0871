import { useState, useEffect, Dispatch, SetStateAction } from "react";
import { CountdownCircleTimer } from "react-countdown-circle-timer";
import styles from "./CountdownTimer.module.scss";
import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";

type TRenderTime =
  | string
  | number
  | boolean
  | React.ReactElement<any, string | React.JSXElementConstructor<any>>
  | Iterable<React.ReactNode>
  | React.PromiseLikeOfReactNode
  | null
  | undefined;

type TCountdownTimerProps = {
  remainingTime: number;
  setIsCheckinAvaliable: Dispatch<SetStateAction<boolean>>;
};

const minuteSeconds = 60;
const hourSeconds = 3600;
const daySeconds = 86400;

const timerProps = {
  isPlaying: true,
  size: 70,
  strokeWidth: 2,
};

const renderTime = (dimension: TRenderTime, time: TRenderTime) => {
  return (
    <div className={styles.timeWrapper}>
      <div className={styles.time}>{time}</div>
      <div>{dimension}</div>
    </div>
  );
};

const getTimeSeconds = (time: number) => (minuteSeconds - time) | 0;
const getTimeMinutes = (time: number) =>
  ((time % hourSeconds) / minuteSeconds) | 0;
const getTimeHours = (time: number) => ((time % daySeconds) / hourSeconds) | 0;

export default function CountdownTimer({
  remainingTime: initialRemainingTime,
  setIsCheckinAvaliable,
}: TCountdownTimerProps) {
  const [remainingTime, setRemainingTime] = useState(initialRemainingTime);

  // 当父组件传入的 remainingTime 变化时，更新本地状态
  useEffect(() => {
    setRemainingTime(initialRemainingTime);
  }, [initialRemainingTime]);

  useEffect(() => {
    if (remainingTime <= 0) {
      return;
    }

    const timer = setInterval(() => {
      setRemainingTime((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          setIsCheckinAvaliable(true);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [remainingTime, setIsCheckinAvaliable]);

  const hours = getTimeHours(remainingTime);
  const minutes = getTimeMinutes(remainingTime);
  const seconds = remainingTime % minuteSeconds;

  const formatNumber = (num: number): string => {
    return num.toString().padStart(2, "0");
  };

  return (
    <>
      {remainingTime > 0 ? (
        <div className={styles.container}>
          <h1 className={styles.title}>Next Daily Checkin</h1>
          <h3>Earn Extra 5 points</h3>

          <motion.button
            className={styles.checkinButton}
            whileTap={buttonEffect.tap}
            // onClick={checkinUser}
            // disabled={isCheckingin}
          >
            {formatNumber(hours)} hours - {formatNumber(minutes)} Minutes -{" "}
            {formatNumber(seconds)}s
          </motion.button>
        </div>
      ) : null}

      {/* <CountdownCircleTimer
              {...timerProps}
              duration={daySeconds}
              initialRemainingTime={remainingTime % daySeconds}
              colors={"#FF4ECD"}
              onComplete={(totalElapsedTime) => ({
                shouldRepeat: remainingTime - totalElapsedTime > hourSeconds,
              })}
              onUpdate={(remainingTime) => {
                if (remainingTime === 0) {
                  setIsCheckinAvaliable(true);
                }
              }}
            >
              {({ elapsedTime, color }) => (
                <span style={{ color }}>
                  {renderTime("hours", getTimeHours(daySeconds - elapsedTime))}
                </span>
              )}
            </CountdownCircleTimer>
            <CountdownCircleTimer
              {...timerProps}
              duration={hourSeconds}
              initialRemainingTime={remainingTime % hourSeconds}
              colors={"#006FEE"}
              onComplete={(totalElapsedTime) => ({
                shouldRepeat: remainingTime - totalElapsedTime > minuteSeconds,
              })}
            >
              {({ elapsedTime, color }) => (
                <span style={{ color }}>
                  {renderTime(
                    "minutes",
                    getTimeMinutes(hourSeconds - elapsedTime),
                  )}
                </span>
              )}
            </CountdownCircleTimer>
            <CountdownCircleTimer
              {...timerProps}
              duration={minuteSeconds}
              initialRemainingTime={remainingTime % minuteSeconds}
              colors={"#17C964"}
              onComplete={(totalElapsedTime) => ({
                shouldRepeat: remainingTime - totalElapsedTime > 0,
              })}
            >
              {({ elapsedTime, color }) => (
                <span style={{ color }}>
                  {renderTime("seconds", getTimeSeconds(elapsedTime))}
                </span>
              )}
            </CountdownCircleTimer> */}
    </>
  );
}

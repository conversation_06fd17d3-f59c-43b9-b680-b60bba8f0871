@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between; // Changed from flex-end to space-between
  padding: 2rem 5rem 2rem 2rem;
  flex: 1;
}

// Add progress bar section styles
.progressSection {
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progressText {
  color: $color-primary;
  font-size: 1.5rem;
  display: flex;
  justify-content: space-between;
}

.progressBar {
  width: 100%;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin: 10px 0;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progressFill {
  height: 100%;
  background: linear-gradient(
    90deg,
    $color-primary,
    $color-primary-contrast
  ); /* Using theme variables instead of hardcoded colors */
  border-radius: 10px;
  transition: width 0.2s ease-out;
  position: relative;
  transform: scaleX(0);
  transform-origin: left;

  &.animate {
    transform: scaleX(1);
    transition: transform 2.5s cubic-bezier(0.19, 1, 0.22, 1); /* Increased from 1.5s to 2.5s */
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    right: 0;
    bottom: 0;
    width: 40%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.5) 50%,
      /* Increased opacity from 0.3 to 0.5 */ rgba(255, 255, 255, 0) 100%
    );
    box-shadow: 0 0 100px rgba(255, 255, 255, 0.7); /* Increased from 5px to 10px */
    animation: slideShimmer 3s infinite; /* Increased from 3s to 6s for slower, more obvious animation */
  }
}

.progressGlow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba($color-primary, 0.8); /* Using theme variable for glow color */
  opacity: 0;
  animation: glow 3s infinite alternate; /* Increased from 1.5s to 3s */
}

.progressText {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;

  span {
    &.countUp {
      animation: countUp 2s ease-out forwards;
    }
  }
}

.highlight {
  color: #00d2ff;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(0, 210, 255, 0.5);
}

@keyframes slideShimmer {
  0% {
    left: -50%;
  }
  100% {
    left: 100%;
  }
}

@keyframes glow {
  0% {
    opacity: 0.3;
    box-shadow: 0 0 5px rgba($color-primary, 0.5);
  }
  100% {
    opacity: 0.9; /* Increased from 0.8 to 0.9 */
    box-shadow:
      0 0 25px rgba($color-primary, 1),
      /* Increased intensity and size */ 0 0 40px
        rgba($color-primary-contrast, 0.8); /* Increased from 30px to 40px and opacity from 0.7 to 0.8 */
  }
}

@keyframes countUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.navLinks {
  @include row-center;
  gap: 1.5rem;
}

.navLink {
  color: white;
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  opacity: 0.8;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

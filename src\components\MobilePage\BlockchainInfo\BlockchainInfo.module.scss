@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  // position: fixed;
  // bottom: min(40px, 2.5vw);
  // left: min(40px, 2.5vw);
  // background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: min(12px, 0.5vw) min(16px, 1vw);
  border-radius: $border-radius-sm;
  // border: 1px solid rgba(0, 196, 208, 0.2);
  z-index: 100;
  box-shadow: 0 8px 32px 0 rgba(0, 196, 208, 0.1);
  width: 100%;

  .infoWrapper {
    .label {
      color: $color-primary;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      display: flex;
      align-items: center;
      gap: min(4px, 0.3vw);
      white-space: nowrap;

      &::after {
        content: "";
        display: inline-block;
        width: 4px;
        height: 4px;
        background-color: #00ff00;
        border-radius: 50%;
        box-shadow: 0 0 4px #00ff00;
        animation: pulse 2s infinite;
      }
    }

    .value {
      color: #ffd700;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      text-shadow: 0 0 1px rgba(255, 215, 0, 0.5);
      letter-spacing: 0.02em;
      opacity: 0.9;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .separator {
      color: $color-primary;
      font-size: $font-size-sm;
      margin: 0 min(4px, 0.3vw);
    }
    .infoItem {
      @include row-between;
      gap: min(4px, 0.3vw);
      flex-wrap: wrap;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(0.7);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// @media screen and (min-width: 2560px) {
//   .container {
//     max-width: 500px;

//     .infoWrapper .infoItem {
//       gap: 12px;

//       .label,
//       .value,
//       .separator {
//         font-size: 14px;
//       }
//     }
//   }
// }

// @media screen and (max-width: 1280px) {
//   .container {
//     max-width: 300px;
//   }
// }

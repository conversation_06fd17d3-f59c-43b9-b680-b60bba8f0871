import { FC, useState } from "react";
import styles from "./SelectMine.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { ignoreTradingMineLists } from "@/constants/ignoreMineList";

interface SelectMineModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SelectMineModal: FC<SelectMineModalProps> = ({ isOpen, onClose }) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const [hoveredMine, setHoveredMine] = useState<string | null>(null);

  const handleSelectMine = (mineName: string) => {
    mineCardStore.selectedMine = mineName;
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalFrame}>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>

        <div className={styles.contentWrapper}>
          <h2 className={styles.header}>SELECT MINE</h2>
          <div className={styles.scrollArea}>
            <div className={styles.contentFrame}>
              <div className={styles.mineSelectionGrid}>
                {minesDetails.map((mine) => {
                  if (ignoreTradingMineLists.includes(mine.shortName))
                    return <></>;
                  else {
                    return (
                      <div
                        key={mine.name}
                        className={`${styles.mineCard} ${
                          mineCardSnapshot.selectedMine === mine.name
                            ? styles.selected
                            : ""
                        } ${hoveredMine === mine.name ? styles.hovered : ""}`}
                        onClick={() => handleSelectMine(mine.name)}
                        onMouseEnter={() => setHoveredMine(mine.name)}
                        onMouseLeave={() => setHoveredMine(null)}
                        style={{
                          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9)), url(${mine.mineImages[1]})`,
                          backgroundSize: "cover",
                          backgroundPosition: "center",
                        }}
                      >
                        <div className={styles.mineCardContent}>
                          <h3 className={styles.mineName}>{mine.name}</h3>
                          {mineCardSnapshot.selectedMine === mine.name ? (
                            <h5 style={{ color: "yellow" }}>SELECTED</h5>
                          ) : null}
                        </div>
                      </div>
                    );
                  }
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectMineModal;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  position: absolute;
  bottom: 0;
  left: 0;

  // border: 1px solid yellow;
  width: 100%;
  @include row-center;

  .buttonWrapper {
    position: relative;
    // z-index: $z-index-1;
    bottom: -95px;
    cursor: pointer;

    .buttonArrow {
      position: absolute;
      // height: 100%;
      top: 90px;
      left: 0;
    }
  }
}

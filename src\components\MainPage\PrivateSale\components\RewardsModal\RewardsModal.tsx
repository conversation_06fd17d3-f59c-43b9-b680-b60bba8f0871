import React, { useLayoutEffect, useState } from "react";
import styles from "./RewardsModal.module.scss";
import { motion } from "framer-motion";
import { fadeIn } from "@/animations/animations";
import { getInvitationCodeFromOwnServer } from "@/actions/getInvitationCodeFromOwnServer";
import { useAccount } from "wagmi";
import { showSuccess } from "@/lib/notification";
import useTokenInfo from "../MainContent/CtaSection/SaleDetails/hooks/useTokenInfo";

interface RewardsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const formatAddress = (address: string) => {
  if (!address || address.length < 10) return address;
  return `${address.substring(0, 8)}...${address.substring(
    address.length - 8,
  )}`;
};

// (DD/MM/YYYY HH:MM:SS)
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000);

  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
};

const RewardsModal: React.FC<RewardsModalProps> = ({ isOpen, onClose }) => {
  const [shortCode, setShortCode] = useState("");
  const { address } = useAccount();
  const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";
  const isDev = process.env.NODE_ENV === "development";
  const websiteUrl = isDev
    ? "http://localhost:3000/main?invitation="
    : isTestnet
    ? "https://testnet.asteroidx.io/main?invitation="
    : "https://asteroidx.io/main?invitation=";

  const {
    rewardPoints,
    contributions,
    totalContributions,
    currentPage,
    totalPages,
    changePage,
    isLoading,
  } = useTokenInfo(shortCode);

  useLayoutEffect(() => {
    if (address) {
      getInvitationCodeFromOwnServer(address).then((shortCode) =>
        setShortCode(shortCode),
      );
    } else {
      setShortCode("");
      onClose();
    }
  }, [address]);

  if (!isOpen) return null;

  return (
    <motion.div
      className={styles.modalOverlay}
      variants={fadeIn(0.3)}
      initial="hidden"
      animate="visible"
      exit="hidden"
    >
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <h2>Share & Expand - Grow with Us</h2>
          <button className={styles.closeButton} onClick={onClose}>
            ✕
          </button>
        </div>

        <div className={styles.modalBody}>
          <p className={styles.introText}>
            Thanks for being part of Asteroid X.{" "}
            <span className={styles.highlight}>
              Invite others to grow the Community!
            </span>{" "}
          </p>

          <div className={styles.rewardSection}>
            <div className={styles.sectionHeader}>Invite friends</div>
            <p className={styles.sectionDescription}>
              For every contribution made by your invitees, you will receive{" "}
              <span className={styles.tokenText}>5%</span> of their HSK amount
              as a direct reward.
            </p>
            <div className={styles.inviteSection}>
              <div className={styles.inviteLink}>{websiteUrl + shortCode}</div>
              <button
                className={styles.actionButton}
                onClick={() => {
                  navigator.clipboard
                    .writeText(websiteUrl + shortCode)
                    .then(() => {
                      showSuccess("Invitation link copied to clipboard");
                    })
                    .catch((err) => {
                      showSuccess(`Failed to copy text: ${err}`);
                    });
                }}
              >
                COPY INVITATION
              </button>
            </div>
          </div>

          <div className={styles.rewardSection}>
            <div className={styles.sectionHeader}>Your current rewards</div>
            <div className={styles.inviteSection}>
              <div className={styles.inviteLink}>Reward {rewardPoints} HSK</div>
            </div>

            {/* Contribution Details*/}
            {contributions.length > 0 && (
              <div className={styles.contributionsTable}>
                <div className={styles.tableHeader}>
                  <h3>Contribution Details</h3>
                  <div className={styles.totalInfo}>
                    Total contributions: {totalContributions}
                  </div>
                </div>
                <table>
                  <thead>
                    <tr>
                      <th>Address</th>
                      <th className={styles.amountColumn}>Amount (HSK)</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {contributions.map((record, index) => {
                      const value = parseFloat(record.amount) * 0.05;
                      const truncated = Math.floor(value * 1000) / 1000;
                      const rewardAmount = truncated.toFixed(3);

                      return (
                        <tr key={index}>
                          <td>{record.contributor}</td>
                          <td className={styles.amountValue}>{rewardAmount}</td>
                          <td>{formatDate(record.timestamp)}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className={styles.pagination}>
                    <button
                      onClick={() => changePage(currentPage - 1)}
                      disabled={currentPage === 0 || isLoading}
                      className={styles.paginationButton}
                    >
                      Previous
                    </button>
                    <span className={styles.pageInfo}>
                      Page {currentPage + 1} of {totalPages}
                    </span>
                    <button
                      onClick={() => changePage(currentPage + 1)}
                      disabled={currentPage >= totalPages - 1 || isLoading}
                      className={styles.paginationButton}
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default RewardsModal;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100vh;
  @include row-center;
  overflow: hidden;
  // border: 3px solid yellow;
  // padding: $padding-md;
}

.canvas {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.backgroundPlanet {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 30%;
  // border: 3px solid green;
}

.content {
  position: absolute;
  margin-bottom: $margin-md;
  width: 100%;
  height: 85%;
  z-index: 10;
  overflow-y: hidden;
  &[data-selection="1"] {
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  // border: 3px solid green;
}
.topBar {
  width: 100%;
  position: fixed;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  top: 0;
  margin: $margin-sm;
  // background-color: #f0f0f0;
  padding: 3px;
  z-index: 1;
}

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100vh;
  @include row-center;
  overflow: hidden;
  // border: 3px solid yellow;
  // padding: $padding-md;
}

.canvas {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.backgroundPlanet {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 30%;
  // border: 3px solid green;
}

.content {
  position: absolute;
  margin-bottom: $margin-md;
  width: 100%;
  height: 85%;
  z-index: 10;
  overflow-y: hidden;
  &[data-selection="1"] {
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  // border: 3px solid green;
}
.topBar {
  width: 100%;
  position: fixed;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  top: 0;
  margin: $margin-sm;
  // background-color: #f0f0f0;
  padding: 3px;
  z-index: 1;
}

.fullscreenPointSystem {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: black;
  z-index: 9999;
  overflow: hidden;
}

.closeButton {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(0, 255, 255, 0.5);
  border-radius: 50%;
  color: white;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.8);
  }
}

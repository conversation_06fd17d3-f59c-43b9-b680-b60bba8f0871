@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  height: 100vh;
  @include row-center;
  overflow: hidden;
  // border: 3px solid yellow;
  // padding: $padding-md;
}

.canvas {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.backgroundPlanet {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 30%;
  // border: 3px solid green;
}

.content {
  position: absolute;
  margin-bottom: $margin-md;
  width: 100%;
  height: 85%;
  z-index: 10;
  overflow-y: hidden;
  &[data-selection="1"] {
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  // border: 3px solid green;
}
.topBar {
  width: 100%;
  position: fixed;
  border: $border-width-xs solid $color-primary-transparent-contrast;
  top: 0;
  margin: $margin-sm;
  // background-color: #f0f0f0;
  padding: 3px;
  z-index: 1;
}

.pointSystemOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.95);
}

.closeButton {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 45px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;

  img {
    width: 20px;
    height: 20px;
  }

  &:hover {
    background: rgba(0, 255, 255, 0.2);
  }
}

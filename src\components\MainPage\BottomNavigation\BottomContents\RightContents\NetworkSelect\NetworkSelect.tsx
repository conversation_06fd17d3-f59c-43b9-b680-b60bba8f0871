import * as Popover from "@radix-ui/react-popover";
import styles from "./NetworkSelect.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { polygonMumbai, bscTestnet, polygon, bsc } from "wagmi/chains";
import { useNetwork, useSwitchNetwork } from "wagmi";
import { useSnapshot } from "valtio";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { BaseError } from "viem";
import { useEffect } from "react";

type NetworkSelectProps = {
  children: React.ReactNode;
};

const NetworkSelect = ({ children }: NetworkSelectProps) => {
  const { chain } = useNetwork();
  const { chains, error, isLoading, pendingChainId, switchNetwork } =
    useSwitchNetwork({
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
      },
      onSuccess: () => {
        showSuccess("Switched to supported network");
      },
    });

  useEffect(() => {
    if (chain?.unsupported) {
      showError("UNSUPPORTED NETWORK");
    }
  }, [chain]);
  return (
    <Popover.Root>
      <Popover.Trigger asChild>{children}</Popover.Trigger>
      <Popover.Portal>
        <Popover.Content className={styles.PopoverContent} sideOffset={5}>
          <h1>supported network</h1>
          {chains.map((x) => (
            <div
              key={x.id}
              className={styles.networkSelect}
              onClick={() => switchNetwork?.(x.id)}
            >
              <img
                src={networkConfigs[x.id].icon}
                width={24}
                height={24}
                alt="currency icon"
              />
              <h2>{x.name}</h2>
            </div>
          ))}
          <Popover.Close className={styles.PopoverClose} aria-label="Close">
            <img src={crossIcon.src} alt="close" />
          </Popover.Close>
          <Popover.Arrow className={styles.PopoverArrow} width={8} height={8} />
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export default NetworkSelect;

import { use<PERSON>elper } from "@react-three/drei";
import { Ref, useRef } from "react";
import { DirectionalLight, DirectionalLightHelper } from "three";

const Lighting = () => {
  const directionalLightRef = useRef();
  // @ts-ignore
  //   useHelper(directionalLightRef, DirectionalLightHelper, 1, "red");
  return (
    <>
      <ambientLight intensity={1} />
      <directionalLight
        // @ts-ignore
        // ref={directionalLightRef}
        // color={0x00c4d0}
        color={0xffffff}
        position={[0, 10, 10]}
        intensity={0.8}
      />
      {/* <directionalLight color="white" position={[0, 0, 10]} intensity={1} />
      <directionalLight color="white" position={[0, -10, 0]} intensity={1} />
      <directionalLight color="white" position={[0, 10, 0]} intensity={1} />
      <directionalLight color="white" position={[-10, 0, 0]} intensity={1} />
      <directionalLight color="white" position={[10, 0, 0]} intensity={1} /> */}
    </>
  );
};

export default Lighting;

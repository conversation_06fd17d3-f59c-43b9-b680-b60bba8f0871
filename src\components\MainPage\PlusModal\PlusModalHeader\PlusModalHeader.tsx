import styles from "./PlusModalHeader.module.scss";
import variables from "@/styles/variables.module.scss";
import lineIconLight from "@/assets/icons/plusModal/lineIconLight.png";
import lineIconMedium from "@/assets/icons/plusModal/lineIconMedium.png";
import lineIconDark from "@/assets/icons/plusModal/lineIconDark.png";
import { useState } from "react";
import { plusModalStore } from "@/stores/plusModal";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";

const PlusModalHeader = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const isSpecialEvent =
    minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0].shortName === "hskreward";

  // const [selectedTitle, setSelectedTitle] = useState(headerDetails[0].title);
  const plusModalSnapshot = useSnapshot(plusModalStore);

  // console.log(isTitleSelected);
  return (
    <div className={styles.container}>
      <h1 className={styles.overview}>overview</h1>
      <div className={styles.mineralInfo}>
        {/* <img src={lineIconLight.src} alt="light line icon" />
        <img src={lineIconMedium.src} alt="medium line icon" />
        <img src={lineIconDark.src} alt="dark line icon" /> */}
        {headerDetails.map((details, index) => (
          <div
            key={index}
            style={
              plusModalSnapshot.selectedTitle === details.title
                ? {
                    textShadow: `0px 0px 15px ${variables.colorPrimary}`,
                    color: variables.colorPrimary,
                    opacity: 1,
                  }
                : undefined
            }
            onClick={() => plusModalSnapshot.setSelectedTitle(details.title)}
          >
            {isSpecialEvent ? (
              <>
                <h1>{specialEventHeaderDetails[index].title}</h1>
                <h2>{specialEventHeaderDetails[index].subtitle}</h2>
              </>
            ) : (
              <>
                <h1>{details.title}</h1>
                <h2>{details.subtitle}</h2>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

const specialEventHeaderDetails = [
  { title: "Event Overview", subtitle: "details about this event" },
  { title: "How to Participate", subtitle: "details about this participation" },
  { title: "Why Join?", subtitle: "details about this joining" },
];

export const headerDetails = [
  { title: "mineral information", subtitle: "details about this mine" },
  { title: "the company", subtitle: "details about this company" },
  { title: "investor zone", subtitle: "details about investment" },
];

export default PlusModalHeader;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin subText {
  font-size: $font-size-sm;
  color: $color-primary-transparent-contrast;
  font-weight: $font-weight-light;
}

@mixin highlight {
  font-size: $font-size-sm;
  color: $color-primary;
  font-weight: $font-weight-semibold;
}

.container {
  position: absolute;
  width: 95%;
  height: 75%;
  overflow: scroll;
  @include col-center;
  justify-content: flex-start;
  align-items: flex-start;
  gap: $spacing-sm;
  padding-bottom: $padding-lg;
  // margin-top: $spacing-sm;
  // padding: 0 $padding-md;
  // border: 1px solid yellow;

  &::-webkit-scrollbar {
    // width: 8px;
    // background-color: $color-black-transparent;
    display: none;
  }

  /* 为滚动条轨道设置样式 */
  // &::-webkit-scrollbar-track {
  //   border-left: 1px solid $color-primary;
  // }

  /* 为滚动条滑块设置样式 */
  // &::-webkit-scrollbar-thumb {
  // background-color: $color-primary;
  // margin-left: 10px;
  // border-left: 2px solid black;
  // border-right: 2px solid black;
  // }

  .titleContainer {
    width: 100%;
    @include row-center;
    justify-content: flex-start;
    padding: $padding-sm;
    gap: $spacing-sm;
    // border: 1px solid yellow;

    .subtitle {
      @include row-center;
      justify-content: flex-start;
      gap: $spacing-sm;

      .mineral {
        background: rgba(255, 255, 255, 0.07);
        padding: 0 $padding-xs;
        border-radius: $border-radius-sm;
        font-size: $font-size-2xs;
        color: yellow;
        // color: $color-primary-transparent-contrast;89
        font-weight: $font-weight-medium;
      }

      .location {
        font-size: $font-size-2xs;
        font-weight: $font-weight-bold;
        color: $color-primary;
      }
    }

    .title {
      font-size: $font-size-md;
      font-weight: $font-weight-semibold;
      color: $color-primary;
    }

    // .rightImage {
    //   margin-left: 30px;
    // }
  }

  .imgContainer {
    width: 100%;
    // height: 100%;
    @include row-center;
    // justify-content: flex-start;
    padding: $padding-sm;
    gap: $spacing-sm;

    .imageFrame {
      width: 100%;
      height: 5rem;
      position: relative;
      background: $color-primary-transparent;
      backdrop-filter: blur(20px);
      // padding: px;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%
      );
      // border: 1px solid yellow;

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        clip-path: polygon(
          0 0,
          calc(100% - 32px) 0,
          100% 32px,
          100% 100%,
          0 100%,
          0 0,
          1px 1px,
          1px calc(100% - 1px),
          calc(100% - 1px) calc(100% - 1px),
          calc(100% - 1px) calc(32px + 0.41px),
          calc(100% - 32px - 0.41px) 1px,
          1px 1px
        );
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .information {
    width: 100%;
    // border-bottom: $border-width-2xs solid $color-primary-transparent;
    padding-bottom: $spacing-sm;

    .logoWrapper {
      // width: 100%;
      @include row-between;

      .title {
        font-size: $font-size-xs;
        color: $color-primary-transparent-contrast;
        font-weight: $font-weight-light;
        text-transform: uppercase;
      }

      .subtitle {
        font-size: $font-size-xs;
        color: $color-primary;
        font-weight: $font-weight-semibold;
      }
    }
  }

  .purchaseContainer {
    width: 100%;
    // height: 100%;
    // border: 1px solid yellow;
    // flex: 4;
    // @include col-between;
    padding: $padding-sm;
    gap: $spacing-sm;
    // z-index: $z-index-2;
    .header {
      // line-height: 108%;
      @include row-between;
      // border: 1px solid yellow;
      // align-items: flex-start;

      .labelWrapper {
        margin-left: $margin-md;
      }

      .title {
        font-size: $font-size-md;
        color: $color-primary;
        font-weight: $font-weight-semibold;
      }
      .subtitle {
        font-size: $font-size-xs;
        color: $color-primary-transparent-contrast;
        font-weight: $font-weight-light;
      }
    }

    .mineDetails {
      // height: 40px;
      width: 100%;
      margin-top: $margin-md;
      border: $border-width-xs solid $color-primary;
      border-radius: $border-radius-sm;
      background: $color-primary-transparent;
      backdrop-filter: blur(20px);
      padding: 0 $padding-sm;
      @include row-between;

      .mineRegion {
        font-size: $font-size-sm;
        color: $color-primary;
        font-weight: $font-weight-medium;
        .mineName {
          font-size: $font-size-md;
          color: white;
        }
      }
      .mineNumber {
        font-size: $font-size-sm;
        color: $color-primary-transparent-contrast;
        font-weight: $font-weight-light;
      }
    }
  }

  .priceContainer {
    width: 100%;
    // height: 100%;
    // border: 1px solid yellow;
    // flex: 4;
    // @include col-between;
    padding: $padding-sm;
    gap: $spacing-sm;

    .title {
      font-size: $font-size-md;
      color: $color-primary;
      font-weight: $font-weight-normal;
    }

    .subtitle {
      margin-left: $margin-lg;
      font-size: $font-size-sm;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}
.loginDetails {
  width: 100%;
  // height: 180px;
  // border: 1px solid yellow;
  @include col-between;
  align-items: flex-start;
  // padding-bottom: $margin-md;
  gap: $spacing-xs;

  .detailsWrapper {
    width: 100%;
    @include row-between;
    .walletAddress {
      @include subText;
    }

    .blockchainNetwork {
      @include subText;
    }

    span {
      @include highlight();
      color: white;
      span {
        font-size: $font-size-xs;
        color: $color-warning;

        span {
          font-size: $font-size-xs;
          font-weight: $font-weight-bold;
          color: $color-danger;
          margin-left: $margin-md;
          cursor: pointer;
          text-transform: none;
        }
      }
    }

    div {
      // border: 1px solid $color-primary;
      // background: $color-black-transparent;
      // padding: $padding-xs;
      cursor: pointer;
      font-size: $font-size-xs;
      font-weight: $font-weight-bold;
      color: $color-danger;
    }
  }
  .loginButton {
    @include row-center;
    width: 100%;
    text-align: center;
    font-size: $font-size-sm;
    font-family: $font-family-poppins;
    // color: $color-primary;
    color: $color-warning;
    text-transform: uppercase;
    font-weight: $font-weight-semibold;
    border: $border-width-xs solid $color-primary-contrast;
    padding: $padding-sm 0;
    margin: $margin-sm 0;
    background: $color-primary-transparent;
    backdrop-filter: blur(5px);
    border-radius: $border-radius-sm;
    cursor: pointer;

    span {
      color: $color-primary-transparent-contrast;
    }

    &:hover {
      border: $border-width-xs solid $color-primary;
    }
  }
}

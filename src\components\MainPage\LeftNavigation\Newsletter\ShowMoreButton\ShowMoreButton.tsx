import { buttonEffect, rotation, slideRight } from "@/animations/animations";
import styles from "./ShowMoreButton.module.scss";
import { motion } from "framer-motion";
import { Dispatch, SetStateAction, useState } from "react";
import leftNavigationButtonFrame from "@/assets/icons/leftNavigation/frame.png";
import leftNavigationButtonArrow from "@/assets/icons/leftNavigation/arrow.png";

interface ShowMoreButtonProps {
  delay?: number;
  isShowMoreButtonClicked: boolean;
  setIsShowMoreButtonClicked: Dispatch<SetStateAction<boolean>>;
}

const ShowMoreButton = ({
  delay = 2,
  isShowMoreButtonClicked,
  setIsShowMoreButtonClicked,
}: ShowMoreButtonProps) => {
  return (
    <div className={styles.container}>
      <motion.div
        className={styles.buttonWrapper}
        whileHover={buttonEffect.hover}
        whileTap={buttonEffect.tap}
        onClick={() => setIsShowMoreButtonClicked(!isShowMoreButtonClicked)}
      >
        <img
          style={{ transform: "rotate(90deg)" }}
          src={leftNavigationButtonFrame.src}
          alt="left navigation button frame"
        />
        <div className={styles.buttonArrow}>
          {isShowMoreButtonClicked ? (
            <motion.img
              variants={rotation(0, -270)}
              initial="hidden"
              animate="visible"
              src={leftNavigationButtonArrow.src}
              alt="left navigation button arrow"
            />
          ) : (
            <motion.img
              variants={rotation(0, -90)}
              initial="hidden"
              animate="visible"
              src={leftNavigationButtonArrow.src}
              alt="left navigation button arrow"
            />
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ShowMoreButton;

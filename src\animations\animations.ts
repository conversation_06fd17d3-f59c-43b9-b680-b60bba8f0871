export const scaleUp = (duration = 2, delay = 0, scale = 1) => {
  return {
    hidden: {
      scale: 0,
    },
    visible: {
      scale,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const expandCard = (
  height: string | number,
  top = "50%",
  left = "50%",
  duration = 2,
  delay = 0,
) => {
  return {
    hidden: {},
    visible: {
      height,
      top,
      left,
      cursor: "default",
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const fadeIn = (duration = 2, delay = 0, opacity = 1) => {
  return {
    hidden: {
      opacity: 0,
    },
    visible: {
      opacity,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const bottomUpFadeIn = (duration = 2, delay = 0, y = 100) => {
  return {
    hidden: {
      opacity: 0,
      y,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const bottomUp = (duration = 2, delay = 0, y = 100) => {
  return {
    hidden: {
      y,
    },
    visible: {
      y: 0,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const topDown = (duration = 2, delay = 0, y = -100) => {
  return {
    hidden: {
      y,
    },
    visible: {
      y: 0,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const slideIn = (
  initialX = -100,
  finalX = 0,
  duration = 2,
  delay = 0,
) => {
  return {
    hidden: {
      x: initialX,
    },
    visible: {
      x: finalX,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const slideRight = (duration = 2, delay = 0, x = -100) => {
  return {
    hidden: {
      x,
    },
    visible: {
      x: 0,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const slideLeft = (duration = 2, delay = 0, x = 100) => {
  return {
    hidden: {
      x,
    },
    visible: {
      x: 0,
      transition: {
        duration,
        delay,
      },
    },
  };
};

export const buttonEffect = {
  hover: {
    scale: 1.1,
  },
  tap: {
    scale: 0.9,
  },
};

export const rotation = (
  initalRotation = 0,
  finalRotation = 180,
  duration = 0.2,
  delay = 0,
) => {
  return {
    hidden: {
      rotate: initalRotation,
    },
    visible: {
      rotate: finalRotation,
      transition: {
        duration,
        delay,
      },
    },
  };
};

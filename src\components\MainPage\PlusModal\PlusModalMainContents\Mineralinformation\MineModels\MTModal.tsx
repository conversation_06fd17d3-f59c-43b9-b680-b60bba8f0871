import { useGLTF, Center, OrbitControls, Html } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";
import { GLTF } from "three-stdlib";
import * as THREE from "three";
import { Suspense, memo } from "react";
import { useModelLoader } from "@/hooks/useModelLoader";

type GLTFResult = GLTF & {
  nodes: {
    OpenPitMine_Material006_0: THREE.Mesh;
  };
  materials: {
    ["Material.006"]: THREE.MeshStandardMaterial;
  };
};

const MTModal = () => {
  const Loader = () => <Html center>loading modal</Html>;

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Canvas
        shadows
        camera={{ position: [0, 0, 170], fov: 25 }}
        gl={{ preserveDrawingBuffer: true }}
      >
        <ambientLight intensity={1} color="white" />
        <directionalLight color="white" position={[0, 0, -20]} intensity={1} />
        <directionalLight color="white" position={[0, 0, 20]} intensity={1} />
        <directionalLight color="white" position={[20, 0, 0]} intensity={1} />
        <directionalLight color="white" position={[-20, 0, 0]} intensity={1} />
        <directionalLight color="white" position={[0, 20, 0]} intensity={1} />
        <directionalLight color="white" position={[0, -20, 0]} intensity={1} />
        <Center>
          <Suspense fallback={<Loader />}>
            <MT />
          </Suspense>
        </Center>
        <OrbitControls
          autoRotate
          autoRotateSpeed={0.5}
          makeDefault
          enableZoom={false}
          minPolarAngle={Math.PI / 2}
          maxPolarAngle={Math.PI / 2}
          // minAzimuthAngle={-Math.PI / 2.1}
          // maxAzimuthAngle={Math.PI / 2.3}
        />
      </Canvas>
    </div>
  );
};

function MT(props: JSX.IntrinsicElements["group"]) {
  const { model } = useModelLoader("MT");
  const { nodes, materials } = model;
  return (
    <group {...props} dispose={null}>
      <group rotation={[-Math.PI / 2, 0, 0]} scale={0.203}>
        <group rotation={[Math.PI / 2, 0, 0]}>
          <mesh
            castShadow
            receiveShadow
            geometry={nodes.OpenPitMine_Material006_0.geometry}
            material={materials["Material.006"]}
            position={[480.743, 0.406, -183.956]}
            rotation={[-Math.PI / 2, 0, 0]}
            scale={100}
          />
        </group>
      </group>
    </group>
  );
}

export default memo(MTModal);

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);
$hightlightBackground: rgba($color-primary, 0.3);

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  height: 100%;
  overflow: hidden;
  // border: 1px solid yellow;

  & * {
    scrollbar-width: none; /* Firefox */
  }
  
  & *::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
    width: 0;
    height: 0;
  }

  .modalFrame {
    margin-top: 6rem;
    width: 1400px;
    // max-height: 750px;
    position: relative;
    background: $color-black-transparent-dark;
    clip-path: polygon(
      0 0,
      calc(100% - 80px) 0,
      100% 80px,
      100% calc(100% - 48px),
      calc(100% - 48px) 100%,
      48px 100%,
      0 calc(100% - 48px)
    );

    .closeButton {
      position: absolute;
      width: 250px;
      height: 50px;
      top: 32px;
      right: -68px;
      cursor: pointer;
      border: $border-width-xs solid $color-primary;
      transform: rotate(45deg);

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(45deg);
      }

      &:hover {
        background: $color-primary-contrast;
      }
    }

    .contentWrapper {
      width: 100%;
      height: 100%;
      padding: 1.5rem;
      @include col-center;
      gap: 1rem;

      .header {
        width: 100%;
        color: $color-primary;
      }

      .scrollArea {
        width: 100%;
        height: 100%;
        overflow: scroll;
        scroll-behavior: smooth;
        // border: 1px solid yellow;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary;
      clip-path: polygon(
        0 0,
        calc(100% - 81px) 0,
        100% 81px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px),
        0 0,
        1px 1px,
        1px calc(100% - 48px - 0.41px),
        calc(48px + 0.41px) calc(100% - 1px),
        calc(100% - 48px - 0.41px) calc(100% - 1px),
        calc(100% - 1px) calc(100% - 48px - 0.41px),
        calc(100% - 1px) calc(48px + 0.41px),
        calc(100% - 48px - 0.41px) 1px,
        1px 1px
      );
    }
  }
}

.contentFrame {
  padding: 1rem 1rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  // height: 100%;
  position: relative;
  background: $color-black-transparent-dark;
  clip-path: polygon(
    0 0,
    calc(100% - 80px) 0,
    100% 80px,
    100% calc(100% - 48px),
    calc(100% - 48px) 100%,
    48px 100%,
    0 calc(100% - 48px)
  );

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    clip-path: polygon(
      0 0,
      calc(100% - 81px) 0,
      100% 81px,
      100% calc(100% - 48px),
      calc(100% - 48px) 100%,
      48px 100%,
      0 calc(100% - 48px),
      0 0,
      1px 1px,
      1px calc(100% - 48px - 0.41px),
      calc(48px + 0.41px) calc(100% - 1px),
      calc(100% - 48px - 0.41px) calc(100% - 1px),
      calc(100% - 1px) calc(100% - 48px - 0.41px),
      calc(100% - 1px) calc(48px + 0.41px),
      calc(100% - 48px - 0.41px) 1px,
      1px 1px
    );
  }
  .derlordInfo {
    h3 {
      color: #00e0ff;
      font-size: 24px;
    }

    span {
      display: block;
      color: #00e0ff;
    }

    p {
      color: #00e0ff;
      font-size: 14px;
    }
  }

  .itemCount,
  .priceSection,
  .durationSection {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;

    h3,
    h4 {
      color: $color-primary;
      font-size: 1rem;
    }

    h4 {
      opacity: 0.8;
    }
  }

  .inputContainer,
  .priceInput,
  .durationControls {
    display: flex;
    gap: 1rem;
    width: 100%;

    input,
    select {
      border-radius: 0.5rem;
      flex: 1;
      background: rgba(0, 224, 255, 0.2);
      border: 1px solid $color-primary-contrast;
      color: #00e0ff;
      padding: 0.75rem 1rem;
      font-size: 1rem;
      outline: none;

      &::placeholder {
        color: $color-primary;
        opacity: 0.5;
      }
    }

    .available,
    .eth {
      display: flex;
      align-items: center;
      color: $color-primary;
      padding: 0 1rem;
    }
  }

  .durationControls {
    select {
      max-width: 200px;
    }

    input[type="datetime-local"] {
      flex: 2;
    }
  }

  .summary {
    display: flex;
    flex-direction: column;
    // gap: 0.2rem;

    .summaryRow {
      display: flex;
      justify-content: space-between;
      color: $color-primary-contrast;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba($color-primary, 0.2);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .completeButton {
    border-radius: 0.5rem;
    width: 60%;
    padding: 1rem;
    background: rgba(0, 224, 255, 0.2);
    border: 1px solid $color-primary-contrast;
    color: $color-primary;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba($color-primary, 0.2);
    }

    &:disabled {
      background: $color-black-transparent-dark;
      color: $color-primary-contrast;
      border: 1px solid $color-primary-contrast;
      cursor: not-allowed;
    }
  }
}

.listingsTable {
  position: relative;
  width: 99%;
  border-radius: 8px;
  overflow: scroll;
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tableHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 12px 16px;
  font-weight: bold;
  text-align: left;
  gap: 1rem;
  color: grey;
  margin-bottom: 1rem;
}

.tableBodyContainer {
  height: 320px; /* Set a fixed height for 5 rows approximately */
  overflow-y: scroll;

  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}

.tableBody {
  width: 100%;
  color: $color-primary;
}

.tableRow {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 1rem;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  &.ownListing {
    background-color: $hightlightBackground;

    &:hover {
      background-color: rgba($color-primary, 0.4);
    }
  }
}

.buyButton {
  background-color: $color-black-transparent-dark;
  border: 1px solid $color-primary;
  width: 100%;
  color: $color-primary;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: $hightlightBackground;
  }

  &:disabled {
    background: $color-black-transparent-dark;
    color: $color-primary-contrast;
    border: 1px solid $color-primary-contrast;
    cursor: not-allowed;
  }

  &.cancelButton {
    background-color: rgba(255, 0, 0, 0.6);
    width: 100%;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 0, 0, 0.8);
    }

    &:disabled {
      background: $color-black-transparent-dark;
      color: $color-primary-contrast;
      border: 1px solid $color-primary-contrast;
      cursor: not-allowed;
    }
  }
}

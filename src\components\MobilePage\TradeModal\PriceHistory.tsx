import PriceHistoryGraph from "./PriceHistoryGraph";
import styles from "./CurrentPrice.module.scss";
import { useState } from "react";

const PriceHistory = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded((prevState) => !prevState);
  };
  return (
    <div className={styles.container}>
      <div className={styles.priceHistory}>
      <div
          className={`${styles.mainHeader} ${
            isExpanded ? styles.expanded : styles.collapsed
          }`}
          onClick={handleToggle}
        >
          <span className={styles.glowText}>PRICE HISTORY</span>
          <span className={styles.buttonHighlight}></span>
        </div>
        {isExpanded && (

          <div className={styles.graph}>
            <PriceHistoryGraph />
          </div>
        )}
      </div>
    </div>
  );
};

export default PriceHistory;

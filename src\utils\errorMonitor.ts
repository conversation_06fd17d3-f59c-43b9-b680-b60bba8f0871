/**
 * 全局错误监听器
 * 用于捕获所有JavaScript错误，包括那些被库内部处理的错误
 */

// 保存原始的console方法
const originalConsoleError = console.error;
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;

// 错误日志存储
interface ErrorLog {
  timestamp: string;
  type: "error" | "log" | "warn";
  message: string;
  details?: any;
}

const errorLogs: ErrorLog[] = [];
const MAX_LOGS = 100; // 限制日志数量

// 重写console.error
console.error = function (...args: any[]) {
  // 调用原始方法
  originalConsoleError.apply(console, args);

  // 记录错误
  const timestamp = new Date().toISOString();
  const message = args[0]?.toString() || "";

  // 特别关注合约错误签名
  const signatureMatch = message.match(/signature:\s+(0x[0-9a-f]{8})/i);
  if (signatureMatch && signatureMatch[1]) {
    const signature = signatureMatch[1];
    originalConsoleLog.call(
      console,
      `[ErrorMonitor] 检测到合约错误签名: ${signature}`,
    );
  }

  // 存储错误日志
  errorLogs.unshift({
    timestamp,
    type: "error",
    message,
    details: args.length > 1 ? args.slice(1) : undefined,
  });

  // 限制日志数量
  if (errorLogs.length > MAX_LOGS) {
    errorLogs.pop();
  }
};

// 同样可以监控其他console方法
console.warn = function (...args: any[]) {
  originalConsoleWarn.apply(console, args);

  const timestamp = new Date().toISOString();
  errorLogs.unshift({
    timestamp,
    type: "warn",
    message: args[0]?.toString() || "",
    details: args.length > 1 ? args.slice(1) : undefined,
  });

  if (errorLogs.length > MAX_LOGS) {
    errorLogs.pop();
  }
};

// 添加全局未捕获异常监听器
if (typeof window !== "undefined") {
  window.addEventListener("error", (event) => {
    originalConsoleError.call(
      console,
      "[ErrorMonitor] 未捕获的全局错误:",
      event.error,
    );

    const timestamp = new Date().toISOString();
    errorLogs.unshift({
      timestamp,
      type: "error",
      message: event.error?.message || event.message || "Unknown Error",
      details: event.error,
    });

    if (errorLogs.length > MAX_LOGS) {
      errorLogs.pop();
    }
  });

  window.addEventListener("unhandledrejection", (event) => {
    originalConsoleError.call(
      console,
      "[ErrorMonitor] 未处理的Promise拒绝:",
      event.reason,
    );

    const timestamp = new Date().toISOString();
    errorLogs.unshift({
      timestamp,
      type: "error",
      message: event.reason?.message || "Unhandled Promise Rejection",
      details: event.reason,
    });

    if (errorLogs.length > MAX_LOGS) {
      errorLogs.pop();
    }
  });
}

// 导出获取日志的方法
export const getErrorLogs = () => {
  return [...errorLogs]; // 返回副本
};

// 导出清除日志的方法
export const clearErrorLogs = () => {
  errorLogs.length = 0;
};

// 提供一个UI显示组件
export const ErrorMonitorComponent = () => {
  if (typeof document === "undefined") return null;

  // 创建一个浮动面板
  const containerStyle = `
    position: fixed;
    bottom: 0;
    right: 0;
    max-height: 300px;
    width: 400px;
    background: rgba(0,0,0,0.8);
    color: white;
    z-index: 9999;
    overflow: auto;
    font-family: monospace;
    font-size: 12px;
    padding: 10px;
  `;

  const container = document.createElement("div");
  container.style.cssText = containerStyle;
  container.id = "error-monitor-container";

  // 将当前日志渲染到面板上
  const updateContent = () => {
    const logs = getErrorLogs();
    container.innerHTML = `
      <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
        <h3 style="margin: 0; color: orange;">错误监控器 (${logs.length})</h3>
        <button id="clear-logs" style="cursor: pointer;">清除</button>
        <button id="close-monitor" style="cursor: pointer; margin-left: 5px;">关闭</button>
      </div>
      <div>
        ${logs
          .map(
            (log) => `
          <div style="margin-bottom: 8px; border-bottom: 1px solid #333; padding-bottom: 5px;">
            <div style="color: #888;">${log.timestamp}</div>
            <div style="color: ${
              log.type === "error"
                ? "red"
                : log.type === "warn"
                ? "yellow"
                : "white"
            };">
              ${log.message}
            </div>
            ${
              log.details
                ? `<pre style="max-height: 100px; overflow: auto;">${JSON.stringify(
                    log.details,
                    null,
                    2,
                  )}</pre>`
                : ""
            }
          </div>
        `,
          )
          .join("")}
      </div>
    `;

    // 添加事件处理
    document.getElementById("clear-logs")?.addEventListener("click", () => {
      clearErrorLogs();
      updateContent();
    });

    document.getElementById("close-monitor")?.addEventListener("click", () => {
      container.remove();
    });
  };

  // 添加到DOM
  document.body.appendChild(container);
  updateContent();

  // 定期更新
  const interval = setInterval(updateContent, 2000);

  // 返回清理函数
  return () => {
    clearInterval(interval);
    container.remove();
  };
};

export default {
  getErrorLogs,
  clearErrorLogs,
  ErrorMonitorComponent,
};

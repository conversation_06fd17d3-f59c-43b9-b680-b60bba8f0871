import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { minesDetails } from "@/constants/mineDetails";
import { networkConfigs } from "@/constants/networkConfigs";
import { showError, showSuccess } from "@/lib/notification";
import { mineCardStore } from "@/stores/mineCard";
import { useEffect, useLayoutEffect, useState } from "react";
import { useSnapshot } from "valtio";
import { BaseError, isAddress } from "viem";
import { parseTokenAmount } from "@/utils/tokenPriceFormatter";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
} from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { extractErrorType } from "@/utils/errorHandling";
import useSupportedPaymentTokens from "./useSupportedPaymentTokens";
import useApproveUsdt from "./useApproveUsdt";

const useCreateListing = (onClose?: () => void) => {
  const [marketplaceAddress, setMarketplaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [avaliableNfts, setAvaliableNfts] = useState(0n);
  const [errorMessage, setErrorMessage] = useState("");
  const [isCreatingListing, setIsCreatingListing] = useState(false);
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [numberOfItem, setNumberOfItem] = useState(0);
  const [amount, setAmount] = useState(0.01);
  const [expirationDateUnixTime, setExpirationDateUnixTime] = useState(0);
  const [totalPaymentValue, setTotalPaymentValue] = useState(
    numberOfItem * amount,
  );
  const [selectedPaymentToken, setSelectedPaymentToken] =
    useState<`0x${string}`>("0x");

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { getSelectedMineId } = useMineTokenId();
  const { isUsdtSupported, usdtAddress } = useSupportedPaymentTokens();
  const { tokenDecimals } = useApproveUsdt();

  // Set USDT as payment token when support is confirmed
  useEffect(() => {
    if (isUsdtSupported && usdtAddress !== "0x") {
      setSelectedPaymentToken(usdtAddress);
    }
  }, [isUsdtSupported, usdtAddress]);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setAsteroidAddress(networkConfigs[chain.id].asteroidAddress);
      setMarketplaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setAsteroidAddress("0x");
      setMarketplaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine, getSelectedMineId]);

  useEffect(() => {
    setTotalPaymentValue(numberOfItem * amount);
  }, [numberOfItem, amount]);

  useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "balanceOf",
    args: [address ?? "0x", BigInt(asteroidMineNumber)],
    enabled: isConnected && asteroidAddress !== "0x" && address !== undefined,
    watch: true,
    onSuccess: (data) => {
      setAvaliableNfts(data);
    },
  });

  const { config: createListingConfig, error: prepareError } =
    usePrepareContractWrite({
      address: marketplaceAddress,
      abi: asteroidXMarketplaceAbi,
      functionName: "createListing",
      args: [
        BigInt(asteroidMineNumber),
        BigInt(numberOfItem),
        parseTokenAmount(
          amount.toString(),
          selectedPaymentToken,
          tokenDecimals,
          chain?.id,
        ),
        BigInt(expirationDateUnixTime),
        selectedPaymentToken,
      ],
      enabled:
        isConnected &&
        marketplaceAddress !== "0x" &&
        isAddress(selectedPaymentToken) &&
        asteroidMineNumber !== 0 &&
        numberOfItem > 0 &&
        expirationDateUnixTime > 0 &&
        amount > 0,
      onError: (error: any) => {
        if (error.cause?.reason) {
          setErrorMessage(error.cause.reason);
        } else {
          setErrorMessage(error.shortMessage || error.message);
        }
      },
    });

  const { write: createListingWrite, data: createListingData } =
    useContractWrite({
      ...createListingConfig,
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
        setIsCreatingListing(false);
      },
    });

  const { isLoading: isWaitingForCreateListing } = useWaitForTransaction({
    confirmations: 5,
    hash: createListingData?.hash,
    onSuccess: () => {
      showSuccess("Successfully Listed!");
      setIsCreatingListing(false);
      onClose?.();
    },
    onError: (error) => {
      showError(
        extractErrorType(error) ||
          (error as BaseError).shortMessage ||
          error.message,
      );
      setIsCreatingListing(false);
    },
  });

  const createListing = async () => {
    if (
      !isConnected ||
      (chain?.id && !selectedMine.supportedNetwork.includes(chain.id))
    ) {
      showError(`Not supported on ${chain?.name}`);
      return;
    }

    if (!isUsdtSupported) {
      showError("USDT payment is not supported by the marketplace.");
      return;
    }

    if (!isAddress(selectedPaymentToken)) {
      showError("Invalid payment token address.");
      return;
    }

    if (!createListingWrite) {
      if (prepareError) {
        showError(extractErrorType(prepareError));
      }
      return;
    }

    setIsCreatingListing(true);
    try {
      createListingWrite?.();
    } catch (error) {
      setIsCreatingListing(false);
      showError(extractErrorType(error) || "Failed to create listing");
    }
  };

  return {
    avaliableNfts,
    setAmount,
    setExpirationDateUnixTime,
    setNumberOfItem,
    setSelectedPaymentToken,
    selectedPaymentToken,
    numberOfItem,
    amount,
    totalPaymentValue,
    createListing,
    isCreatingListing,
    isWaitingForCreateListing,
  };
};

export default useCreateListing;

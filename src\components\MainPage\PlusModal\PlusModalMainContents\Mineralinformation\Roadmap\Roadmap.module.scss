@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

// Mobile breakpoint
$mobile-breakpoint: 768px;

.container {
  width: 100%;
  padding: $padding-md 0;
  margin-top: $margin-md;

  @media screen and (max-width: $mobile-breakpoint) {
    padding: $padding-sm 0;
    margin-top: $margin-sm;
  }

  .titleWrapper {
    text-align: center;
    text-transform: uppercase;
    margin-bottom: $margin-md;
    line-height: 160%;
    color: $color-primary;

    @media screen and (max-width: $mobile-breakpoint) {
      margin-bottom: $margin-sm;
      line-height: 140%;
    }

    h1 {
      font-weight: $font-weight-semibold;
      font-size: $font-size-4xl;

      @media screen and (max-width: $mobile-breakpoint) {
        font-size: $font-size-2xl;
      }
    }

    h2 {
      font-size: $font-size-xl;
      font-weight: $font-weight-thin;

      @media screen and (max-width: $mobile-breakpoint) {
        font-size: $font-size-lg;
      }
    }
  }

  h4 {
    font-size: $font-size-md;
    color: $color-primary-contrast;
    font-weight: $font-weight-medium;
    text-transform: uppercase;
    @include row-between;

    @media screen and (max-width: $mobile-breakpoint) {
      font-size: $font-size-sm;
      flex-direction: column;
      gap: 8px;
    }

    span {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-bold;

      @media screen and (max-width: $mobile-breakpoint) {
        font-size: $font-size-md;
      }
    }
  }

  p {
    font-size: $font-size-lg;
    color: $color-primary;
    font-weight: $font-weight-semibold;

    @media screen and (max-width: $mobile-breakpoint) {
      font-size: $font-size-md;
    }
  }
}

.dateClassName {
  color: $color-primary;
  text-transform: uppercase;

  @media screen and (max-width: $mobile-breakpoint) {
    font-size: $font-size-sm;
    color: yellow;
  }
}

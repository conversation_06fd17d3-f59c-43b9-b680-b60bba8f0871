@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.wrapper {
  width: 377px;
  height: 500px;
  position: relative;
  background: $color-primary-transparent;
  backdrop-filter: blur(20px);
  padding: $padding-lg;
  clip-path: polygon(0 0, calc(100% - 32px) 0, 100% 32px, 100% 100%, 0 100%);
  // border: 1px solid yellow;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }
}

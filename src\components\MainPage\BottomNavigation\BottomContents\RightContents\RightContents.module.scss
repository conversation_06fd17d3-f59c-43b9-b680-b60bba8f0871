@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

@mixin subText {
  font-size: $font-size-sm;
  color: $color-primary-transparent-contrast;
  font-weight: $font-weight-light;
}

@mixin highlight {
  font-size: $font-size-md;
  color: $color-primary;
  font-weight: $font-weight-bold;
}

.transactionHash {
  border-radius: 0;
  border: 2px solid $color-primary;
  color: $color-primary;
  background: #000000;
  padding: 0.6rem;

  a {
    color: blue;
  }
}

.container {
  width: 100%;
  height: 100%;
  // border: 1px solid yellow;
  flex: 4;
  @include col-between;
  align-items: flex-start;
  padding: 0 $padding-md;

  .headerContent {
    width: 100%;
    @include row-between;
    // line-height: 108%;

    .title {
      font-size: $font-size-md;
      color: $color-primary;
      font-weight: $font-weight-semibold;
      // text-transform: uppercase;
    }
    .subtitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
      // text-transform: uppercase;
    }
  }

  .loginDetails {
    width: 100%;
    // height: 180px;
    // border: 1px solid yellow;
    @include col-between;
    align-items: flex-start;
    // margin-bottom: $margin-md;
    gap: $spacing-xs;

    .detailsWrapper {
      width: 100%;
      @include row-between;
      .walletAddress {
        @include subText;
      }

      .blockchainNetwork {
        @include subText;
      }

      span {
        @include highlight();
        color: white;
        span {
          font-size: $font-size-xs;
          color: $color-warning;

          span {
            font-size: $font-size-xs;
            font-weight: $font-weight-bold;
            color: $color-danger;
            margin-left: $margin-md;
            cursor: pointer;
            text-transform: none;
          }
        }
      }

      div {
        // border: 1px solid $color-primary;
        // background: $color-black-transparent;
        // padding: $padding-xs;
        cursor: pointer;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        color: $color-danger;
      }
    }
    .loginButton {
      @include row-center;
      width: 100%;
      text-align: center;
      font-size: $font-size-sm;
      font-family: $font-family-poppins;
      // color: $color-primary;
      color: $color-warning;
      text-transform: uppercase;
      font-weight: $font-weight-semibold;
      border: $border-width-xs solid $color-primary-contrast;
      padding: $padding-sm 0;
      background: $color-primary-transparent;
      backdrop-filter: blur(5px);
      border-radius: $border-radius-sm;
      cursor: pointer;

      span {
        color: $color-primary-transparent-contrast;
      }

      &:hover {
        border: $border-width-xs solid $color-primary;
      }
    }
  }
}

.selectValue {
  @include row-center;
  gap: $spacing-xs;
}

.selectTrigger {
  outline: none;
  @include row-center;
  gap: $spacing-xs;
  width: 78px;
  height: 24px;
  background: rgba(255, 255, 255, 0.06);
  border: none;
  border-radius: $border-radius-sm;
  color: $color-primary;
  font-size: $font-size-sm;
  &:hover {
    border: $border-width-xs solid $color-primary;
  }
}

.selectContent {
  width: 100px;
  z-index: $z-index-1;
  background: $color-black-transparent;
  backdrop-filter: blur(20px);
  padding: $padding-sm;
  border-radius: $border-radius-sm;
}

.selectItem {
  width: 100%;
  color: $color-primary;
  @include row-between;
  padding: $padding-xs $padding-sm;
  font-size: $font-size-sm;

  img {
    width: 15px;
    height: 15px;
  }

  &:hover {
    outline: none;
    background: $color-primary-contrast;
    border-radius: $border-radius-sm;
  }
}

.networkMenu {
  position: absolute;
  background-color: $color-black-transparent;
  backdrop-filter: blur(20px);
  //border: $border-width-xs solid $color-primary-contrast;
  border-radius: $border-radius-sm;
  z-index: 10;
  padding: 8px 0;
  width: 200px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  bottom: 25px;
  left: 0;
}

.networkMenuItem {
  padding: 8px 16px;
  cursor: pointer;
  font-size: $font-size-sm;
  color: $color-primary;
  transition: background-color 0.2s;

  &:hover {
    background-color: $color-primary-transparent;
    border-radius: $border-radius-sm;
  }
}

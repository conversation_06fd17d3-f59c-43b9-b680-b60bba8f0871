@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
}

.modalContent {
  position: fixed;
  width: 85%;
  padding: 1.5rem;
  margin: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) !important;
  background: #001a1a;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 16px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1001;

  &::-webkit-scrollbar {
    display: none;
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; // 改为顶部对齐
  margin-bottom: 1.5rem;
  position: relative;
  padding-right: 3rem;

  .titleGroup {
    h2 {
      color: #00ffff;
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      line-height: 1.3; // 调整行高

      &:first-child {
        margin-bottom: 0.2rem; // 两行标题之间的间距
      }
    }
  }
}

.closeButton {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  color: #00ffff;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;

  &:hover {
    color: white;
  }
}

.content {
  .description {
    color: #ffffff;
    margin-bottom: 1rem;
    line-height: 1.6;

    strong {
      color: #00ffff;
    }
  }
}

.divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(0, 255, 255, 0.3),
    transparent
  );
  margin: 0.5rem 0; // 增加上下间距
  position: relative;

  &::before {
    // 添加发光效果
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      to right,
      transparent,
      rgba(0, 255, 255, 0.1),
      transparent
    );
    filter: blur(2px);
  }
}

.navigationLinks {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;

  .navLink {
    padding: 0.6rem 1rem;
    border-radius: 8px;
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    font-size: 0.8rem;
    font-weight: 600;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 255, 255, 0.2);

    &:hover {
      background: rgba(0, 255, 255, 0.2);
      transform: translateY(-2px);
    }
  }
}

import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";

type TPriceHistory = {
  date: string;
  price: string;
  paymentToken: `0x${string}`;
};

const useLineChartPriceHistory = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [priceHistory, setPriceHistory] = useState<TPriceHistory[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const OFFSET = 0n;
  const LIMIT = 30n;

  // Function to convert Unix timestamp to formatted date
  const convertUnixTimeToDate = (unixTime: string): string => {
    const date = new Date(Number(unixTime) * 1000);
    return date.toLocaleDateString();
  };

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getPriceHistoryPaginated",
    args: [BigInt(asteroidMineNumber), OFFSET, LIMIT],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
    onSuccess: (data) => {
      const tempPriceHistory = data[0].map((price) => ({
        date: convertUnixTimeToDate(price.timestamp.toString()),
        price: price.price.toString(),
        paymentToken: price.paymentToken as `0x${string}`,
      }));
      tempPriceHistory.sort((a, b) => Number(a.date) - Number(b.date));
      setPriceHistory(tempPriceHistory);
      setIsLoadingHistory(false);
    },
    onError: () => {
      setIsLoadingHistory(false);
    },
  });

  return { priceHistory, isLoadingHistory };
};

export default useLineChartPriceHistory;

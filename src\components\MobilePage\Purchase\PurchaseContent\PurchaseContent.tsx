import React from "react";
import { useEffect, useState } from "react";
import styles from "./PurchaseContent.module.scss";
import { AnimatePresence, motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { useSnapshot } from "valtio";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import {
  useAccount,
  useContractRead,
  useContractWrite,
  useDisconnect,
  useNetwork,
  usePrepareContractWrite,
  useWaitForTransaction,
  useContractReads,
  useSwitchNetwork,
} from "wagmi";
import { showCustom, showError, showSuccess } from "@/lib/notification";
import {
  BaseError,
  parseEther,
  formatEther,
  ContractFunctionExecutionError,
} from "viem";
import NetworkSelect from "@/components/MainPage/BottomNavigation/BottomContents/RightContents/NetworkSelect/NetworkSelect";
import { networkConfigs } from "@/constants/networkConfigs";
import doubleRing from "@/assets/icons/bottomNavigation/doubleRing.svg";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { confettiStore } from "@/stores/confetti";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import PurchaseDetailsModal from "../../PurchaseDetailsModal/PurchaseDetailsModal";
import { asteroidAddressABI } from "@/constants/abis/ERC1155AsteroidABI";
import { launchPadAsteroidABI } from "@/constants/abis/LaunchPadAsteroidABI";
import { usdtABI } from "@/constants/abis/UsdtABI";
import { ToastContainer, ToastContentProps, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { rewardAsteroidXAbi } from "@/constants/abis/RewardAsteroidXABI";
import { extractErrorReason } from "@/utils/errorHandling";

import Image from "next/image";
// interface PurchaseContentsProps {
//   }

const PurchaseContent = () => {
  // const TOTAL_NFTS_SUPPLY = 20;
  // const currencySelection: Record<string, string> = { usd: "USD", aud: "AUD" };
  // const [selectedCurrency, setSelectedCurrency] = useState("usd");

  const TOTAL_NFTS_SUPPLY = 20;
  const currencySelection: Record<string, string> = { usd: "USD", aud: "AUD" };
  const [selectedCurrency, setSelectedCurrency] = useState("usd");
  const [launchPadAddress, setLaunchPadAddress] = useState<`0x${string}`>("0x");
  const [asteroidAddress, setAsteroidAddress] = useState<`0x${string}`>("0x");
  const [usdtAddress, setUsdtAddress] = useState<`0x${string}`>("0x");
  const [rewardAddress, setRewardAddress] = useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const confettiSnapshot = useSnapshot(confettiStore);
  const [allowance, setAllowance] = useState(false);
  const [price, setPrice] = useState(0);
  const [errorPurchasedMessage, setErrorPurchasedMessage] = useState("");
  const [specialEventErrorMessage, setSpecialEventErrorMessage] = useState("");
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const isSpecialEvent = selectedMine?.shortName === "hskreward";

  const { isConnected, address } = useAccount();
  const { chain } = useNetwork();

  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { disconnect } = useDisconnect({
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      toast.success("Wallet disconnected");
    },
  });
  const { chains, error, isLoading, pendingChainId, switchNetwork } =
    useSwitchNetwork({
      onError: (error) => {
        toast.error((error as BaseError).shortMessage ?? error.message);
      },
      onSuccess: () => {
        toast.success("Switched to supported network");
      },
    });

  const { data: minePrice } = useContractRead({
    address: asteroidAddress,
    abi: asteroidAddressABI,
    functionName: "tokenMinAmounts",
    args: [BigInt(asteroidMineNumber)],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      asteroidAddress !== "0x" &&
      asteroidMineNumber !== 0,
  });

  const { data: allowanceBalance } = useContractRead({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "allowance",
    args: [address ?? "0x", launchPadAddress],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      usdtAddress !== "0x" &&
      address !== undefined &&
      launchPadAddress !== "0x",
  });

  // get the price of the next NFT
  minePrice && purchaseDetailsModalSnapshot.setSinglePrice(minePrice);
  // ----------------------------------------------------------------------------
  // Event only functions (can be removed if not needed)

  const { data: currentSpecialEventId } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "currentPeriodId",
    watch: true,
    enabled: !chain?.unsupported && isConnected,
  });

  const { data: SpecialEventDetails } = useContractRead({
    address: rewardAddress,
    abi: rewardAsteroidXAbi,
    functionName: "periodInfo",
    args: [currentSpecialEventId ?? 0n],
    watch: true,
    enabled:
      !chain?.unsupported && isConnected && currentSpecialEventId !== undefined,
  });

  const { config: specialEventConfig, error: specialEventError } =
    usePrepareContractWrite({
      address: rewardAddress,
      abi: rewardAsteroidXAbi,
      functionName: "purchased",
      args: [
        BigInt(asteroidMineNumber),
        purchaseDetailsModalSnapshot.purchasePrice,
        "******************************************",
      ],
      value: parseEther(purchaseDetailsModalSnapshot.purchasePrice.toString()),
      onError: (error) => {
        if (error instanceof ContractFunctionExecutionError) {
          setSpecialEventErrorMessage(extractErrorReason(error));
        }
      },
      enabled:
        isConnected &&
        !chain?.unsupported &&
        asteroidMineNumber !== 0 &&
        purchaseDetailsModalSnapshot.purchasePrice !== 0n,
    });

  const {
    write: specialEventWrite,
    data: specialEventData,
    isLoading: isSpecialEventLoading,
  } = useContractWrite({
    ...specialEventConfig,
    onError: (error) => {
      if (error instanceof ContractFunctionExecutionError) {
        toast.error(extractErrorReason(error));
      }
    },
  });

  const { isLoading: isWaitingForSpecialEvent } = useWaitForTransaction({
    confirmations: 3,
    hash: specialEventData?.hash,
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      toast.success(specialEventData?.hash);
    },
  });

  const purchaseSpecialEvent = () => {
    if (chain?.unsupported) {
      toast.error("Please Switch Network");
      return;
    }
    if (Number(purchaseDetailsModalSnapshot.purchasePrice) === 0) {
      toast.error("Wrong purchase price");
      return;
    }

    if (SpecialEventDetails?.isPurchasable === false) {
      toast.error("Event is not started yet");
      return;
    }

    if (!specialEventWrite) {
      if (specialEventError) {
        toast.error(specialEventErrorMessage);
      }
      return;
    }

    specialEventWrite?.();
  };

  // prepare to write contract
  const {
    config,
    refetch: refetchPurchased,
    error: errorPurchased,
  } = usePrepareContractWrite({
    address: launchPadAddress,
    abi: launchPadAsteroidABI,
    functionName: "purchased",
    args: [
      BigInt(asteroidMineNumber),
      purchaseDetailsModalSnapshot.purchasePrice,
    ],
    // onError: (error) => {
    //   setErrorPurchasedMessage(
    //     (error as BaseError).shortMessage ?? error.message,
    //   );
    // },
    //value: purchaseDetailsModalSnapshot.purchasePrice,
    // value: minePrice,
    enabled:
      isConnected &&
      !chain?.unsupported &&
      asteroidMineNumber !== 0 &&
      purchaseDetailsModalSnapshot.purchasePrice !== 0n &&
      // minePrice !== undefined &&
      launchPadAddress !== "0x",
    // onError: (error) => {
    //   showError((error as BaseError).shortMessage ?? error.message);
    // },
    onError: (error) => {
      setErrorPurchasedMessage(extractErrorReason(error));
    },
  });

  const {
    write,
    data,
    isLoading: isMintingNft,
  } = useContractWrite({
    ...config,
    onError: (error) => {
      toast.error(extractErrorReason(error));
    },
  });

  const { isLoading: isWaitingForConfirmation } = useWaitForTransaction({
    confirmations: 3,
    hash: data?.hash,
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: () => {
      showConfetti(data?.hash);
    },
  });

  const showConfetti = (hash: `0x${string}` | undefined) => {
    confettiSnapshot.setIsShowConfetti(true);
    purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(false);

    setTimeout(() => {
      confettiSnapshot.setIsShowConfetti(false);
      toast.success(
        "Congratulations! You have successfully purchased the Mine",
      );
      toast.success("Click to view your transaction");
      toast.info(
        <>
          {chain && !chain?.unsupported ? (
            <div className={styles.transactionHash}>
              <a
                href={`${networkConfigs[chain?.id].etherscanTxAddress}${hash}`}
                target="_blank"
              >
                Click here to view
              </a>
            </div>
          ) : null}
        </>,
      );
    }, 4500);
  };

  const purchaseNft = () => {
    if (chain?.unsupported) {
      toast.error("Please Switch Network");
      return;
    }
    if (Number(purchaseDetailsModalSnapshot.purchasePrice) === 0) {
      toast.error("Wrong purchase price");
      return;
    }
    if (!write) {
      if (errorPurchased) {
        toast.error(errorPurchasedMessage);
      }
      return;
    }

    write?.();
  };

  const { config: approved, refetch: refetchApproved } =
    usePrepareContractWrite({
      address: usdtAddress,
      abi: usdtABI,
      functionName: "approve",
      args: [
        launchPadAddress,
        parseEther("0xffffffffffffffffffffffffffffffff"),
      ],
      enabled:
        isConnected &&
        !chain?.unsupported &&
        launchPadAddress !== "0x" &&
        usdtAddress !== "0x" &&
        purchaseDetailsModalSnapshot.purchasePrice !== 0n,
      onError: (error) => {
        // toast.error((error as BaseError).shortMessage ?? error.message);
      },
    });

  const {
    write: approveWrite,
    data: approveData,
    isLoading: isApproving,
  } = useContractWrite({
    ...approved,
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
  });

  const trimWalletAddress = (address: string) =>
    address.slice(0, 6) + "..." + address.slice(-4);

  const { isLoading: isWaitingForApprove } = useWaitForTransaction({
    confirmations: 5,
    hash: approveData?.hash,
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: async () => {
      // setAllowance(true);
      // await refetchPurchased();
      // purchaseNft();
      // showSuccess("Successfully Approved, Press the Buy button");
      try {
        await refetchPurchased();

        // 延时后再检查 write 状态
        setTimeout(async () => {
          try {
            // 再次刷新状态
            await refetchPurchased();

            if (!write) {
              console.log("Write not ready, waiting for manual trigger");
              toast.success(
                "USDT approved successfully. Please click 'Buy' to continue.",
              );
              return;
            }

            purchaseNft();
          } catch (error) {
            console.log("Auto purchase attempt failed:", error);
            toast.success(
              "USDT approved successfully. Please click 'Buy' to continue.",
            );
          }
        }, 2000);
      } catch (error) {
        console.log("Refetch failed:", error);
        toast.success(
          "USDT approved successfully. Please click 'Buy' to continue.",
        );
      }
    },
  });

  const { config: approveZero } = usePrepareContractWrite({
    address: usdtAddress,
    abi: usdtABI,
    functionName: "approve",
    args: [launchPadAddress, 0n],
    enabled:
      isConnected &&
      !chain?.unsupported &&
      launchPadAddress !== "0x" &&
      usdtAddress !== "0x",
    onError: (error) => {
      console.log(error);
    },
  });

  const {
    write: approveZeroWrite,
    data: approveZeroData,
    isLoading: isApprovingZero,
  } = useContractWrite({
    ...approveZero,
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
  });

  const { isLoading: isWaitingForApproveZero } = useWaitForTransaction({
    confirmations: 7,
    hash: approveZeroData?.hash,
    onError: (error) => {
      toast.error((error as BaseError).shortMessage ?? error.message);
    },
    onSuccess: async () => {
      await refetchApproved();
      approveUsdt();
    },
  });

  const approveZeroUsdt = () => {
    if (chain?.unsupported) {
      toast.error("Please Switch Network");
      return;
    }
    if (!approveZeroWrite) {
      toast.error("Unable to approve Mine");
      return;
    }
    approveZeroWrite?.();
  };

  const approveUsdt = () => {
    if (chain?.unsupported) {
      toast.error("Please Switch Network");
      return;
    }
    if (!approveWrite) {
      toast.error("Unable to approve Mine");
      return;
    }
    approveWrite?.();
  };

  const getSelectedMineAddress = (chainId: number) => {
    return networkConfigs[chainId].asteroidAddress;
  };

  const getUsdtAddress = (chainId: number) => {
    return networkConfigs[chainId].usdtAddress;
  };

  const getSelectedMineId = (chainId: number) => {
    const selectedMine = minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0].name;

    switch (selectedMine) {
      case minesDetails[0].name:
        return networkConfigs[chainId].assetIds.mt;
      case minesDetails[1].name:
        return networkConfigs[chainId].assetIds.matsa;
      case minesDetails[2].name:
        return networkConfigs[chainId].assetIds.zephyr;
      case minesDetails[3].name:
        return networkConfigs[chainId].assetIds.jim;
      case minesDetails[4].name:
        return networkConfigs[chainId].assetIds.pcgold;
      case minesDetails[5].name:
        return networkConfigs[chainId].assetIds.menzies;
      case minesDetails[6].name:
        return networkConfigs[chainId].assetIds.hskreward;
      default:
        break;
    }
  };

  useEffect(() => {
    if (chain?.unsupported) {
      setLaunchPadAddress("0x");
      setAsteroidAddress("0x");
      setRewardAddress("0x");
      toast.error("UNSUPPORTED NETWORK");
    } else {
      if (chain) {
        setLaunchPadAddress(networkConfigs[chain.id].launchPadAddress);
        setAsteroidAddress(getSelectedMineAddress(chain.id) ?? "0x");
        setUsdtAddress(getUsdtAddress(chain.id) ?? "0x");
        setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
        setRewardAddress(networkConfigs[chain.id].rewardAddress);
      }
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const loadingStatus = {
    loadingType: {
      isApproving,
      isWaitingForApprove,
      isApprovingZero,
      isWaitingForApproveZero,
      isWaitingForConfirmation,
      isMintingNft,
      isSpecialEventLoading,
      isWaitingForSpecialEvent,
    },
  };

  const { data: unitPriceFromContract } = useContractRead({
    address: launchPadAddress,
    abi: launchPadAsteroidABI,
    functionName: "unitPrices",
    args: [BigInt(asteroidMineNumber)],
    watch: true,
    enabled:
      !chain?.unsupported &&
      isConnected &&
      launchPadAddress !== "0x" &&
      asteroidMineNumber !== 0,
  });

  return (
    <>
      <AnimatePresence>
        {purchaseDetailsModalSnapshot.isOpenPurchaseDetailsModal ? (
          <PurchaseDetailsModal
            purchaseNft={purchaseNft}
            // allowance={allowance}
            approveUsdt={approveUsdt}
            approveZeroUsdt={approveZeroUsdt}
            minePrice={minePrice}
            unitPriceFromContract={unitPriceFromContract}
            allowanceBalance={allowanceBalance}
            purchaseSpecialEvent={purchaseSpecialEvent}
            {...loadingStatus}
          />
        ) : null}
      </AnimatePresence>
      <div className={styles.container}>
        <div className={styles.titleContainer}>
          <div>
            <div className={styles.subtitle}>
              <h1 className={styles.mineral}>{information.mineMineral}</h1>
              {/* <img src={locationIcon.src} alt="location icon" /> */}
              <h1 className={styles.location}>{information.mineLocation}</h1>
            </div>
            <h1 className={styles.title}>{information.name}</h1>
          </div>
        </div>
        <div className={styles.imgContainer}>
          <div className={styles.imageFrame}>
            <Image
              src={information.mineImages[0]}
              alt={information.name}
              layout="fill"
              style={{ objectFit: "cover" }}
            />
          </div>
          <div className={styles.information}>
            <div className={styles.logoWrapper}>
              <div>
                <h1 className={styles.title}>price</h1>
                <h1 className={styles.subtitle}>${information.minePrice}</h1>
              </div>
            </div>
            <div className={styles.logoWrapper}>
              <div>
                <h1 className={styles.title}>Highlight</h1>
                <h1 className={styles.subtitle}>{information.mineStorage}</h1>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.purchaseContainer}>
          <div className={styles.header}>
            <div>
              <h1 className={styles.title}>Purchase Number</h1>
              <h2 className={styles.subtitle}>Buy A Region</h2>
            </div>
          </div>
          <div className={styles.mineDetails}>
            <span className={styles.mineName}>{information.mineZone}</span>
            <div>
              {asteroidMineNumber && isConnected ? (
                <h1 className={styles.mineNumber}>ID #{asteroidMineNumber}</h1>
              ) : null}
            </div>
          </div>
          <div className={styles.mineDetails}>
            <div>
              <h1 className={styles.mineRegion}>
                <span className={styles.mineName}>
                  {information.mineMineral}
                </span>
              </h1>
            </div>
            <div>
              <h1 className={styles.mineRegion}>
                {minePrice !== undefined && isConnected ? (
                  <span className={styles.mineNumber}>
                    $ {information.minePrice}
                  </span>
                ) : null}
                {/* <span className={styles.mineNumber}> PER CART</span> */}
              </h1>
            </div>
          </div>
        </div>
        <div className={styles.loginDetails}>
          <div className={styles.detailsWrapper}>
            <h1 className={styles.walletAddress}>Wallet:</h1>
            {address ? (
              <>
                <span>{trimWalletAddress(address.toString())}</span>
              </>
            ) : (
              <span>N/A</span>
            )}
          </div>
          <div className={styles.detailsWrapper}>
            <h1 className={styles.blockchainNetwork}>Network:</h1>
            {chain ? (
              <span style={{ textTransform: "uppercase" }}>
                {chain?.name ?? chain?.id}
                <span>
                  {" "}
                  {chain.unsupported ? "(UNSUPPORTED NETWORK)" : null}
                  {chain.unsupported ? (
                    <span onClick={() => switchNetwork?.(chains[0].id)}>
                      Switch Network{" "}
                    </span>
                  ) : null}
                  {!chain.unsupported ? (
                    <span
                      onClick={() =>
                        switchNetwork?.(
                          chains.filter((x) => x.id !== chain.id)[0].id,
                        )
                      }
                    >
                      Switch Network{" "}
                    </span>
                  ) : null}
                </span>
              </span>
            ) : (
              <span style={{ textTransform: "uppercase" }}>N/A</span>
            )}
          </div>

          {isConnected ? (
            <>
              {isMintingNft ||
              isWaitingForConfirmation ||
              isApproving ||
              isWaitingForApprove ||
              isApprovingZero ||
              isWaitingForApproveZero ||
              isSpecialEventLoading ||
              isWaitingForSpecialEvent ? (
                <motion.button
                  className={styles.loginButton}
                  disabled
                  style={{ cursor: "wait" }}
                >
                  <img
                    src={doubleRing.src}
                    width={20}
                    height={20}
                    alt="loading icon"
                  />
                  {"  "}
                  Processing...
                </motion.button>
              ) : (
                <>
                  {/* {isSpecialEvent ?
                    ( */}
                  <motion.button
                    className={styles.loginButton}
                    whileTap={buttonEffect.tap}
                    // disabled={!write}
                    onClick={() => {
                      // showConfetti();
                      // purchaseNft();
                      if (chain && chain.unsupported) {
                        showError("Please Switch Network");
                        return;
                      }
                      if (
                        chain &&
                        selectedMine.supportedNetwork.includes(chain?.id)
                      ) {
                        purchaseDetailsModalSnapshot.setIsOpenPurchaseDetailsModal(
                          true,
                        );
                      } else {
                        switchNetwork?.(
                          chain?.testnet
                            ? selectedMine.supportedNetwork[0]
                            : selectedMine.supportedNetwork[1],
                        );
                      }
                    }}
                  >
                    Purchase This Mine
                  </motion.button>
                  {/* )
                  (null)
                   } */}
                  <motion.button
                    onClick={() => disconnect()}
                    className={styles.loginButton}
                    whileTap={buttonEffect.tap}
                  >
                    Logout
                  </motion.button>
                </>
              )}
            </>
          ) : (
            <motion.div
              className={styles.loginButton}
              whileTap={buttonEffect.tap}
              onClick={() => {
                connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
              }}
            >
              Log in <span>&nbsp;or&nbsp;</span> Register Now
            </motion.div>
          )}
        </div>
      </div>
      {/* <ToastContainer
        position="top-left"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        // pauseOnFocusLoss
        // draggable
        // pauseOnHover
        theme="dark"
      // transition={Flip}
      /> */}
    </>
  );
};

export default PurchaseContent;

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$rotation-angle: rotateX(10deg) rotateY(0deg);
$download-rotation-angle: rotateX(6deg) rotateY(0deg);
$hightlightBackground: rgba($color-primary, 0.3);
@mixin table-grid-columns {
  grid-template-columns:
    minmax(110px, 1fr) // Price per Token
    minmax(120px, 1fr) // Quantity
    minmax(70px, 1.2fr) // Selling Price
    minmax(100px, 1fr) // From
    minmax(180px, auto)
    minmax(70px, auto); // Action
}
.wrapper {
  position: fixed;
  top: 52%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px; /* 可以根据实际需求调整最大宽度 */
  height: 85%;
  max-height: 600px; /* 可以根据实际需求调整最大高度 */
  z-index: 1000;
  background: $color-black-transparent;
  backdrop-filter: blur(10px);
  // mask-image: linear-gradient(to bottom, black 85%, transparent);
  padding: $padding-md;
  clip-path: polygon(0 0, calc(100% - 2rem) 0, 100% 2rem, 100% 100%, 0 100%);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: $color-primary;
    // background: linear-gradient(
    //   to bottom,
    //   $color-primary,
    //   $color-black-transparent
    // );
    box-shadow: 0 0 10rem 10rem $color-primary;

    clip-path: polygon(
      0 0,
      calc(100% - 32px) 0,
      100% 32px,
      100% 100%,
      0 100%,
      0 0,
      1px 1px,
      1px calc(100% - 1px),
      calc(100% - 1px) calc(100% - 1px),
      calc(100% - 1px) calc(32px + 0.41px),
      calc(100% - 32px - 0.41px) 1px,
      1px 1px
    );
  }

  .header {
    font-family: "Garalama", sans-serif;
    font-weight: $font-weight-extralight;
    letter-spacing: 1px;
    width: 100%;
    color: #00e0ff;
  }
  .headerWrapper {
    @include row-between;
    // margin-bottom: 1rem;
    gap: 1rem;

    .closeButton {
      margin-top: 0.5rem;
    }
  }
}

.contentWrapper {
  width: 100%;
  height: 100%;
  padding: 1rem 0;
  @include col-center;
  gap: 1rem;

  .header {
    width: 100%;
    color: $color-primary;
  }

  .scrollArea {
    width: 100%;
    height: 100%;
    overflow: scroll;
    scroll-behavior: smooth;
    // border: 1px solid yellow;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// &::before {
//   content: "";
//   position: absolute;
//   inset: 0;
//   background: $color-primary;
//   clip-path: polygon(
//     0 0,
//     calc(100% - 81px) 0,
//     100% 81px,
//     100% calc(100% - 48px),
//     calc(100% - 48px) 100%,
//     48px 100%,
//     0 calc(100% - 48px),
//     0 0,
//     1px 1px,
//     1px calc(100% - 48px - 0.41px),
//     calc(48px + 0.41px) calc(100% - 1px),
//     calc(100% - 48px - 0.41px) calc(100% - 1px),
//     calc(100% - 1px) calc(100% - 48px - 0.41px),
//     calc(100% - 1px) calc(48px + 0.41px),
//     calc(100% - 48px - 0.41px) 1px,
//     1px 1px
//   );

.contentFrame {
  // padding: 1rem 1rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  // height: 100%;
  position: relative;
  // background: $color-black-transparent-dark;
  // clip-path: polygon(
  //   0 0,
  //   calc(100% - 80px) 0,
  //   100% 80px,
  //   100% calc(100% - 48px),
  //   calc(100% - 48px) 100%,
  //   48px 100%,
  //   0 calc(100% - 48px)
  // );

  // &::before {
  //   content: "";
  //   position: absolute;
  //   inset: 0;
  //   background: $color-primary;
  //   clip-path: polygon(
  //     0 0,
  //     calc(100% - 81px) 0,
  //     100% 81px,
  //     100% calc(100% - 48px),
  //     calc(100% - 48px) 100%,
  //     48px 100%,
  //     0 calc(100% - 48px),
  //     0 0,
  //     1px 1px,
  //     1px calc(100% - 48px - 0.41px),
  //     calc(48px + 0.41px) calc(100% - 1px),
  //     calc(100% - 48px - 0.41px) calc(100% - 1px),
  //     calc(100% - 1px) calc(100% - 48px - 0.41px),
  //     calc(100% - 1px) calc(48px + 0.41px),
  //     calc(100% - 48px - 0.41px) 1px,
  //     1px 1px
  //   );
  // }
  .derlordInfo {
    h3 {
      color: #00e0ff;
      font-size: 24px;
    }

    span {
      display: block;
      color: #00e0ff;
    }

    p {
      color: #00e0ff;
      font-size: 14px;
    }
  }

  .itemCount,
  .priceSection,
  .durationSection {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;

    h3,
    h4 {
      color: $color-primary;
      font-size: 1rem;
    }

    h4 {
      opacity: 0.8;
    }
  }

  .inputContainer,
  .priceInput,
  .durationControls {
    display: flex;
    gap: 1rem;
    width: 100%;

    input,
    select {
      border-radius: 0.5rem;
      flex: 1;
      background: rgba(0, 224, 255, 0.2);
      border: 1px solid $color-primary-contrast;
      color: #00e0ff;
      padding: 0.75rem 1rem;
      font-size: 1rem;
      outline: none;

      &::placeholder {
        color: $color-primary;
        opacity: 0.5;
      }
    }

    .available,
    .eth {
      display: flex;
      align-items: center;
      color: $color-primary;
      padding: 0 1rem;
    }
  }

  .durationControls {
    select {
      max-width: 200px;
    }

    input[type="datetime-local"] {
      flex: 2;
    }
  }

  .summary {
    display: flex;
    flex-direction: column;
    // gap: 0.2rem;

    .summaryRow {
      display: flex;
      justify-content: space-between;
      color: $color-primary-contrast;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba($color-primary, 0.2);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .completeButton {
    border-radius: 0.5rem;
    width: 60%;
    padding: 1rem;
    background: rgba(0, 224, 255, 0.2);
    border: 1px solid $color-primary-contrast;
    color: $color-primary;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba($color-primary, 0.2);
    }

    &:disabled {
      background: $color-black-transparent-dark;
      color: $color-primary-contrast;
      border: 1px solid $color-primary-contrast;
      cursor: not-allowed;
    }
  }
}

.listingsTable {
  padding: 0 1rem 1rem 0;
  width: 100%;
  overflow-x: auto; // 关键：启用横向滚动
  // overflow-y: scroll;
  // border: 1px solid yellow;

  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
  .tableHeader {
    display: grid;
    @include table-grid-columns; // 复用列宽配置
    gap: 1rem;
    padding: 0.5rem 0;
    position: sticky;
    left: 0; // 固定表头（可选）
    // background: #1A1A1A; // 根据你的背景色调整
    border-bottom: 1px solid rgba($color-black-transparent-light, 0.1);
    color: rgba(white, 0.6);
    min-width: fit-content; // 防止列挤压
  }

  .tableBody {
    .tableRow {
      display: grid;
      @include table-grid-columns; // 确保与表头列宽一致
      gap: 1rem;
      padding: 1rem 0;
      border-bottom: 1px solid rgba($color-black-transparent-light, 0.1);
      align-items: center;
      min-width: fit-content; // 防止移动端挤压

      &:last-child {
        border-bottom: none; // 最后一行去掉边框
      }

      .price {
        color: $color-primary;
      }

      .usdPrice {
        color: $color-primary;
      }

      .quantity {
        color: $color-primary;
      }

      .expiration {
        color: $color-primary;
      }

      .from {
        color: $color-primary;
      }

      button.buyButton {
        padding: 0.5rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        width: auto; // 让按钮宽度自适应内容
        min-width: 70px; // 可选，保证最小宽度
        font-weight: bold;
        border: 1px solid $color-primary;
        background: $color-black-transparent-dark;
        color: $color-primary;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;

        &:hover {
          background: $hightlightBackground;
        }

        &:disabled {
          background: $color-black-transparent-dark;
          color: $color-primary-contrast;
          border: 1px solid $color-primary-contrast;
          cursor: not-allowed;
        }
      }
    }
  }
}

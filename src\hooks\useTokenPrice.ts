import { useNetwork } from "wagmi";
import { useContractRead } from "wagmi";
import { erc20ABI } from "wagmi";
import { useState, useEffect, useMemo } from "react";
import {
  formatTokenPrice,
  parseTokenAmount,
  getTokenDecimals,
} from "../utils/tokenPriceFormatter";

/**
 * Hook to get token decimals
 * @param tokenAddress Token address
 * @returns decimals
 */
export const useTokenDecimals = (tokenAddress?: `0x${string}`) => {
  const [decimals, setDecimals] = useState<number>(18);
  const { chain } = useNetwork();

  // Only fetch decimals from on-chain contract, never fallback
  const { data } = useContractRead({
    address: tokenAddress,
    abi: erc20ABI,
    functionName: "decimals",
    enabled:
      !!tokenAddress &&
      tokenAddress !== "0x0000000000000000000000000000000000000000" &&
      !!chain?.id,
    watch: true,
  });

  useEffect(() => {
    if (data !== undefined) {
      setDecimals(Number(data));
    }
  }, [tokenAddress, data, chain?.id]);

  return decimals;
};

/**
 * Hook to format token price
 * @param amount Amount
 * @param tokenAddress Token address
 * @returns Formatted price string
 */
export const useFormatTokenPrice = (
  amount: bigint | undefined,
  tokenAddress?: `0x${string}`,
) => {
  const decimals = useTokenDecimals(tokenAddress);
  const { chain } = useNetwork();

  // Always use the latest chainId for formatting
  const formattedPrice = useMemo(() => {
    return formatTokenPrice(amount, tokenAddress, decimals, chain?.id);
  }, [amount, tokenAddress, decimals, chain?.id]);

  return formattedPrice;
};

/**
 * Parse token amount hook
 * @param amount Amount string
 * @param tokenAddress Token address
 * @returns Parsed amount BigInt
 */
export const useParseTokenAmount = (
  amount: string | number,
  tokenAddress?: `0x${string}`,
) => {
  const decimals = useTokenDecimals(tokenAddress);
  const { chain } = useNetwork();

  // Always use the latest chainId for parsing
  const parsedAmount = useMemo(() => {
    return parseTokenAmount(amount, tokenAddress, decimals, chain?.id);
  }, [amount, tokenAddress, decimals, chain?.id]);

  return parsedAmount;
};

import { CameraControls } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";
import { MutableRefObject } from "react";

type AutoRotateProps = {
  cameraControlsRef: MutableRefObject<CameraControls | undefined>;
};

const AutoRotate = ({ cameraControlsRef }: AutoRotateProps) => {
  useFrame(() => {
    cameraControlsRef.current?.rotate(-0.0001, 0, true);
  });

  return null;
};

export default AutoRotate;

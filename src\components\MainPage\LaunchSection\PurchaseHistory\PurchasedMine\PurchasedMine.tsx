import { MinePurchased } from "../hooks/usePurchaseHistory";
import styles from "./PurchasedMine.module.scss";

const PurchasedMine = ({
  mineDetails,
}: {
  mineDetails: MinePurchased["mineDetails"];
}) => {
  return (
    <div className={styles.container}>
      <img src={mineDetails.image} alt="mine image" />
      <div className={styles.detailsWrapper}>
        <div className={styles.details}>
          <h5>Mine:</h5>
          <p>{mineDetails.name}</p>
        </div>
        <div className={styles.details}>
          <h5>Mineral:</h5>
          <p>{mineDetails.mineral}</p>
        </div>
        <div className={styles.details}>
          <h5>Price:</h5>
          <p>${mineDetails.price}</p>
        </div>
      </div>
    </div>
  );
};

export default PurchasedMine;

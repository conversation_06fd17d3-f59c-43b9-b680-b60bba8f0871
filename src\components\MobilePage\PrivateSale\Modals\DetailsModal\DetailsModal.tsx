import { motion, AnimatePresence } from "framer-motion";
import styles from "./DetailsModal.module.scss";
import useProgressBar from "@/components/MainPage/PrivateSale/components/MainContent/HeroSection/Navigation/hooks/useProgressBar";

interface DetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DetailsModal: React.FC<DetailsModalProps> = ({ isOpen, onClose }) => {
  const { raisedAmount } = useProgressBar();
  const saleInfo = [
    {
      label: "PRICE",
      value: "0.3 HSK",
    },
    {
      label: "LIMIT",
      value: "5 per wallet",
    },
    {
      label: "NEXT ROUND PRICE",
      value: "1 HSK ≈ 2.86 DrillX",
    },
    {
      label: "NEXT ROUND QUANTITY",
      value: "1.5M DrillX",
    },
    {
      label: "Total Raised",
      value: `${raisedAmount} HSK`,
    },
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className={styles.overlay}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          <motion.div
            className={styles.modal}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
          >
            <button className={styles.closeButton} onClick={onClose}>
              ×
            </button>

            <div className={styles.content}>
              <p className={styles.description}>
                {/* Thank you for your continued support of HSK and Asteroid X. Here,
                <strong> you can keep earning $Asteroid tokens</strong> and continuously
                increase your rewards. */}
                <strong>$Drill X Details</strong>
              </p>

              <div className={styles.divider} />

              <div className={styles.saleInfoContainer}>
                {saleInfo.map((info, index) => (
                  <div key={index} className={styles.infoRow}>
                    <span className={styles.label}>{info.label}</span>
                    <span className={styles.value}>{info.value}</span>
                  </div>
                ))}
              </div>

              <div className={styles.divider} />

              <div className={styles.navigationLinks}>
                <a
                  href="https://asteroid-x-1.gitbook.io/asteroid-x-product-book"
                  target="_blank"
                  className={styles.navLink}
                >
                  PRODUCT BOOK
                </a>
                <a href="/" className={styles.navLink}>
                  TOKENOMICS
                </a>
                <a href="/" className={styles.navLink}>
                  DISCLAIMER
                </a>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default DetailsModal;

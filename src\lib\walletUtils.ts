/**
 * 钱包工具函数 - 实现EIP-6963标准
 */

// 为Window添加_FORCE_METAMASK_ONLY属性
declare global {
  interface Window {
    _FORCE_METAMASK_ONLY?: boolean;
  }
}

// 清理敏感对象，避免无限递归和过多信息
function cleanupForLogging(obj: any) {
  if (!obj) return obj;

  // 提取基本信息
  return {
    isMetaMask: obj.isMetaMask,
    isOKXWallet: obj.isOKXWallet,
    isOkxWallet: obj.isOkxWallet,
    isOKX: obj.isOKX,
    isOKExWallet: obj.isOKExWallet,
    isCoinbaseWallet: obj.isCoinbaseWallet,
    _metamask: obj._metamask ? "(present)" : undefined,
    name: obj.name,
    rdns: obj.rdns,
    chainId: obj.chainId,
    selectedAddress: obj.selectedAddress,
  };
}

// 存储EIP-6963声明的提供者
let eip6963Providers: Array<{
  info: {
    uuid: string;
    name: string;
    icon: string;
    rdns: string;
  };
  provider: any;
}> = [];

// EIP-6963事件处理
function setupEIP6963Detection() {
  if (typeof window === "undefined") return;

  // 清除现有的提供者列表
  eip6963Providers = [];

  // 声明处理函数
  function handleEIP6963Announce(event: any) {
    const { info, provider } = event.detail;
    console.log(`EIP-6963提供商 #${eip6963Providers.length}: `, info);

    // 去重处理
    if (!eip6963Providers.some((p) => p.info.uuid === info.uuid)) {
      eip6963Providers.push({ info, provider });
    }
  }

  // 添加事件监听器
  window.addEventListener("eip6963:announceProvider", handleEIP6963Announce);

  // 等待一小段时间，确保所有提供者都有时间注册
  setTimeout(() => {
    // 请求提供者
    window.dispatchEvent(new Event("eip6963:requestProvider"));
    console.log(`EIP-6963检测完成，找到提供商: ${eip6963Providers.length}`);
  }, 300);
}

// 获取后打印状态
const logProviderStatus = (message: string, provider: any) => {
  console.log(message, cleanupForLogging(provider));
};

/**
 * 从EIP-6963提供者中获取真正的MetaMask提供者
 */
export const getTrueMetaMaskFromEIP6963 = () => {
  // 如果没有找到EIP-6963提供者，重新扫描
  if (eip6963Providers.length === 0) {
    setupEIP6963Detection();
    // 等待提供者声明
    return null;
  }

  // 从EIP-6963提供者中找到MetaMask
  const metamaskProvider = eip6963Providers.find(
    (p) => p.info.rdns === "io.metamask" && p.provider.isMetaMask,
  );

  if (metamaskProvider) {
    console.log("通过rdns找到真正的MetaMask:", metamaskProvider.info.name);
    console.log("成功从EIP-6963获取MetaMask");
    return metamaskProvider.provider;
  }

  // 尝试通过name属性识别
  const nameMetamaskProvider = eip6963Providers.find(
    (p) => p.info.name === "MetaMask" && p.provider.isMetaMask,
  );

  if (nameMetamaskProvider) {
    console.log("通过name属性找到MetaMask:", nameMetamaskProvider.info.name);
    return nameMetamaskProvider.provider;
  }

  return null;
};

/**
 * 从window.ethereum.providers获取纯正的MetaMask提供者
 */
export const getDirectMetaMaskProvider = () => {
  try {
    // 首先尝试EIP-6963标准检测
    const eip6963Provider = getTrueMetaMaskFromEIP6963();
    if (eip6963Provider) {
      return eip6963Provider;
    }

    console.log("尝试识别真正的MetaMask提供者");

    // 检查是否存在多个提供者
    if (window.ethereum?.providers?.length) {
      console.log("检测到多个提供者:", window.ethereum.providers.length);

      // 尝试方法1: 使用EIP6963 - 以"isMetaMask && rdns === 'io.metamask'"为特征
      for (const provider of window.ethereum.providers) {
        if (
          provider.isMetaMask &&
          (provider as any).rdns === "io.metamask" &&
          !(provider as any).isOKXWallet
        ) {
          console.log("通过EIP-6963 rdns找到MetaMask提供者", provider);
          return provider;
        }
      }

      // 尝试方法2: 使用name属性
      for (const provider of window.ethereum.providers) {
        if (
          provider.isMetaMask &&
          (provider as any).name === "MetaMask" &&
          !(provider as any).isOKXWallet
        ) {
          console.log("通过name属性找到MetaMask提供者", provider);
          return provider;
        }
      }

      // 尝试方法3: 使用_metamask内部属性
      for (const provider of window.ethereum.providers) {
        if ((provider as any)._metamask && !(provider as any).isOKXWallet) {
          console.log("通过_metamask属性找到MetaMask提供者", provider);
          return provider;
        }
      }

      // 尝试方法4: 通过排除法 - 找出isMetaMask=true但不是OKX的提供者
      for (const provider of window.ethereum.providers) {
        if (
          provider.isMetaMask &&
          !(provider as any).isOKXWallet &&
          !(provider as any).isOkxWallet &&
          !(provider as any).isOKExWallet &&
          !(provider as any).rdns?.includes("okex")
        ) {
          console.log("通过排除法找到可能的MetaMask提供者", provider);
          return provider;
        }
      }

      // 如果仍然失败，使用第一个提供者作为最后尝试
      console.log("无法明确识别MetaMask，尝试使用第一个提供者");
      return window.ethereum.providers[0];
    }

    // 如果没有providers数组，检查window.ethereum本身
    if (
      window.ethereum?.isMetaMask &&
      !(window.ethereum as any).isOKXWallet &&
      !(window.ethereum as any).isOkxWallet
    ) {
      console.log("使用window.ethereum作为MetaMask提供者");
      return window.ethereum;
    }
  } catch (e) {
    console.error("获取MetaMask提供者时出错", e);
  }

  console.log("无法找到MetaMask提供者");
  return null;
};

/**
 * 诊断钱包提供者环境，打印日志
 */
export const diagnoseWalletProviders = async () => {
  console.log("钱包诊断工具运行中...");

  if (!window.ethereum) {
    console.log("未检测到任何以太坊提供者");
    return;
  }

  console.log("主以太坊提供者属性:", cleanupForLogging(window.ethereum));

  // 重新扫描EIP-6963提供者
  setupEIP6963Detection();

  // 给EIP-6963检测一些时间
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 显示检测到的EIP-6963提供者
  if (eip6963Providers.length > 0) {
    console.log(`检测到${eip6963Providers.length}个EIP-6963提供者`);

    eip6963Providers.forEach((item, index) => {
      console.log(`EIP-6963提供者 #${index}详情:`, {
        name: item.info.name,
        rdns: item.info.rdns,
        uuid: item.info.uuid,
        isMetaMask: item.provider.isMetaMask,
        isOKXWallet: item.provider.isOKXWallet,
        isOkxWallet: item.provider.isOkxWallet,
        isOKX: item.provider.isOKX,
        isOKExWallet: item.provider.isOKExWallet,
      });
    });
  }

  // 检查window.ethereum.providers
  if (window.ethereum.providers) {
    console.log(
      `检测到${window.ethereum.providers.length}个providers数组提供者`,
    );

    window.ethereum.providers.forEach((provider, index) => {
      console.log(`Provider #${index}详情:`, {
        name: (provider as any).name,
        rdns: (provider as any).rdns,
        isMetaMask: provider.isMetaMask,
        isOKXWallet: (provider as any).isOKXWallet,
        isOkxWallet: (provider as any).isOkxWallet,
        isOKX: (provider as any).isOKX,
        isOKExWallet: (provider as any).isOKExWallet,
      });
    });
  }

  // 检测MetaMask提供者
  const metaMaskProvider = await getDirectMetaMaskProvider();
  logProviderStatus("MetaMask特定检测结果:", metaMaskProvider);

  console.log("钱包诊断完成。如果您仍有问题，请将上述日志信息提供给开发人员。");
};

/**
 * 注册全局钱包诊断函数
 */
export const registerGlobalDiagnostics = () => {
  // 添加诊断函数到全局作用域
  if (typeof window !== "undefined") {
    (window as any).diagnoseWalletProviders = diagnoseWalletProviders;

    // 初始化EIP-6963检测
    setupEIP6963Detection();

    console.log(
      "已添加全局钱包诊断功能，可以在控制台运行 window.diagnoseWalletProviders() 进行诊断",
    );
  }
};

/**
 * 强制禁用OKX钱包劫持，强制使用MetaMask
 * 这个函数使用属性描述符来创建一个隔离的环境，而不尝试直接修改只读属性
 */
export const forceMetaMaskOnly = async (callback: () => Promise<any>) => {
  if (typeof window === "undefined") return null;

  // 保存原始提供者环境
  const originalEthereum = window.ethereum;
  let originalDescriptor = Object.getOwnPropertyDescriptor(window, "ethereum");

  // 如果没有找到描述符，创建一个基本的描述符
  if (!originalDescriptor) {
    originalDescriptor = {
      configurable: true,
      enumerable: true,
      writable: true,
      value: originalEthereum,
    };
  }

  try {
    // 获取真正的MetaMask提供者
    const metaMaskProvider = getTrueMetaMaskFromEIP6963();
    if (!metaMaskProvider) {
      console.log("未找到MetaMask提供者，无法执行强制操作");
      return await callback();
    }

    console.log("强制使用MetaMask，创建隔离环境...");

    // 保存一个使用MetaMask的副本，保持必要的引用
    const isolatedProvider = metaMaskProvider;

    // 如果存在providers数组，创建一个只包含MetaMask的新数组
    if (isolatedProvider.providers) {
      isolatedProvider.providers = [isolatedProvider];
    }

    // 使用属性描述符重新定义window.ethereum
    Object.defineProperty(window, "ethereum", {
      configurable: true,
      enumerable: true,
      get: function () {
        return isolatedProvider;
      },
      set: function () {
        // 忽略所有设置尝试，保持我们的提供者
        console.log("阻止ethereum被替换");
      },
    });

    // 设置标志，让其他代码知道我们处于强制MetaMask模式
    window._FORCE_METAMASK_ONLY = true;

    console.log("环境已准备好，只允许MetaMask");

    // 执行回调函数
    const result = await callback();

    // 给连接操作一点时间完成
    await new Promise((resolve) => setTimeout(resolve, 500));

    return result;
  } catch (error) {
    console.error("强制使用MetaMask过程中出错:", error);
    throw error;
  } finally {
    // 恢复原始环境
    console.log("恢复原始以太坊环境...");

    // 使用原始描述符恢复window.ethereum
    Object.defineProperty(window, "ethereum", originalDescriptor);

    // 清除标志
    delete window._FORCE_METAMASK_ONLY;
  }
};

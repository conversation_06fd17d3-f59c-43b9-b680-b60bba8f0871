import { buttonEffect, rotation, slideRight } from "@/animations/animations";
import styles from "./NavigationButton.module.scss";
import { motion } from "framer-motion";
import { Dispatch, SetStateAction, useState } from "react";
import leftNavigationButtonFrame from "@/assets/icons/leftNavigation/frame.png";
import leftNavigationButtonArrow from "@/assets/icons/leftNavigation/arrow.png";

interface NavigationButtonProps {
  delay?: number;
  isButtonClicked: boolean;
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
}

const NavigationButton = ({
  delay = 2,
  isButtonClicked,
  setIsButtonClicked,
}: NavigationButtonProps) => {
  return (
    <motion.div
      className={styles.buttonWrapper}
      variants={slideRight(2, delay, -40)}
      initial="hidden"
      animate="visible"
      whileHover={buttonEffect.hover}
      whileTap={buttonEffect.tap}
      onClick={() => setIsButtonClicked(!isButtonClicked)}
    >
      <img
        src={leftNavigationButtonFrame.src}
        alt="left navigation button frame"
      />
      <div className={styles.buttonArrow}>
        {isButtonClicked ? (
          <motion.img
            variants={rotation(0, 180)}
            initial="hidden"
            animate="visible"
            src={leftNavigationButtonArrow.src}
            alt="left navigation button arrow"
          />
        ) : (
          <motion.img
            variants={rotation(180, 0)}
            initial="hidden"
            animate="visible"
            src={leftNavigationButtonArrow.src}
            alt="left navigation button arrow"
          />
        )}
      </div>
    </motion.div>
  );
};

export default NavigationButton;

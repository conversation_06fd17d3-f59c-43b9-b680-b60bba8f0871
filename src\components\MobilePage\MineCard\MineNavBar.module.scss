@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$card-width: 100%;

.wrapper {
  width: $card-width;
}

.navbar {
  width: $card-width;
  @include row-between;
  font-family: "G<PERSON>lam<PERSON>", sans-serif;
  letter-spacing: 1px;
  margin: $margin-sm 0;
  padding: 0 $padding-sm;

  .navButton {
    background-color: $color-black-transparent;
    display: inline-block;
    cursor: pointer;
    color: $color-primary-transparent-contrast;
    font-size: $font-size-xs;
    font-weight: $font-weight-extralight;
    text-decoration: none;
  }
  button:focus {
    font-size: $font-size-md;
  }
  .active {
    transform: translateY(-2px);
    color: $color-primary;
    font-weight: $font-weight-light;
    font-size: $font-size-sm;
  }
}

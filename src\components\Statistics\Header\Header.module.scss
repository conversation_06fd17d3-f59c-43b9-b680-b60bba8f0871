@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  padding: 2rem;
  gap: 1rem;
  @include row-center;

  @media screen and (max-width: 1023px) {
    @include col-center;
  }

  .icon {
    width: 256px;
    height: 256px;
    object-fit: cover;
  }

  .description {
    width: 100%;

    h1 {
      font-size: $font-size-5xl;
      font-weight: $font-weight-semibold;
      color: $color-primary;
    }

    h3 {
      font-size: $font-size-xl;
      font-weight: $font-weight-extralight;
      color: white;

      span {
        color: $color-warning;
      }
    }
  }
}

import { minesDetails } from "@/constants/mineDetails";
import MineCard from "../../MinesNavigation/MineCard/MineCard";
import { useAreaBought } from "./hooks/useAreaBought";
import { useEffect } from "react";
import { showError } from "@/lib/notification";

const AreaBought = () => {
  const { mineOwnedStatus } = useAreaBought();

  // useEffect(() => {
  //   if (mineOwnedStatus.length > 0 && !mineOwnedStatus.includes(true)) {
  //     showError("You don't own any mines");
  //   }
  // }, [mineOwnedStatus]);

  return (
    <>
      {minesDetails.map((mine, index) => {
        if (!mineOwnedStatus[index]) return null;
        return (
          <MineCard
            delay={(index + 0.1) * 0.5}
            information={mine}
            key={index}
            isLaunchSection
          />
        );
      })}
    </>
  );
};

export default AreaBought;

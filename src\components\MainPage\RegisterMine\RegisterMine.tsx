"use client";
import React, { useEffect, useState } from "react";
import styles from "./RegisterMine.module.scss";
import { SubmitHandler, useForm } from "react-hook-form";
import requestBody from "./requestBody.json";
import { showError, showSuccess } from "@/lib/notification";
import { uploadMineFormData } from "@/actions/uploadMineFormData";
import doubleRing from "@/assets/icons/bottomNavigation/doubleRing.svg";

type RegisterMineFormData = {
  contactName: string;
  abn: string;
  companyName: string;
  website: string;
  email: string;
  contactNumber: string;
  tenement: number;
  tenementSelection: string;
  tenementFile: FileList;
  ppt: string;
  pptFile: FileList;
  technicalReport: string;
  technicalReportFile: FileList;
  financialReport: string;
  financialReportFile: FileList;
  valuation: number;
};

type TRequestBody = typeof requestBody;

const Divider = () => <div className={styles.divider} />;
const convertToBase64 = (
  file: File,
  setFile: React.Dispatch<React.SetStateAction<string>>,
) => {
  var reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = () => {
    setFile(reader.result as string);
  };
  reader.onerror = (error) => {
    console.log("Error: ", error);
  };
};

const RegisterMine = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<RegisterMineFormData>();

  const onSubmit: SubmitHandler<RegisterMineFormData> = async (data) => {
    try {
      setIsSubmittingForm(true);
      let body: TRequestBody = {
        companyName: data.companyName,
        companyWebsite: data.website,
        companyABN: data.abn,
        contactName: data.contactName,
        contactEmail: data.email,
        contactNumber: data.contactNumber,
        tenement: data.tenement,
        tenementType: data.tenementSelection,
        tenementReport,
        hasPPT: hasPpt ? 1 : 0,
        hasTechnicalReport: hasTechnicalReport ? 1 : 0,
        hasFinancialReport: hasFinancialreport ? 1 : 0,
        currentValuation: data.valuation,
        pptAttachment,
        technicalReportAttachment,
        financialReportAttachment,
      };
      await uploadMineFormData(body);
      showSuccess("Mine registered successfully");
      reset();
    } catch (err) {
      showError("Please submit again");
    } finally {
      setIsSubmittingForm(false);
    }
  };

  const [hasPpt, setHasPpt] = useState(false);
  const [hasTechnicalReport, setHasTechnicalReport] = useState(false);
  const [hasFinancialreport, setHasFinancialreport] = useState(false);
  const [tenementReport, setTenementReport] = useState("");
  const [pptAttachment, setPptAttachment] = useState("");
  const [technicalReportAttachment, setTechnicalReportAttachment] =
    useState("");
  const [financialReportAttachment, setFinancialReportAttachment] =
    useState("");
  const [isSubmittingForm, setIsSubmittingForm] = useState(false);

  useEffect(() => {
    if (!hasPpt) {
      setPptAttachment("");
    }
    if (!hasTechnicalReport) {
      setTechnicalReportAttachment("");
    }
    if (!hasFinancialreport) {
      setFinancialReportAttachment("");
    }
  }, [hasPpt, hasTechnicalReport, hasFinancialreport]);

  return (
    <div className={styles.container}>
      <div className={styles.formContainer}>
        {isSubmittingForm ? (
          <div className={styles.loadingOverlay}>
            <h1>Submitting...</h1>
            <img
              src={doubleRing.src}
              width={32}
              height={32}
              alt="loading icon"
            />
          </div>
        ) : null}
        <h1>Register Mine</h1>
        <Divider />

        <div className={styles.formDataContainer}>
          <form onSubmit={handleSubmit(onSubmit)} autoComplete="off">
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Company Name</label>
                <input
                  placeholder="Enter Company Name"
                  {...register("companyName", {
                    required: "Please provide a company name",
                  })}
                />
                <p>{errors.companyName?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Company Website</label>
                <input
                  placeholder="Enter Company Website"
                  {...register("website", {
                    required: "Please provide a comapny website",
                    pattern: {
                      value:
                        /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi,
                      message: "Please provide a valid website",
                    },
                  })}
                />
                <p>{errors.website?.message}</p>
              </div>
            </div>
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Company ABN</label>
                <input
                  placeholder="Enter Company ABN"
                  {...register("abn", {
                    required: "Please provide a ABN",
                    pattern: {
                      value: /^(\d *?){11}$/,
                      message: "Please provide a valid ABN",
                    },
                  })}
                />
                <p>{errors.abn?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Contact Name</label>
                <input
                  placeholder="Enter Contact Name"
                  {...register("contactName", {
                    required: "Please provide contact name",
                  })}
                />
                <p>{errors.contactName?.message}</p>
              </div>
            </div>
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Contact Email</label>
                <input
                  placeholder="Enter Contact Email"
                  {...register("email", {
                    required: "Please provide contact email",
                    pattern: {
                      value:
                        /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                      message: "Please provide a valid email",
                    },
                  })}
                />
                <p>{errors.email?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Contact Number</label>
                <input
                  placeholder="Enter Contact Number"
                  {...register("contactNumber", {
                    required: "Please provide contact number",
                    pattern: {
                      value:
                        /^(?:\+?(61))? ?(?:\((?=.*\)))?(0?[2-57-8])\)? ?(\d\d(?:[- ](?=\d{3})|(?!\d\d[- ]?\d[- ]))\d\d[- ]?\d[- ]?\d{3})$/gm,
                      message: "Please provide a valid contact number",
                    },
                  })}
                />
                <p>{errors.contactNumber?.message}</p>
              </div>
            </div>
            <div className={styles.formDataGroup}>
              <div className={styles.formSection}>
                <label>Tenement</label>
                <input
                  placeholder="Number of tenements"
                  {...register("tenement", {
                    required: "Please provide number of tenements",
                    min: {
                      value: 1,
                      message: "It should be at least 1 tenement",
                    },
                  })}
                  type="number"
                  defaultValue={1}
                  min={1}
                />
                <p>{errors.tenement?.message}</p>
              </div>
              <div className={styles.formSection}>
                <label>Tenement type</label>
                <select
                  {...register("tenementSelection", { required: true })}
                  defaultValue={"exploring"}
                >
                  <option value="exploring">Exploring</option>
                  <option value="developed" disabled>
                    Developed
                  </option>
                </select>
                <p>{errors.tenementSelection?.message}</p>
              </div>
            </div>
            <div className={styles.formSection}>
              <label>Submit tenement report</label>
              <input
                {...register("tenementFile", {
                  required: "Please upload tenement report",
                })}
                type="file"
                onChange={(e) => {
                  if (e.target.files) {
                    convertToBase64(e.target.files[0], setTenementReport);
                  }
                }}
              />
              <p>{errors.tenementFile?.message}</p>
            </div>

            <div className={styles.formSection}>
              <label>Do you have a PPT?</label>
              <select
                {...register("ppt", { required: true })}
                defaultValue={"no"}
                onChange={() => {
                  setHasPpt(!hasPpt);
                }}
              >
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
              <p>{errors.ppt?.message}</p>
            </div>
            {hasPpt ? (
              <div className={styles.formSection}>
                <label>Submit PPT</label>
                <input
                  {...register("pptFile", {
                    required: "Please upload ppt",
                  })}
                  type="file"
                  onChange={(e) => {
                    if (e.target.files) {
                      convertToBase64(e.target.files[0], setPptAttachment);
                    }
                  }}
                />
                <p>{errors.pptFile?.message}</p>
              </div>
            ) : null}
            <div className={styles.formSection}>
              <label>Do you have a technical report?</label>
              <select
                {...register("technicalReport", { required: true })}
                defaultValue={"no"}
                onChange={() => {
                  setHasTechnicalReport(!hasTechnicalReport);
                }}
              >
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
              <p>{errors.technicalReport?.message}</p>
            </div>
            {hasTechnicalReport ? (
              <div className={styles.formSection}>
                <label>Submit technical report</label>
                <input
                  {...register("technicalReportFile", {
                    required: "Please upload technical report",
                  })}
                  type="file"
                  onChange={(e) => {
                    if (e.target.files) {
                      convertToBase64(
                        e.target.files[0],
                        setTechnicalReportAttachment,
                      );
                    }
                  }}
                />
                <p>{errors.technicalReportFile?.message}</p>
              </div>
            ) : null}
            <div className={styles.formSection}>
              <label>Do you have a financial report?</label>
              <select
                {...register("financialReport", { required: true })}
                defaultValue={"no"}
                onChange={() => {
                  setHasFinancialreport(!hasFinancialreport);
                }}
              >
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
              <p>{errors.financialReport?.message}</p>
            </div>
            {hasFinancialreport ? (
              <div className={styles.formSection}>
                <label>Submit financial report</label>
                <input
                  {...register("financialReportFile", {
                    required: "Please upload financial report",
                  })}
                  type="file"
                  onChange={(e) => {
                    if (e.target.files) {
                      convertToBase64(
                        e.target.files[0],
                        setFinancialReportAttachment,
                      );
                    }
                  }}
                />
                <p>{errors.financialReportFile?.message}</p>
              </div>
            ) : null}
            <div className={styles.formSection}>
              <label>Do you have current valuation?</label>
              <div className={styles.currentValuation}>
                <span>$</span>
                <input
                  {...register("valuation", {
                    required: "Please provide current valuation",
                    min: {
                      value: 1,
                      message: "It should be at least $1 valuation",
                    },
                  })}
                  type="number"
                  defaultValue={1}
                  min={1}
                />
                <p>{errors.valuation?.message}</p>
              </div>
            </div>
            <input type="submit" className={styles.submitButton} />
          </form>
        </div>
      </div>
    </div>
  );
};

export default RegisterMine;

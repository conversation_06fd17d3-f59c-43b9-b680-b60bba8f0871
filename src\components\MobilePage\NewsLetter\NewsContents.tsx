import { useSnapshot } from "valtio";
import styles from "./NewsContents.module.scss";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import { NewsData, newsDataStore } from "@/stores/newsData";
import moment from "moment";
import { useEffect, useState } from "react";

interface NewsContentsProps {
  isShowMoreButtonClicked: boolean;
}

const NewsContents = ({ isShowMoreButtonClicked }: NewsContentsProps) => {
  const newsDataSnapshot = useSnapshot(newsDataStore);
  const [newsData, setNewsData] = useState<NewsData>([
    ...newsDataSnapshot.newsData,
  ]);

  const sortedNewsDataByDate = newsData.sort(
    (date1, date2) =>
      new Date(date2.timestamp).getTime() - new Date(date1.timestamp).getTime(),
  );

  useEffect(() => {
    setNewsData([...newsDataSnapshot.newsData]);
  }, [newsDataSnapshot.newsData]);

  return (
    <>
      {/* <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>newsletter</h1>
          <h2 className={styles.subTitle}>list of updated news</h2>
        </div>
        <img src={dotDotDot.src} alt="dot dot dot icon" />
      </div> */}
      <div className={styles.scrollArea} style={{ height: "84%" }}>
        {sortedNewsDataByDate.map((news, index) => {
          const timeDifferent = moment(news.timestamp).fromNow();
          return (
            <div
              key={index}
              className={styles.bodyWrapper}
              onClick={() => window.open(news.link)}
            >
              <div className={styles.time}>{timeDifferent}</div>
              <div className={styles.bodyText}>{news.title}</div>
            </div>
          );
        })}
        <br />
      </div>
    </>
  );
};

export default NewsContents;

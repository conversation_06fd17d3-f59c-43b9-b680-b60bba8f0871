import { minesDetails } from "@/constants/mineDetails";
import styles from "./PurchasedMine.module.scss";
import buyButton from "./icons/buyButton.png";
import { useLayoutEffect, useState } from "react";
import { motion } from "framer-motion";
import { useSnapshot } from "valtio";
import { tradeModalStore } from "@/stores/tradeModal";
import { mineCardStore } from "@/stores/mineCard";
import useMineTokenId from "../Mineralinformation/TradeDashboard/hooks/useMineTokenId";
import { useNetwork } from "wagmi";

const PurchaseMine = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const tradeModalSnapshot = useSnapshot(tradeModalStore);
  const [showOverlay, setShowOverlay] = useState(false);
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const { getSelectedMineId } = useMineTokenId();
  const { chain } = useNetwork();

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  return (
    <div className={styles.container}>
      <div className={styles.innerContainer}>
        <div className={styles.mineInfo}>
          <img src={selectedMine.mineImages[1]} alt="mine image" />
          <div className={styles.header}>
            <div className={styles.titleSection}>
              <h2>ID: {asteroidMineNumber}</h2>
              {/* <span className={styles.price}>0.45 ETH</span> */}
            </div>
            <div className={styles.location}>
              <span className={styles.city}>
                {selectedMine.mineMineral}, {selectedMine.mineLocation}
              </span>
              <h3>{selectedMine.name}</h3>
            </div>
          </div>

          <div className={styles.details}>
            <div className={styles.detailItem}>
              <span className={styles.label}>MINE LOCATION</span>
              <span className={styles.value}>{selectedMine.location[0]}</span>
            </div>

            <div className={styles.detailItem} style={{ marginBottom: "0" }}>
              <span className={styles.label}>MINING AREA</span>
              <span className={styles.value}>
                {selectedMine.area === "" ? "N/A" : selectedMine.area}
              </span>
            </div>

            {/* <div className={styles.detailItem} style={{ marginBottom: "0" }}>
              <span className={styles.label}>MINING EQUIPMENT</span>
              <span className={styles.value}>
                Diamond Drilling Rigs And Blasting Equipment
              </span>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseMine;

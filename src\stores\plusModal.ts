import { headerDetails } from "@/components/MainPage/PlusModal/PlusModalHeader/PlusModalHeader";
import { proxy } from "valtio";
import { devtools } from "valtio/utils";

export const plusModalStore = proxy({
  isOpenPlusModal: false,
  setIsOpenPlusModal: (status: boolean) => {
    plusModalStore.isOpenPlusModal = status;
  },
  selectedTitle: headerDetails[0].title,
  setSelectedTitle: (title: string) => {
    plusModalStore.selectedTitle = title;
  },
});

devtools(plusModalStore, {
  name: "plusModalStore",
  enabled: false,
});

"use client";

import { universeAnimationStore } from "@/stores/universeAnimation";
import styles from "./Explore.module.scss";
import { useSnapshot } from "valtio";
import { MutableRefObject } from "react";
import { CameraControls } from "@react-three/drei";
import { useRouter } from "next/navigation";
import { scrollContainerStore } from "@/stores/scrollContainer";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { TypeAnimation } from "react-type-animation";
import { useMediaQuery } from "usehooks-ts";

type ExploreProps = {
  cameraControlsRef: MutableRefObject<CameraControls | undefined>;
  replace: ReturnType<typeof useRouter>["replace"];
};

const Explore = ({ cameraControlsRef, replace }: ExploreProps) => {
  const universeAnimationSnapshot = useSnapshot(universeAnimationStore);
  const scrollContainerSnapshot = useSnapshot(scrollContainerStore);
  const exploreContainerHeight =
    scrollContainerSnapshot.containerHeight -
    scrollContainerSnapshot.exploreContainerHeight;
  const { inView: buttonInView, ref: buttonRef } = useInView({
    threshold: 0.01,
  });

  const isTablet = useMediaQuery("(max-width: 1279px)");
  const isMobile = useMediaQuery("(max-width: 767px)");

  const handleFinalAnimation = async () => {
    universeAnimationSnapshot.setShowFinalAnimation(true);
    await cameraControlsRef.current?.setLookAt(0, 0, -193, 0, 0, -200, true);
    await cameraControlsRef.current?.rotate(-0.25, 0, true);
    cameraControlsRef.current?.setLookAt(-1.25, 0, -196, -1.25, 0, -200, true);

    replace("/main");
  };
  // console.log(exploreContainerHeight);
  return (
    <>
      <div
        className={styles.buttonWrapper}
        style={{
          height:
            isMobile || isTablet
              ? exploreContainerHeight / 2.5
              : exploreContainerHeight,
          opacity: buttonInView ? 1 : undefined,
        }}
      >
        {buttonInView ? (
          <TypeAnimation
            sequence={[
              "",
              1000,
              "Fair, transparent, reliable mining projects.",
              3000,
            ]}
            className={styles.animatedTypedText}
          />
        ) : null}
        {/* <p>
          The vision of Asteroid is to build a fair, transparent, reliable and
          productive incubation center for physical mining projects. it drives
          the development of web3 technology and physical mining, and achieves a
          perfect fusion between physical projects and digital assets.
        </p> */}
        {/* <div style={{ width: "40%" }}>
          <EndingText
            title="Vision"
            content="Create a fair, transparent, reliable, and productive incubation center for physical mining projects."
          />
          <EndingText
            title="Goal"
            content="Drive the development of web3 technology and physical mining."
          />
          <EndingText
            title="Objective"
            content="Achieve a seamless integration between physical projects and digital assets."
          />
        </div> */}
        <div ref={buttonRef}>
          <button
            className={styles.exploreButton}
            onClick={handleFinalAnimation}
          >
            Start your Journey
          </button>
          <h2>Be a part of the digital future</h2>
        </div>
      </div>
    </>
  );
};

const EndingText = ({ title = "", content = "" }) => {
  return (
    <p>
      <span className={styles.endingTextTitle}>{title}:</span>
      <span className={styles.endingTextContent}>{content}</span>
    </p>
  );
};

export default Explore;

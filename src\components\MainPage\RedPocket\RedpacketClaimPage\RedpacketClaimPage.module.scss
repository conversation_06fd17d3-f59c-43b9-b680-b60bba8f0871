@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 400px;
  @include col-center;
  gap: 1rem;
}

.buttonContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 24px 0;
}

.claimButton,
.closeButton {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
    opacity: 0.7;

    &:hover {
      transform: none;
    }

    &:active {
      transform: none;
    }
  }
}

.claimButton {
  background-color: #17c0dd;
  color: white;
}

.closeButton {
  background-color: #ff4444;
  color: white;
  backdrop-filter: blur(4px);
}

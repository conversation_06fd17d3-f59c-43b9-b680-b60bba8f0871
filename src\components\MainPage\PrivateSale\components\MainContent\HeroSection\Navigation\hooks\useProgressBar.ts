import { privateSaleABI } from "@/constants/abis/PrivateSaleABI";
import { networkConfigs } from "@/constants/networkConfigs";
import { useLayoutEffect, useState } from "react";
import { formatEther } from "viem";
import { useAccount, useContractRead, useNetwork } from "wagmi";

const useProgressBar = () => {
  const [privateSaleAddress, setPrivateSaleAddress] =
    useState<`0x${string}`>("0x");
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [targetFund, setTargetFund] = useState("");
  const [avaliableNativeToken, setAvaliableNativeToken] = useState("");
  const [raisedAmount, setRaisedAmount] = useState("");
  const { chain } = useNetwork();
  const { isConnected } = useAccount();

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setPrivateSaleAddress(networkConfigs[chain.id].privateSaleAddress);
    } else {
      setPrivateSaleAddress("0x");
    }
  }, [chain]);

  useContractRead({
    address: privateSaleAddress,
    abi: privateSaleABI,
    functionName: "getSaleStatus",
    enabled: isConnected && privateSaleAddress !== "0x",
    watch: true,
    onSuccess: (data) => {
      setTargetFund(formatEther(data[0]));
      setProgressPercentage(Number(data[5]) / 100);
      setRaisedAmount(formatEther(data[1]));

      setAvaliableNativeToken(
        data[0] - data[1] >= 0n ? formatEther(data[0] - data[1]) : "0",
      );
    },
  });

  return { progressPercentage, targetFund, avaliableNativeToken, raisedAmount };
};

export default useProgressBar;

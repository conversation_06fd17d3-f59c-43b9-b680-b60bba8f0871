import { connectWalletModalStore } from "@/stores/connectWalletModal";
import styles from "./ConnectWalletModal.module.scss";
import { motion } from "framer-motion";
import { useSnapshot } from "valtio";
import {
  bottomUpFadeIn,
  buttonEffect,
  fadeIn,
  scaleUp,
} from "@/animations/animations";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import coinbaseIcon from "@/assets/icons/connectWallet/coinbaseIcon.png";
import walletConnectIcon from "@/assets/icons/connectWallet/walletConnectIcon.svg";
import metamaskIcon from "@/assets/icons/connectWallet/metamaskIcon.png";
import okxIcon from "@/assets/icons/connectWallet/okxWalletIcon.svg";
import trustWalletIcon from "@/assets/icons/connectWallet/trustWalletIcon.svg";
import rabbyWalletIcon from "@/assets/icons/connectWallet/rabbyWalletIcon.svg";
import binanceWalletIcon from "@/assets/icons/connectWallet/binanceWalletIcon.svg";
import bitgetWalletIcon from "@/assets/icons/connectWallet/bitgetWalletIcon.svg";
import { useAccount, useConnect } from "wagmi";
import { MouseEventHandler, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { BaseError } from "viem";
import { showError, showSuccess } from "@/lib/notification";
import TermsAndConditions from "./TermsAndConditions/TermsAndConditions";
import {
  getDirectMetaMaskProvider,
  diagnoseWalletProviders,
  registerGlobalDiagnostics,
  forceMetaMaskOnly,
} from "@/lib/walletUtils";

interface WalletSelectProps {
  walletName: string;
  icon: string;
  isPopular?: boolean;
  onClick: MouseEventHandler<HTMLDivElement> | undefined;
}

const ConnectWalletModal = () => {
  const { connector, isConnected } = useAccount();
  const { connect, connectors, error, isLoading, pendingConnector } =
    useConnect({
      onError: (error) => {
        showError((error as BaseError).shortMessage ?? error.message);
      },
      onSuccess: () => {
        connectWalletModalSnapshot.setIsOpenConnectWalletModal(false);
        showSuccess("Wallet connected");
      },
    });

  const getConnector = (id: string) =>
    connectors.filter((connector) => connector.id === id)[0];

  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);

  const [hasUserReadTandC, setHasUserReadTandC] = useState(false);
  const [showMoreWallets, setShowMoreWallets] = useState(false);

  // useEffect(() => {
  //   registerGlobalDiagnostics();
  //   console.log("已在MainPage的ConnectWalletModal组件中注册全局钱包诊断工具");
  //   if (isConnected) {
  //     connectWalletModalSnapshot.setIsOpenConnectWalletModal(false);
  //     showSuccess("Wallet connected");
  //   }
  // }, [isConnected]);

  const WalletSelect = ({
    walletName,
    icon,
    isPopular,
    onClick,
  }: WalletSelectProps) => (
    <div className={styles.walletSelect} onClick={onClick}>
      <div>
        <img src={icon} alt={walletName} />
        {walletName}
      </div>
      {isPopular ? <h1>popular</h1> : null}
    </div>
  );

  const handleOKX = async () => {
    try {
      const isOKXInstalled = typeof window !== "undefined" && window.okxwallet;
      if (isOKXInstalled) {
        const connector = connectors.find((c) => c.name === "OKX Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("OKX Wallet connector not found");
        }
      } else {
        // 跳转到OKX钱包官方浏览器插件下载页面
        window.open("https://www.okx.com/web3/extension", "_blank");
      }
    } catch (error) {
      console.error("OKX connection error:", error);
      showError("Failed to connect to OKX Wallet");
    }
  };

  const handleMetaMask = async () => {
    try {
      const isMetaMaskInstalled =
        typeof window.ethereum !== "undefined" && window.ethereum.isMetaMask;
      if (isMetaMaskInstalled) {
        const connector = getConnector("metaMask");
        connect({ connector });
      } else {
        // 跳转到MetaMask官方浏览器插件下载页面
        window.open("https://metamask.io/download/", "_blank");
      }
    } catch (error) {
      console.error("MetaMask error:", error);
      showError("Failed to connect with MetaMask");
    }
  };

  const handleWalletConnect = async () => {
    try {
      const connector = connectors.find((c) => c.id === "walletConnect");
      if (connector) {
        await connect({ connector });
      } else {
        showError("WalletConnect connector not found");
      }
    } catch (error) {
      console.error("WalletConnect error:", error);
      showError("Failed to connect with WalletConnect");
    }
  };

  const handleTrustWallet = async () => {
    try {
      const isTrustWalletInstalled =
        typeof window !== "undefined" && window.trustwallet;
      if (isTrustWalletInstalled) {
        const connector = connectors.find((c) => c.name === "Trust Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Trust Wallet connector not found");
        }
      } else {
        // 跳转到Trust Wallet官方浏览器插件下载页面
        window.open("https://trustwallet.com/browser-extension", "_blank");
      }
    } catch (error) {
      console.error("Trust Wallet error:", error);
      showError("Failed to connect to Trust Wallet");
    }
  };

  const handleRabbyWallet = async () => {
    try {
      const isRabbyInstalled =
        typeof window.ethereum !== "undefined" && window.ethereum.isRabby;
      if (isRabbyInstalled) {
        const connector = connectors.find((c) => c.name === "Rabby Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Rabby Wallet connector not found");
        }
      } else {
        // 跳转到Rabby钱包官方浏览器插件下载页面
        window.open("https://rabby.io/", "_blank");
      }
    } catch (error) {
      console.error("Rabby Wallet error:", error);
      showError("Failed to connect to Rabby Wallet");
    }
  };

  const handleBinanceWallet = async () => {
    try {
      const isBinanceWalletInstalled =
        typeof window !== "undefined" && window.BinanceChain;
      if (isBinanceWalletInstalled) {
        const connector = connectors.find((c) => c.name === "Binance Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Binance Wallet connector not found");
        }
      } else {
        // 跳转到Binance钱包官方浏览器插件下载页面
        window.open("https://www.bnbchain.org/en/binance-wallet", "_blank");
      }
    } catch (error) {
      console.error("Binance Wallet error:", error);
      showError("Failed to connect to Binance Wallet");
    }
  };

  const handleBitgetWallet = async () => {
    try {
      const isBitgetWalletInstalled =
        typeof window !== "undefined" && window.bitkeep?.ethereum;
      if (isBitgetWalletInstalled) {
        const connector = connectors.find((c) => c.name === "Bitget Wallet");
        if (connector) {
          await connect({ connector });
        } else {
          showError("Bitget Wallet connector not found");
        }
      } else {
        // 跳转到Bitget钱包官方浏览器插件下载页面
        window.open("https://web3.bitget.com/en/wallet-download", "_blank");
      }
    } catch (error) {
      console.error("Bitget Wallet error:", error);
      showError("Failed to connect to Bitget Wallet");
    }
  };

  const handleCoinbaseWallet = async () => {
    try {
      const connector = getConnector("coinbaseWallet");

      // 直接连接，Coinbase会自动判断是否需要显示QR码
      await connect({ connector });
    } catch (error) {
      console.error("Coinbase Wallet error:", error);
      showError("Failed to connect with Coinbase Wallet");
    }
  };

  return (
    <motion.div
      className={styles.container}
      initial={{ opacity: 0, top: "50%", left: "50%" }}
      animate={{ opacity: 1, transform: "translate(-50%, -50%)" }}
      exit={{ opacity: 0, transform: "translate(25%, 25%)" }}
      transition={{ duration: 0.3 }}
    >
      <div className={styles.modalFrame}>
        <div
          className={styles.closeButton}
          onClick={() =>
            connectWalletModalSnapshot.setIsOpenConnectWalletModal(false)
          }
        >
          <img src={crossIcon.src} alt="cross icon" />
        </div>
        <div className={styles.connectWallet}>
          <div className={styles.titleWrapper}>
            <h1>connect your wallet</h1>
            {!window.ethereum ? (
              <h2>
                If you don&apos;t have a wallet, you can click install now and
                create one.{" "}
                <a
                  href="https://chromewebstore.google.com/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn?pli=1"
                  target="_blank"
                >
                  install now
                </a>
              </h2>
            ) : null}
          </div>
          {hasUserReadTandC ? (
            <>
              <WalletSelect
                walletName="OKX Wallet"
                icon={okxIcon.src}
                isPopular
                onClick={handleOKX}
              />
              <WalletSelect
                walletName="Metamask"
                icon={metamaskIcon.src}
                isPopular
                onClick={handleMetaMask}
              />
              <WalletSelect
                walletName="Coinbase Wallet"
                icon={coinbaseIcon.src}
                onClick={() => handleCoinbaseWallet()}
              />
              <WalletSelect
                walletName="WalletConnect"
                icon={walletConnectIcon.src}
                onClick={handleWalletConnect}
              />

              {showMoreWallets ? (
                <>
                  <WalletSelect
                    walletName="Trust Wallet"
                    icon={trustWalletIcon.src}
                    onClick={handleTrustWallet}
                  />
                  <WalletSelect
                    walletName="Rabby Wallet"
                    icon={rabbyWalletIcon.src}
                    onClick={handleRabbyWallet}
                  />
                  <WalletSelect
                    walletName="Binance Wallet"
                    icon={binanceWalletIcon.src}
                    onClick={handleBinanceWallet}
                  />
                  <WalletSelect
                    walletName="Bitget Wallet"
                    icon={bitgetWalletIcon.src}
                    onClick={handleBitgetWallet}
                  />
                  <div
                    className={styles.showMoreWallets}
                    onClick={() => setShowMoreWallets(false)}
                  >
                    Show less wallets
                  </div>
                </>
              ) : (
                <div
                  className={styles.showMoreWallets}
                  onClick={() => setShowMoreWallets(true)}
                >
                  Show more wallets
                </div>
              )}
            </>
          ) : (
            <TermsAndConditions setHasUserReadTandC={setHasUserReadTandC} />
            // <div className={styles.termAndCondition}>
            //   <h1>Important</h1>
            //   <h3>
            //     Please click{" "}
            //     <a
            //       href="https://asteroid-x-1.gitbook.io/asteroid-x-product-book/terms-of-service"
            //       target="_blank"
            //       onClick={() => setHasUserReadTandC(true)}
            //     >
            //       here
            //     </a>{" "}
            //     to read our terms and conditions before connecting your wallet
            //   </h3>
            // </div>
          )}
          {/* <WalletSelect
            walletName="WallectConnect"
            icon={walletConnectIcon.src}
          /> */}
        </div>
      </div>
    </motion.div>
  );
};

export default ConnectWalletModal;

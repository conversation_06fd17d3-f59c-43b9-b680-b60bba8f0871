import { minesDetails } from "@/constants/mineDetails";
import { proxy } from "valtio";
import { devtools } from "valtio/utils";

const DEFAULT_MINE = "pcgold";

export const mineCardStore = proxy({
  selectedMine: minesDetails.filter(
    (mine) => mine.shortName === DEFAULT_MINE,
  )[0].name,
  setSelectedMine: (mine: string) => {
    mineCardStore.selectedMine = mine;
  },
  isShowOverlay: false,
  setIsShowOverlay: (isShowOverlay: boolean) => {
    mineCardStore.isShowOverlay = isShowOverlay;
  },
});

devtools(mineCardStore, {
  name: "mineCardStore",
  enabled: false,
});

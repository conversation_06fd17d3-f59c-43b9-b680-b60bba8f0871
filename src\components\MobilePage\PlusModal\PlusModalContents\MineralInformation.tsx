import { memo } from "react";
import { useSnapshot } from "valtio";
import styles from "./MineralInformation.module.scss";
import { mineCardStore } from "@/stores/mineCard";
import { plusModalStore } from "@/stores/plusModal";
import { minesDetails } from "@/constants/mineDetails";
import { headerDetails } from "../PlusModalHeader/PlusModalHeader";
import Roadmap from "@/components/MainPage/PlusModal/PlusModalMainContents/Mineralinformation/Roadmap/Roadmap";
import Barchart3d from "@/components/UI/Charts/Barchart3d/Barchart3d";
import { useEffect, useRef } from "react";
import zephyrProjectOverview from "@/assets/images/zephyrProjectOverview.png";
import mtProjectOverview from "@/assets/images/mtProjectOverview.webp";
import matsaProjectOverview from "@/assets/images/matsaProjectOverview.webp";
import jimProjectOverview from "@/assets/images/jimProjectOverview.webp";
import pcgoldProjectOverview from "@/assets/images/pcgoldProjectOverview.jpg";
import menziesProjectOverview from "@/assets/images/menziesProjectOverview.webp";

import MineModelPcgold from "@/components/MainPage/MineModels/MineModelPcgold";

const Mineralinformation = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const containerRef = useRef<HTMLDivElement>(null);
  const isSpecialEvent = selectedMine.shortName === "hskreward";
  const isPcgold = selectedMine.shortName === "pcgold";

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, [plusModalSnapshot.selectedTitle]);

  const selected3dModal = () => {
    switch (selectedMine.name) {
      case minesDetails[0].name: {
        return (
          <img
            src={mtProjectOverview.src}
            alt="mt image"
            width={"100%"}
            height={"100%"}
            style={{ objectFit: "cover" }}
          />
        );
      }
      case minesDetails[1].name: {
        return (
          <img
            src={matsaProjectOverview.src}
            alt="matsa image"
            width={"100%"}
            height={"100%"}
            style={{ objectFit: "cover" }}
          />
        );
      }
      case minesDetails[2].name: {
        return (
          <img
            src={zephyrProjectOverview.src}
            alt="zephyr image"
            width={"100%"}
            height={"100%"}
            style={{ objectFit: "cover" }}
          />
        );
      }
      case minesDetails[3].name: {
        return (
          <img
            src={jimProjectOverview.src}
            alt="jim image"
            width={"100%"}
            height={"100%"}
            style={{ objectFit: "cover" }}
          />
        );
      }
      case minesDetails[4].name: {
        return (
          <img
            src={pcgoldProjectOverview.src}
            alt="pcgold image"
            width={"100%"}
            height={"100%"}
            style={{ objectFit: "cover" }}
          />
        );
      }
      case minesDetails[5].name: {
        return (
          <img
            src={menziesProjectOverview.src}
            alt="pcgold image"
            width={"100%"}
            height={"100%"}
            style={{ objectFit: "cover" }}
          />
        );
      }
      default:
        return null;
    }
  };

  const selectedHeader = () => {
    switch (plusModalSnapshot.selectedTitle) {
      case headerDetails[0].title: {
        return <Mineralinformation />;
      }
      case headerDetails[1].title: {
        return <CompanyDetails />;
      }
      case headerDetails[2].title: {
        return <InvestorZone />;
      }
      default:
        return null;
    }
  };

  const Mineralinformation = () => {
    const hasMineralInformation = selectedMine.mineralInformation.length > 0;
    return (
      <>
        <div
          className={
            hasMineralInformation
              ? styles.mineralInformation
              : styles.mineralInformationComingSoon
          }
        >
          {hasMineralInformation ? (
            <>
              {isPcgold ? <MineModelPcgold /> : null}
              {selectedMine.mineralInformation.map((details, index) => (
                <div key={index} className={styles.mineralInformationWrapper}>
                  <h1>{details.title}</h1>

                  {!isSpecialEvent ? (
                    <p>{details.text}</p>
                  ) : (
                    <h4>{details.text}</h4>
                  )}

                  {details.image !== "" ? (
                    <>
                      <div style={{ margin: "20px 0" }} />
                      <img
                        src={details.image}
                        alt="mineral information image"
                      />
                    </>
                  ) : null}
                </div>
              ))}
              {!isSpecialEvent ? <Roadmap /> : null}
            </>
          ) : (
            "Coming Soon"
          )}
        </div>
      </>
    );
  };

  const CompanyDetails = () => {
    const hasCompanyDetails = selectedMine.companyDetails.length > 0;
    return (
      <>
        <div
          className={
            hasCompanyDetails
              ? styles.companyDetails
              : styles.companyDetailsComingSoon
          }
        >
          {hasCompanyDetails
            ? selectedMine.companyDetails.map((details, index) => (
                <div key={index} className={styles.companyDetailsWrapper}>
                  <h1>{details.title}</h1>
                  {!isSpecialEvent ? (
                    <p>{details.text}</p>
                  ) : (
                    <h4>{details.text}</h4>
                  )}
                  {details.image !== "" ? (
                    <>
                      <div style={{ margin: "20px 0" }} />
                      <img src={details.image} alt="company details image" />
                    </>
                  ) : null}
                </div>
              ))
            : "Coming Soon"}
        </div>
      </>
    );
  };
  const InvestorZone = () => {
    const hasInvestmentDetails = selectedMine.investmentDetails.length > 0;

    return (
      <>
        <div
          className={
            hasInvestmentDetails
              ? styles.investorZone
              : styles.investorZoneComingSoon
          }
        >
          {hasInvestmentDetails
            ? selectedMine.investmentDetails.map((investment, index) => (
                <div key={index} className={styles.investorZoneWrapper}>
                  <h1>{investment.title}</h1>
                  {!isSpecialEvent ? (
                    <p>{investment.text}</p>
                  ) : (
                    <h4>{investment.text}</h4>
                  )}
                  {investment.data.length > 0 ? (
                    <>
                      <div style={{ margin: "20px 0" }} />
                      <div style={{ width: "100%", height: "500px" }}>
                        <Barchart3d data={investment.data} />
                      </div>
                    </>
                  ) : null}
                </div>
              ))
            : "Coming Soon"}
        </div>
      </>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.scrollArea} ref={containerRef}>
        {selectedHeader()}
      </div>
    </div>
  );
};

export default memo(Mineralinformation);

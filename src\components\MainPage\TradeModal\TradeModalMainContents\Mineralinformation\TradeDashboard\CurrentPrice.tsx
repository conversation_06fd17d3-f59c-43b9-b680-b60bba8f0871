import { useEffect, useState } from "react";
import styles from "./TradeDashboard.module.scss";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import MakeOfferModal from "./TradeModals/MakeOfferModal/MakeOfferModal";
import QuickListModal from "./TradeModals/QuickListModal/QuickListModal";
import useApprove from "./hooks/useApprove";
import useMaxAmount from "./hooks/useMaxAmount";
import TokenPrice from "@/components/common/TokenPrice";
import useActiveListing from "./hooks/useActiveListing";
import usePurchaseListing from "./hooks/usePurchaseListing";
import CancelListingModal from "./TradeModals/CancelListingModal/CancelListingModal";
import CancelOfferModal from "./TradeModals/CancelOfferModal/CancelOfferModal";
import useCreateListing from "./hooks/useCreateListing";
import useCancelOffer from "./hooks/useCancelOffer";
import useCancelListing from "./hooks/useCancelListing";
import SelectMineModal from "./TradeModals/SelectMineModal/SelectMineModal";
import { useNetwork } from "wagmi";
import useApproveUsdt from "./hooks/useApproveUsdt";

const CurrentPrice = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const [selectedOption, setSelectedOption] = useState(selectedMine.name);
  const [isMakeOfferModalOpen, setIsMakeOfferModalOpen] = useState(false);
  const [isQuickListModalOpen, setIsQuickListModalOpen] = useState(false);
  const [isCancelListingModalOpen, setIsCancelListingModalOpen] =
    useState(false);
  const [isCancelOfferModalOpen, setIsCancelOfferModalOpen] = useState(false);
  const [isSelectMineModalOpen, setIsSelectMineModalOpen] = useState(false);
  const [purchaseListingQuantity, setPurchaseListingQuantity] = useState(1);
  const [isOpen, setIsOpen] = useState(true);

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedOption(event.target.value);
    mineCardSnapshot.setSelectedMine(event.target.value);
  };

  const { isApprovedForAll, handleApprove, isApproving, isWaitingForApprove } =
    useApprove();
  const { avaliableNfts } = useCreateListing();
  const { maxPricePerToken } = useMaxAmount();
  const [isHandlingPurchasing, setIsHandlingPurchasing] = useState(false);
  const {
    allActiveListingSortedByPrice,
    expirationTime,
    isPurchasable,
    maxPurchasableAmount,
  } = useActiveListing();

  const {
    isPurchasingListing,
    isWaitingForPurchaseListing,
    purchaseListing,
    value: usdtValue,
    refresh,
    setRefresh,
  } = usePurchaseListing({
    amount: BigInt(purchaseListingQuantity),
  });
  const {
    isApprovingUsdt,
    isWaitingForApproveUsdt,
    usdtAllowance,
    handleApproveUsdt,
  } = useApproveUsdt({ usdtValue, purchaseListing, refresh });
  const { allActiveOfferSortedByPrice } = useCancelOffer();
  const { allActiveListingSortedBySeller } = useCancelListing();
  const { chain } = useNetwork();

  useEffect(() => {
    if (
      purchaseListingQuantity > maxPurchasableAmount &&
      maxPurchasableAmount > 0
    ) {
      setPurchaseListingQuantity(maxPurchasableAmount);
    }
  }, [maxPurchasableAmount]);

  useEffect(() => {
    setIsHandlingPurchasing(
      isPurchasingListing ||
        isWaitingForPurchaseListing ||
        isApprovingUsdt ||
        isWaitingForApproveUsdt,
    );
  }, [
    isPurchasingListing,
    isWaitingForPurchaseListing,
    isApprovingUsdt,
    isWaitingForApproveUsdt,
  ]);

  useEffect(() => {
    const hasChanged =
      isHandlingPurchasing && usdtAllowance >= usdtValue && usdtValue > 0;

    if (hasChanged) {
      setRefresh((prev) => prev + 1);
    }
  }, [isHandlingPurchasing, usdtAllowance, usdtValue, setRefresh]);

  const handlePurchaseListing = () => {
    if (usdtAllowance < usdtValue) {
      handleApproveUsdt();
    } else {
      purchaseListing();
    }
  };

  return (
    <div className={styles.currentPrice}>
      <div
        className={styles.headerContainer}
        // onClick={() => setIsOpen(!isOpen)}
      >
        <h2 className={styles.mainHeader}>CURRENT PRICE</h2>
        {/* <span className={styles.toggleIcon}>{isOpen ? "-" : "+"}</span> */}
      </div>
      {isOpen && (
        <div className={styles.priceActions}>
          <div className={styles.buttons}>
            <div className={styles.buttonsGroup}>
              {allActiveListingSortedBySeller &&
              allActiveListingSortedBySeller.length !== 0 ? (
                <button
                  className={styles.sellButton}
                  onClick={() => setIsCancelListingModalOpen(true)}
                  disabled={!isApprovedForAll}
                >
                  CANCEL LISTING
                </button>
              ) : null}
              {allActiveOfferSortedByPrice.length !== 0 ? (
                <button
                  className={styles.sellButton}
                  onClick={() => setIsCancelOfferModalOpen(true)}
                  disabled={!isApprovedForAll}
                >
                  CANCEL OFFER
                </button>
              ) : null}
              {!isApprovedForAll &&
              chain &&
              selectedMine.supportedNetwork.includes(chain?.id) ? (
                <button
                  className={styles.buyButton}
                  onClick={handleApprove}
                  disabled={isApproving || isWaitingForApprove}
                >
                  {isApproving ? (
                    <span className={styles.loader} />
                  ) : isWaitingForApprove ? (
                    <span className={styles.loader} />
                  ) : (
                    "Approve All NFTs"
                  )}
                </button>
              ) : null}
            </div>
            <div className={styles.buyWrapper}>
              <button
                className={`${styles.sellButton} ${styles.pulseButton}`}
                onClick={() => setIsSelectMineModalOpen(true)}
                // disabled={!isApprovedForAll}
              >
                <span className={styles.glowText}>Select Mine</span>
                <span className={styles.buttonHighlight}></span>
              </button>
              {/* <button className={styles.buyButton}>BUY</button> */}
              {/* <select
                className={styles.autoComplete}
                value={selectedOption}
                onChange={handleSelectChange}
              >
                {minesDetails.map((mine, index) => (
                  <option value={mine.name} key={index}>
                    {mine.name}
                  </option>
                ))}
              </select> */}
              {/* <button className={styles.buyButton}>Connect Wallet</button> */}
            </div>
          </div>

          <div className={styles.detailsWrapper}>
            <div className={styles.priceInfo}>
              <div className={styles.timer}>
                <span className={styles.timerIcon}>⏱</span>
                SALE ENDS{" "}
                {expirationTime.length > 0 ? expirationTime : "--/--/--"}
              </div>
              <div className={styles.price}>
                <span className={styles.amount}>
                  {allActiveListingSortedByPrice &&
                  allActiveListingSortedByPrice.length !== 0 ? (
                    <TokenPrice
                      amount={allActiveListingSortedByPrice[0].pricePerToken}
                      tokenAddress={
                        allActiveListingSortedByPrice[0].paymentToken
                      }
                      currencySymbol={selectedMine.currency}
                      showCurrency={true}
                    />
                  ) : (
                    "0"
                  )}
                </span>
                <span className={styles.usdPrice}>
                  {maxPurchasableAmount} avaliable
                </span>
              </div>
            </div>

            <div className={styles.actions}>
              {/* <div className={styles.maxPrice}>
                <span>MAX PRICE PER TOKEN</span>
                <input
                  type="number"
                  value={formatEther(maxPricePerToken)}
                  readOnly
                />
                <span>ETH</span>
              </div> */}
              <div className={styles.buttonWrapper}>
                {avaliableNfts !== 0n ? (
                  <button
                    className={styles.buyNowButton}
                    onClick={() => setIsQuickListModalOpen(true)}
                    disabled={!isApprovedForAll}
                  >
                    LIST
                  </button>
                ) : null}
                <button
                  className={styles.makeOfferButton}
                  onClick={() => setIsMakeOfferModalOpen(true)}
                  disabled={!isApprovedForAll}
                >
                  MAKE OFFER
                </button>
                <button
                  className={styles.buyNowButton}
                  disabled={
                    !isApprovedForAll || !isPurchasable || isHandlingPurchasing
                  }
                  onClick={handlePurchaseListing}
                >
                  {isHandlingPurchasing ? (
                    <span className={styles.loader} />
                  ) : (
                    `BUY ${purchaseListingQuantity} NOW`
                  )}
                </button>
                <div className={styles.quantity}>
                  <button
                    disabled={!isPurchasable}
                    onClick={() =>
                      setPurchaseListingQuantity((prev) =>
                        prev > 1 ? prev - 1 : prev,
                      )
                    }
                  >
                    -
                  </button>
                  <span>{purchaseListingQuantity}</span>
                  <button
                    disabled={!isPurchasable}
                    onClick={() =>
                      setPurchaseListingQuantity((prev) =>
                        prev < maxPurchasableAmount ? prev + 1 : prev,
                      )
                    }
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <MakeOfferModal
        isOpen={isMakeOfferModalOpen}
        onClose={() => setIsMakeOfferModalOpen(false)}
      />
      <QuickListModal
        isOpen={isQuickListModalOpen}
        onClose={() => setIsQuickListModalOpen(false)}
      />
      <CancelListingModal
        isOpen={isCancelListingModalOpen}
        onClose={() => setIsCancelListingModalOpen(false)}
      />
      <CancelOfferModal
        isOpen={isCancelOfferModalOpen}
        onClose={() => setIsCancelOfferModalOpen(false)}
      />
      <SelectMineModal
        isOpen={isSelectMineModalOpen}
        onClose={() => setIsSelectMineModalOpen(false)}
      />
    </div>
  );
};

export default CurrentPrice;

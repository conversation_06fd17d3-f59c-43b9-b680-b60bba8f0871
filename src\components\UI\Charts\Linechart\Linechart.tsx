import { commoditiesDetails } from "@/constants/commoditiesDetails";
import { Chart } from "react-google-charts";
import variables from "@/styles/variables.module.scss";

type Unpacked<T> = T extends (infer Item)[] ? Item : T;

export default function Linechart({
  data,
  title = "",
}: {
  data: Unpacked<typeof commoditiesDetails>["historicalData"];
  title?: string;
}) {
  return (
    <Chart
      chartType="LineChart"
      width="100%"
      height="100%"
      data={data}
      options={{
        title,
        titleTextStyle: { color: "white", fontSize: 16 },
        backgroundColor: "transparent",
        legend: "none",
        tooltip: { trigger: "none" },
        curveType: "function",
        hAxis: {
          gridlines: { color: "transparent" },
          textStyle: {
            color: "transparent",
          },
        },

        vAxis: {
          gridlines: { color: "transparent" },
          textStyle: {
            color: "transparent",
          },
        },
      }}
    />
  );
}

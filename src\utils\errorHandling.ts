import { decodeErrorResult } from "viem";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";

/**
 * Extract the actual error message from a contract error
 * @param error The contract error object
 * @returns The extracted error message
 */
export const extractErrorReason = (error: any) => {
  // Split error message into lines
  const lines = error.message.split("\n");

  // Find the line containing the actual error message (e.g., "Already purchased")
  for (const line of lines) {
    const trimmedLine = line.trim();
    // If the line starts with a parenthesis and doesn't contain "string reason"
    if (trimmedLine.startsWith("(") && !trimmedLine.includes("string reason")) {
      // Remove parentheses and return the actual error message
      return trimmedLine.slice(1, -1);
    }
  }

  // If no matching error message is found, return shortMessage or original message
  return error.shortMessage || error.message;
};

/**
 * Decode contract error signature
 * @param error The contract error object
 * @returns The decoded error message
 */
export const decodeContractErrorSignature = (error: any): string => {
  try {
    // Extract error signature from error message
    const signatureMatch = error.message.match(/signature:\s+(0x[0-9a-f]{8})/i);
    if (signatureMatch && signatureMatch[1]) {
      const signature = signatureMatch[1];

      try {
        // Use viem's decodeErrorResult to decode error
        const decodedError = decodeErrorResult({
          abi: asteroidXMarketplaceAbi,
          data: signature.padEnd(66, "0"), // Pad data to match ABI requirements
        });

        // Format error message
        let errorMessage = decodedError.errorName;
        if (decodedError.args && decodedError.args.length > 0) {
          errorMessage += `(${decodedError.args.join(", ")})`;
        }
        return errorMessage;
      } catch (decodeErr) {
        // If no matching error is found in ABI, provide a generic mapping
        const knownErrors: Record<string, string> = {
          "0x832f98b5":
            "The item has been purchased or is no longer available, please refresh the page",
          // Add other known errors here
        };

        return (
          knownErrors[signature.toLowerCase()] ||
          `Unrecognized error (${signature})`
        );
      }
    }

    // If no error signature is found, return original error
    return error.shortMessage || error.message;
  } catch (e) {
    console.error("Error decoding error:", e);
    return error.shortMessage || error.message;
  }
};

/**
 * Extract the error type from contract error and format it with spaces
 * @param error The contract error object
 * @returns The formatted error type (e.g., "Insufficient Token Balance")
 */
export const extractErrorType = (error: any) => {
  //console.log("[Error Parsing] Start parsing error:", error);
  if (!error?.message) return "Unknown Error";

  // Check if the error message contains a signature
  if (error.message.includes("signature:")) {
    return decodeContractErrorSignature(error);
  }

  // Handle new error format: Error: InsufficientBalance(uint256 expected, uint256 actual, address token)
  //                         (10000000, 0, 0x01c845DCE557931FC79Fa09e342b18f55CAdF252)
  // Use a more flexible regex that can handle multi-line cases
  const errorNamePattern = /Error:\s+(\w+)(?:\s*\(|\s*\n)/;
  const multiLineErrorMatch = error.message.match(errorNamePattern);
  if (multiLineErrorMatch && multiLineErrorMatch[1]) {
    // console.log(
    //   "[Error Parsing] Multi-line error match successful:",
    //   multiLineErrorMatch[1],
    // );
    return multiLineErrorMatch[1].replace(/([A-Z])/g, " $1").trim();
  }

  // Original standard error matching: ErrorName(params)
  const errorMatch = error.message.match(/Error:\s+(\w+)\(/);
  if (errorMatch && errorMatch[1]) {
    // Format the error name by adding spaces between camel case words
    return errorMatch[1].replace(/([A-Z])/g, " $1").trim();
  }

  // If we can't extract the error type, fall back to the short message or complete message
  return error.shortMessage || error.message;
};

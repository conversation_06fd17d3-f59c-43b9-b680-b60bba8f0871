@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

// .tableWrapper {
//   display: flex;
//   flex-direction: column;
//   gap: 16px;
//   width: 100%;
// }

// .tableContainer {
//   width: 100%;
//   overflow-y: auto;
//   border-radius: 8px;
//   border: 1px solid rgba(255, 255, 255, 0.1);
//   -ms-overflow-style: none;
//   scrollbar-width: none;

//   &::-webkit-scrollbar {
//     display: none;
//   }
// }

// .table {
//   width: 100%;
//   border-collapse: collapse;
//   text-align: left;

//   th,
//   td {
//     padding: 12px;
//     border-bottom: 1px solid rgba(255, 255, 255, 0.1);
//   }

//   th {
//     position: sticky;
//     top: 0;
//     z-index: 1;
//     background-color: black;
//     font-weight: 600;
//   }

//   tr:hover {
//     background-color: rgba(255, 255, 255, 0.05);
//   }
// }

// .currentUser {
//   background-color: rgba(255, 215, 0, 0.1);
//   font-weight: 600;
// }

// .youLabel {
//   color: #ffd700;
//   font-weight: bold;
//   margin-left: 4px;
// }

// .pagination {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   gap: 16px;
//   padding: 8px;
// }

// .pageButton {
//   padding: 8px 16px;
//   border-radius: 4px;
//   background-color: rgba(255, 255, 255, 0.1);
//   border: 1px solid rgba(255, 255, 255, 0.2);
//   color: white;
//   cursor: pointer;
//   transition: all 0.2s;

//   &:hover:not(:disabled) {
//     background-color: rgba(255, 255, 255, 0.2);
//   }

//   &:disabled {
//     opacity: 0.5;
//     cursor: not-allowed;
//   }
// }

// .pageInfo {
//   color: rgba(255, 255, 255, 0.7);
//   font-size: 14px;
// }
.title {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: $margin-md;
  font-size: $font-size-md;
  text-transform: uppercase;
  color: $color-primary;
  font-family: "Garalama", sans-serif;
  font-weight: $font-weight-extralight;
  letter-spacing: 1px;
}

.leaderboardHeader {
  display: flex;
  justify-content: space-between;
  padding: 0.3rem 0;
  border-bottom: 2px solid $color-primary; // 标题底部添加较粗的边框区分
  margin-bottom: 2px; // 与表格内容有一点间距
  font-weight: $font-weight-bold;
  text-transform: uppercase;
  color: $color-primary;
}

.positionHeader,
.addressHeader,
.amountHeader,
.claimTimeHeader {
  font-size: $font-size-xs;
}

.leaderboardList {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.leaderboardRow {
  display: flex;
  justify-content: space-between; // 修改为水平排列
  align-items: center;
  gap: 0.3rem;
  padding: 0.3rem;
  border-bottom: 1px solid $color-primary-contrast;
}

.positionWrapper {
  flex: 0 0 5%; // 为排名预留一定宽度
}

.pointsValue {
  display: flex;
  flex: 0 0 90%; // 内容部分占剩余宽度
  justify-content: space-between;
  align-items: center;
  font-size: $font-size-sm;
  color: $color-primary;
  text-align: right;
  font-family: $font-family-poppins;
}

.addressValue {
  flex: 0 0 25%; // 地址部分宽度
  font-size: $font-size-sm;
  color: $color-primary;
}

.amountValue {
  flex: 0 0 20%; // 金额部分宽度
}

.claimTimeValue {
  flex: 0 0 30%; // 领取时间部分宽度
}

/* 为最后一行移除底部边框 */
.leaderboardRow:last-child {
  border-bottom: none;
}

/* 可选：添加hover效果 */
.leaderboardRow:hover {
  background: rgba(0, 188, 212, 0.5);
}

.currentUser {
  background-color: rgba(255, 255, 0, 0.2); // 当前用户行的背景色
}

.youLabel {
  color: yellow; // “(YOU)” 标签颜色
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: $margin-md;
}

.pageButton {
  padding: 0.5rem 1rem;
  border: none;
  background-color: $color-primary;
  color: white;
  cursor: pointer;
  border-radius: 4px;

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
}

.pageInfo {
  font-size: $font-size-sm;
  color: $color-primary;
}
@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100vw;
  // height: 400vh;
  position: absolute;
  top: 100vh;
  // height: 155vh;
  // border: 1px solid yellow;
  background: rgb(0, 0, 0);
  background: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.99) 10%,
    rgba(0, 0, 0, 0) 100%
  );

  .content {
    margin: auto;
    width: 75%;
    // height: 100%;
    // border: 1px solid green;
    // @include col-between;

    @media screen and (max-width: 1279px) {
      width: 100%;
    }

    .titleText {
      @include titleText();

      @media screen and (max-width: 1279px) {
        font-size: $font-size-5xl;
      }
    }

    .visionWrapper {
      padding: $padding-md;
      .vision {
        width: 100%;
        background: $color-black-transparent-medium;
        padding: $padding-md;
        @include col-center;
        // border: $border-width-xs solid $color-primary-transparent;
        // box-shadow: 0 0 20px $color-primary-contrast;

        h2 {
          @include row-center;
          justify-content: flex-start;
          gap: $spacing-md;
          font-size: $font-size-2xl;
          font-weight: $font-weight-extralight;
          color: $color-primary;
        }
      }
    }

    .objectiveWrapper {
      padding: $padding-md;
      .objective {
        width: 100%;
        background: $color-black-transparent-medium;
        padding: $padding-md;
        border: $border-width-xs solid $color-primary-transparent;
        box-shadow: 0 0 20px $color-primary-contrast;

        q {
          font-size: $font-size-2xl;
          font-weight: $font-weight-extralight;
          color: $color-primary;
        }
      }
    }

    .mineInformation {
      width: 100%;
      // height: 100%;
      padding: $padding-md;
      // @include col-between;

      @media screen and (min-width: 768px) {
        @include col-center;
      }

      .mineInformationCard {
        width: 100%;
        height: 300px;
        border: $border-width-xs solid $color-primary-transparent;
        box-shadow: 0 0 20px $color-primary-contrast;
        padding: $padding-md;
        margin-bottom: $margin-lg;
        @include row-center;
        gap: $spacing-md;
        background: $color-black-transparent-medium;
        // backdrop-filter: blur(5px);

        &:nth-child(2n) {
          flex-direction: row-reverse;
        }

        &:last-child {
          margin-bottom: 0;
        }

        @media screen and (max-width: 1279px) {
          height: auto;
          flex-direction: column-reverse;

          &:nth-child(2n) {
            flex-direction: column-reverse;
          }
        }

        @media screen and (min-width: 768px) {
          width: 400px;
        }

        @media screen and (min-width: 1280px) {
          width: 100%;
        }

        .descriptionWrapper {
          flex: 2;
          @include col-center;
          align-items: flex-start;
          gap: $spacing-md;

          .title {
            width: 100%;
            font-size: $font-size-3xl;
            font-weight: $font-weight-bold;
            color: $color-primary;
            text-align: center;
          }
          .description {
            @include row-center;
            justify-content: flex-start;
            gap: $spacing-md;
            font-size: $font-size-xl;
            font-weight: $font-weight-extralight;
            color: $color-primary;
            // -webkit-text-stroke: 1px $color-primary;

            @media screen and (max-width: 1279px) {
              font-size: $font-size-lg;
            }
          }
        }

        .image {
          flex: 1;
          // width: 100%;
          height: 100%;
          object-fit: cover;
          border: $border-width-xs solid $color-primary;
        }
      }
    }
  }
}

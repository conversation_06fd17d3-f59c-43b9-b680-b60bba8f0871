@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100%;
$displayHeight: 100%;

@mixin detailsWrapper {
  width: 100%;
  margin-bottom: $margin-lg;

  h1 {
    color: $color-primary;
    font-weight: 800;
    text-decoration: underline;
  }
  p {
    color: $color-primary;
    font-size: $font-size-3xl;
    font-weight: 100;
  }
}

.canvasScreen {
  width: $displayWidth;
  height: $displayHeight;

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
    // zoom: $zoom-level-1280;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
    // zoom: $zoom-level-1920;
  }
}

.container {
  width: 100%;
  height: 100%;
  @include col-between;
  align-items: flex-start;

  .scrollArea {
    width: 100%;
    height: 552px;
    overflow: scroll;
    // border: 1px solid green;

    &::-webkit-scrollbar {
      display: none;
    }

    .mineralInformation {
      width: 100%;
      height: 100%;
    }

    .mineralInformationComingSoon {
      width: 100%;
      height: 100%;
      @include col-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .mineralInformationWrapper {
      @include detailsWrapper;

      img {
        width: 100%;
        // height: 100%;
        object-fit: cover;
      }
    }

    .companyDetails {
      width: 100%;
      height: 100%;
    }

    .companyDetailsComingSoon {
      width: 100%;
      height: 100%;
      @include col-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .companyDetailsWrapper {
      @include detailsWrapper;

      img {
        width: 100%;
        // height: 100%;
        object-fit: cover;
      }
    }

    .investorZoneComingSoon {
      width: 100%;
      height: 100%;
      @include row-center;
      font-size: $font-size-5xl;
      color: $color-primary;
    }

    .investorZone {
      width: 100%;
      height: 100%;
    }

    .investorZoneWrapper {
      @include detailsWrapper;
    }

    .imageFrame {
      width: 100%;
      height: 100%;
      position: relative;
      background: $color-black-transparent;
      clip-path: polygon(
        0 0,
        calc(100% - 80px) 0,
        100% 80px,
        100% calc(100% - 48px),
        calc(100% - 48px) 100%,
        48px 100%,
        0 calc(100% - 48px)
      );

      &::before {
        content: "";
        position: absolute;
        inset: 0;
        background: $color-primary;
        clip-path: polygon(
          0 0,
          calc(100% - 81px) 0,
          100% 81px,
          100% calc(100% - 48px),
          calc(100% - 48px) 100%,
          48px 100%,
          0 calc(100% - 48px),
          0 0,
          1px 1px,
          1px calc(100% - 48px - 0.41px),
          calc(48px + 0.41px) calc(100% - 1px),
          calc(100% - 48px - 0.41px) calc(100% - 1px),
          calc(100% - 1px) calc(100% - 48px - 0.41px),
          calc(100% - 1px) calc(48px + 0.41px),
          calc(100% - 48px - 0.41px) 1px,
          1px 1px
        );
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .mainTitle {
    // width: 70%;
    height: 80px;
    // border: 1px solid green;
    @include col-between;
    justify-content: flex-start;
    align-items: flex-start;
    // line-height: 200%;

    .topTitle {
      align-self: center;
      @include row-center;

      .mineral {
        background: rgba(255, 255, 255, 0.07);
        padding: 0 $padding-xs;
        border-radius: $border-radius-sm;
        font-size: $font-size-md;
        color: #454545;
        font-weight: $font-weight-medium;
      }

      img {
        margin-left: $spacing-sm;
      }

      .location {
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        color: #454545;
      }
    }

    .bottomTitle {
      margin-top: -$spacing-sm;
      font-size: $font-size-5xl;
      font-weight: $font-weight-light;
      color: #454545;
    }
  }
}

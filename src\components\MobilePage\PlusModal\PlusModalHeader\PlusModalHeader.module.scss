@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  width: 100%;
  // border: 3px solid yellow;
}

.headerContainer {
  width: 100%;
  // height: 10%;
  @include row-between;
  // margin: $margin-lg 0;
  justify-content: flex-start;
  gap: $spacing-sm;
  // border: 3px solid yellow;

  .leftContainer {
    width: 100%;
    @include row-between;
    justify-content: flex-start;
  }
  .subtitle {
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-sm;

    .mineral {
      background: rgba(255, 255, 255, 0.02);
      padding: 0 $padding-xs;
      border-radius: $border-radius-sm;
      font-size: $font-size-2xs;
      // color: $color-primary-transparent-contrast;
      color: yellow;
      font-weight: $font-weight-medium;
    }

    .location {
      font-size: $font-size-2xs;
      font-weight: $font-weight-bold;
      color: $color-primary;
    }
  }

  .title {
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    color: $color-primary;
  }

  .rightImage {
    align-self: flex-end;
    // border: 3px solid yellow;
    // flex-shrink: 1;
    margin-right: $margin-md;
    cursor: pointer;
  }
}

.navBarContainer {
  width: 100%;
  // border: 1px solid yellow;
  // @include row-between;

  margin-top: $margin-sm;
  margin-bottom: $margin-md;
  // .overview {
  //   font-size: $font-size-md;
  //   // font-weight: $font-weight-semibold;
  //   color: $color-primary;
  //   text-transform: uppercase;
  // }

  .mineralInfo {
    width: 100%;
    @include row-between;
    // border: 1px solid yellow;
    font-family: "Garalama", sans-serif;
    letter-spacing: 1px;
    margin: $margin-sm 0;
    // gap: -2px;
    padding: 0 $padding-sm;

    .navButton {
      // box-shadow: 0px 0px 4px 0px $color-primary;
      // background:linear-gradient(to bottom, #ffffff 5%, #ffffff 100%);
      background-color: $color-black-transparent;
      // border-radius:15px;
      // border:2px solid $color-primary;
      display: inline-block;
      cursor: pointer;
      color: $color-primary-transparent-contrast;
      font-size: $font-size-xs;
      font-weight: $font-weight-extralight;
      // padding: $padding-sm;
      text-decoration: none;
    }
    button:focus {
      font-size: $font-size-md;
      // color: yellow;
      // font-weight: $font-weight-normal;
    }
    .active {
      transform: translateY(-2px);
      color: $color-primary;
      font-weight: $font-weight-light;
      font-size: $font-size-sm;
      // background-color:white;
    }
  }
}

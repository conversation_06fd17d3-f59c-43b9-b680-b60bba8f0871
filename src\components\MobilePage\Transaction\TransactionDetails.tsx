import styles from "./TransactionDetails.module.scss";
import dotDotDot from "@/assets/icons/leftNavigation/dotDotDot.png";
import { useState, useEffect } from "react";

import { motion } from "framer-motion";
import { buttonEffect } from "@/animations/animations";
import { useSnapshot } from "valtio";
import variables from "@/styles/variables.module.scss";
import Linechart from "@/components/UI/Charts/Linechart/Linechart";

import { newsDataStore } from "@/stores/newsData";
import { transactionData } from "@/sampleAssets/constants/transactionData";
import {
  commoditiesDetails,
  getMetalsDetails,
} from "@/constants/commoditiesDetails";
import { metalsDataStore } from "@/stores/metalsData";

import {
  TransactionData,
  useTransactionData,
} from "../../MainPage/RightNavigation/TransactionData/TransactionDetails/hooks/useTransactionData";

const filterList = ["BSC", "POLYGON"] as const;

interface TransactionDetailsProps {
  isShowMoreButtonClicked: boolean;
}

type FilterList = (typeof filterList)[keyof typeof filterList];

const TransactionDetails = ({
  isShowMoreButtonClicked,
}: TransactionDetailsProps) => {
  // const [selectedFilter, setSelectedFilter] = useState<FilterList>(
  //   filterList[0],
  // );

  // const { transactionBsc, transactionPolygon } = useTransactionData();
  // const newsDataSnapshot = useSnapshot(newsDataStore);

  // const getPriceUnit = () => {
  //   switch (selectedFilter) {
  //     case "BSC":
  //       return "BNB";
  //     case "POLYGON":
  //       return "MATIC";
  //     default:
  //       null;
  //   }
  // };

  // const DisplayTransaction = ({
  //   transactionData,
  // }: {
  //   transactionData: TransactionData[];
  // }) => (
  //   <>
  //     {transactionData
  //       .sort((a, b) => b.tokenId - a.tokenId)
  //       .map((transaction, index) => (
  //         <div key={index} className={styles.bodyWrapper}>
  //           <div className={styles.transactionTitleWrapper}>
  //             <h1 className={styles.transactionTitle}>{transaction.name}</h1>
  //             <h1 className={styles.transactionTitle}>
  //               {transaction.tokenPrice} <span>{getPriceUnit()}</span>
  //             </h1>
  //           </div>
  //           <div className={styles.transactionSubTitleWrapper}>
  //             <h1 className={styles.transactionSubtitle}>
  //               # {transaction.tokenId}
  //             </h1>
  //             <h1 className={styles.transactionSubtitle}>PER CARAT</h1>
  //           </div>
  //         </div>
  //       ))}
  //   </>
  // );

  // const getSelectedFilter = () => {
  //   switch (selectedFilter) {
  //     case "BSC":
  //       return <DisplayTransaction transactionData={transactionBsc} />;
  //     case "POLYGON":
  //       return <DisplayTransaction transactionData={transactionPolygon} />;
  //     default:
  //       return null;
  //   }
  // };

  const [metalsData, setMetalsData] = useState<typeof commoditiesDetails>([]);

  const metalsDataSnapshot = useSnapshot(metalsDataStore);
  useEffect(() => {
    if (metalsDataSnapshot.metalsData !== null) {
      setMetalsData(
        getMetalsDetails(
          JSON.parse(JSON.stringify(metalsDataSnapshot.metalsData)),
        ),
      );
    }
  }, [metalsDataSnapshot.metalsData]);
  return (
    <>
      {/* <div className={styles.headerWrapper}>
        <div className={styles.header}>
          <h1 className={styles.title}>Commodities</h1>
          <h2 className={styles.subTitle}>list of updated prices</h2>
        </div>
        <img src={dotDotDot.src} alt="dot dot dot icon" />
      </div> */}
      {/* <div className={styles.filter}>
        {filterList.map((item, index) => {
          const isSelected = selectedFilter === item;
          return (
            <motion.h1
              whileTap={buttonEffect.tap}
              whileHover={buttonEffect.hover}
              key={index}
              className={styles.list}
              style={{ color: isSelected ? variables.colorPrimary : undefined }}
              onClick={() => setSelectedFilter(item)}
            >
              {item}
            </motion.h1>
          );
        })}
      </div>
      <div
        className={styles.scrollArea}
        style={{ height: isShowMoreButtonClicked ? "79.5%" : "75.5%" }}
      >
        {getSelectedFilter()}
      </div> */}
      {/* <div className={styles.scrollArea} style={{ height: "84%" }}>
        {transactionData.map((transactions, index) => {
          return (
            <div key={index} className={styles.bodyWrapper}>
                <div className={styles.name}>
                  <div className={styles.bodyText}>{transactions.mineName}</div>
                  <div className={styles.label}>{transactions.mineId}</div>
                </div>
                <div className={styles.price}>
                  <div className={styles.bodyText}>${transactions.price}</div>
                  <div className={styles.label}>PER CARAT</div>
                </div>
              </div>
          );
        })}
      </div> */}
      {/* <div
        className={styles.scrollArea}
        style={{ height: isShowMoreButtonClicked ? "79.5%" : "75.5%" }}
      >
        {commoditiesDetails.map((item, index) => (
          <div className={styles.commoditiesDetailsWrapper} key={index}>
            <h1
              className={styles.commoditiesName}
              style={{ color: item.color }}
            >
              {item.name}
            </h1>
            <div className={styles.commoditiesPriceWrapper}>
              <h2 className={styles.commoditiesPrice}>
                ${item.price.toFixed(2)}
              </h2>
              <h3
                style={{
                  color:
                    item.priceChangePercentage24h >= 0
                      ? variables.colorPrimaryContrast
                      : variables.colorDanger,
                }}
              >
                {item.priceChangePercentage24h >= 0 && "+"}
                {item.priceChangePercentage24h}%
              </h3>
            </div>
          </div>
        ))}
        {/* {getSelectedFilter()} */}
      {/* </div> */}
      <div
        className={styles.scrollArea}
        // style={{ height: isShowMoreButtonClicked ? "79.5%" : "75.5%" }}
      >
        {metalsData.map((item, index) => (
          <div className={styles.commoditiesDetailsWrapper} key={index}>
            <div className={styles.commoditiesNameWrapper}>
              <h1
                className={styles.commoditiesName}
                style={{ color: item.color }}
              >
                {item.name}
              </h1>
              <h2 className={styles.commoditiesPrice}>
                ${item.price.toFixed(2)}
              </h2>
            </div>
            <div className={styles.commoditiesPriceWrapper}>
              <div style={{ width: "100px", height: "50px" }}>
                <Linechart data={item.historicalData} />
              </div>
              <h3
                style={{
                  color:
                    item.priceChangePercentage24h >= 0
                      ? variables.colorPrimaryContrast
                      : variables.colorDanger,
                }}
              >
                {item.priceChangePercentage24h >= 0 && "+"}
                {item.priceChangePercentage24h}%
              </h3>
            </div>
          </div>
        ))}
        {/* {getSelectedFilter()} */}
      </div>
    </>
  );
};

export default TransactionDetails;

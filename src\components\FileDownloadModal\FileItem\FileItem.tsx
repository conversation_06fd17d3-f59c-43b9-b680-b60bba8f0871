import React from "react";
import { motion } from "framer-motion";
import styles from "./FileItem.module.scss";
import { DownloadableFile } from "@/constants/mineDetails";
import { buttonEffect } from "@/animations/animations";
import { downloadSingleFile } from "@/utils/zipDownload";

interface FileItemProps {
  file: DownloadableFile;
  isSelected: boolean;
  onToggle: () => void;
}

const FileItem: React.FC<FileItemProps> = ({ file, isSelected, onToggle }) => {
  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return "📄";
      case "image":
        return "🖼️";
      case "document":
        return "📊";
      case "report":
        return "📋";
      default:
        return "📁";
    }
  };

  const getFileTypeLabel = (type: string) => {
    switch (type) {
      case "pdf":
        return "PDF";
      case "image":
        return "Images";
      case "document":
        return "Document";
      case "report":
        return "Report";
      default:
        return "File";
    }
  };

  return (
    <motion.div
      className={`${styles.fileItem} ${isSelected ? styles.selected : ""}`}
      whileTap={buttonEffect.tap}
      onClick={onToggle}
    >
      <div className={styles.checkbox}>
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onToggle}
          onClick={(e) => e.stopPropagation()}
        />
        <span className={styles.checkmark}></span>
      </div>

      <div className={styles.fileIcon}>{getFileIcon(file.type)}</div>

      <div className={styles.fileInfo}>
        <div className={styles.fileName}>{file.name}</div>
        <div className={styles.fileDetails}>
          <span className={styles.fileType}>{getFileTypeLabel(file.type)}</span>
          <span className={styles.fileSize}>{file.size}</span>
        </div>
        {file.description && (
          <div className={styles.fileDescription}>{file.description}</div>
        )}
      </div>

      <motion.button
        className={styles.downloadSingle}
        whileTap={buttonEffect.tap}
        onClick={(e) => {
          e.stopPropagation();
          downloadSingleFile({ name: file.name, url: file.url });
        }}
        title="Download this file"
      >
        ⬇️
      </motion.button>
    </motion.div>
  );
};

export default FileItem;

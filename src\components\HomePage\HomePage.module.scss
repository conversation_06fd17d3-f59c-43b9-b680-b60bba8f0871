@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.container {
  @include row-center;
  width: 100vw;
  height: 100vh;
  background-image: url("../../assets/images/australia.webp");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .wrapper {
    @include col-center;
    gap: $spacing-3xl;
  }

  .exploreButton {
    // width: 400px;
    font-family: $font-family-poppins;
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    text-align: center;
    cursor: pointer;
    padding: $padding-lg;
    background: $color-primary-transparent;
    color: $color-primary;
    backdrop-filter: blur(30px);
    border: $border-width-sm solid $color-primary;
  }
}

.canvasContainer {
  width: 100vw;
  height: 100vh;
}

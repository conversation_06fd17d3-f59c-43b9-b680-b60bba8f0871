import { minesDetails } from "@/constants/mineDetails";
import styles from "./LeftContents.module.scss";
import locationIcon from "@/assets/icons/minesNavigation/locationIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";

interface InformationProps {
  title: string;
  subtitle: string;
}

const LeftContents = () => {
  const mineCardSnapshot = useSnapshot(mineCardStore);

  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const Information = ({ title, subtitle }: InformationProps) => (
    <>
      <div className={styles.information}>
        <div className={styles.logoWrapper}>
          <div>
            <h1 className={styles.title}>{title}</h1>
            <h1 className={styles.subtitle}>{subtitle}</h1>
          </div>
        </div>
      </div>
    </>
  );

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <div className={styles.headerSubtitle}>
            <h1 className={styles.mineral}>{information.mineMineral}</h1>
            <img src={locationIcon.src} alt="location icon" />
            <h1 className={styles.location}>{information.mineLocation}</h1>
          </div>
          <h1 className={styles.headerTitle}>{information.name}</h1>
        </div>
      </div>
      <div className={styles.titleInformation}>
        <div className={styles.imageFrame}>
          <img src={information.mineImages[0]} alt={information.name} />
        </div>
        <div>
          <Information title={"price"} subtitle={`$${information.minePrice}`} />
          <Information title={"HIGHLIGHT"} subtitle={information.mineStorage} />
        </div>
      </div>
    </div>
  );
};

export default LeftContents;

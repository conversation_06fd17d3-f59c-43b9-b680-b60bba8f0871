import styles from "./PlusModalHeader.module.scss";
import variables from "@/styles/variables.module.scss";
import React, { useRef } from "react";
import { useEffect } from "react";
import { plusModalStore } from "@/stores/plusModal";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import Image from "next/image";
import headerIcon from "@/assets/icons/minesNavigation/mineHeaderIcon.png";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import locationIcon from "@/assets/icons/minesNavigation/locationIcon.png";

type onSelectType = (selectedTitle: string) => void;

interface NavbarProps {
  onSelect: onSelectType;
  selected: string;
}
export const headerDetails = [
  { title: "Mine Info" },
  { title: "Company" },
  { title: "Investor" },
];
const specialEventHeaderDetails = [
  { title: "Event Overview" },
  { title: "How to Participate" },
  { title: "Why Join?" },
];
const NavBar: React.FC<NavbarProps> = ({ onSelect, selected }) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const isSpecialEvent =
    minesDetails.filter(
      (mine) => mine.name === mineCardSnapshot.selectedMine,
    )[0].shortName === "hskreward";
  return (
    <div className={styles.navBarContainer}>
      <div className={styles.mineralInfo}>
        {headerDetails.map(({ title }, index) => (
          <React.Fragment key={title}>
            <div
              onClick={() => onSelect(title)}
              className={`${styles.navButton} ${selected === title ? styles.active : ""
                }`}
            >
              {isSpecialEvent ? (
                <>
                  {specialEventHeaderDetails[index].title}
                </>
              ) : (
                <>
                  {title}
                </>
              )}
            </div>
            {isSpecialEvent ? (
              null
            ) : (
              <>
                {index !== headerDetails.length - 1 && (
                  <span className={styles.divider}>/</span>
                )}
              </>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

const PlusModalHeader: React.FC = () => {
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const contentRef = useRef<HTMLDivElement>(null);


  useEffect(() => {
    if (plusModalSnapshot.isOpenPlusModal) {
      plusModalSnapshot.setSelectedTitle(headerDetails[0].title);
    }
  }, [plusModalSnapshot.isOpenPlusModal]);

  const information = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];

  const handleSelect = (title: string) => {
    plusModalSnapshot.setSelectedTitle(title);
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.headerContainer}>
        <div className={styles.leftContainer}>
          <Image
            src={headerIcon.src}
            alt="header icon"
            width={50}
            height={50}
          />
          <div>
            <div className={styles.subtitle}>
              <h1 className={styles.mineral}>{information.mineMineral}</h1>
              <img src={locationIcon.src} alt="location icon" />
              <h1 className={styles.location}>{information.mineLocation}</h1>
            </div>
            <h1 className={styles.title}>{information.name}</h1>
          </div>
        </div>
        <div>
          <img
            className={styles.rightImage}
            src={crossIcon.src}
            alt="dotdotdot icon"
            onClick={() => {
              plusModalSnapshot.setIsOpenPlusModal(false);
              plusModalSnapshot.setSelectedTitle(headerDetails[0].title);
            }}
          />
        </div>
      </div>
      <NavBar
        onSelect={handleSelect}
        selected={plusModalSnapshot.selectedTitle}
      />
    </div>
  );
};

export default PlusModalHeader;

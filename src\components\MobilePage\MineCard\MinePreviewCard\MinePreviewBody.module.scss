@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.information {
  width: 100%;
  border-bottom: $border-width-2xs solid $color-primary-transparent;
  padding-bottom: $spacing-sm;

  .logoWrapper {
    width: 100%;
    @include row-between;

    .title {
      font-size: $font-size-xs;
      color: white;
      font-weight: $font-weight-light;
      text-transform: uppercase;
    }

    .subtitle {
      font-size: $font-size-xs;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
  }
}

.container {
  position: absolute;
  width: 87%;
  height: 80%;
  overflow: scroll;
  @include col-center;
  justify-content: flex-start;
  align-items: flex-start;
  gap: $spacing-sm;
  // margin-top: $spacing-sm;
  // position: absolute;
  overflow-y: auto;
  padding-left: $padding-sm;
  padding-bottom: $padding-lg;
  // border: 3px solid yellow;

  .titleInformation {
    width: 100%;
    @include row-center;
    justify-content: flex-start;
    gap: $spacing-sm;
  }
  .imageFrame {
    width: 100%;
    @include row-center;
    justify-content: flex-start;
    // gap: $spacing-sm;
    height: 100%;
    // border: 3px solid yellow;
    position: relative;
    background: $color-primary-transparent;
    backdrop-filter: blur(20px);
    // padding: px;
    clip-path: polygon(0 0, calc(100% - 32px) 0, 100% 32px, 100% 100%, 0 100%);
    // border: 1px solid yellow;

    &::before {
      content: "";
      position: absolute;
      inset: 0;
      background: $color-primary;
      clip-path: polygon(
        0 0,
        calc(100% - 32px) 0,
        100% 32px,
        100% 100%,
        0 100%,
        0 0,
        1px 1px,
        1px calc(100% - 1px),
        calc(100% - 1px) calc(100% - 1px),
        calc(100% - 1px) calc(32px + 0.41px),
        calc(100% - 32px - 0.41px) 1px,
        1px 1px
      );
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .horizontalContainer {
    @include row-between;
    width: 100%;
    margin: $margin-md 0;
    .title {
      font-size: $font-size-xs;
      color: white;
      font-weight: $font-weight-light;
      text-transform: uppercase;
    }

    .subtitle {
      font-size: $font-size-xs;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    // .leftSection {
    //   flex-grow: 1;
    // }
    // .rightSection {
    //   flex-grow: 1;
    // }
  }

  .modelInfo {
    position: absolute;
    left: $margin-sm;
    bottom: $margin-sm;
    font-size: $font-size-xs;
    color: black;
  }
  .mineName {
    // margin-right: $margin-md;
    backdrop-filter: blur(7px);
    background: rgba(255, 255, 255, 0.02);
    padding: 0 $padding-xs;
    border-radius: $border-radius-sm;
    font-size: $font-size-2xs;
    // color: $color-primary-transparent-contrast;
    color: yellow;
    font-weight: $font-weight-medium;
  }
  .mineLocation {
    flex-grow: 1;
  }
  .mineFacility {
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
  }

  .detailsButton {
    width: 100%;
    // height: 100%;
    text-align: center;
    margin-bottom: 0.5rem;
    font-size: $font-size-sm;
    font-family: $font-family-poppins;
    color: yellow;
    border: $border-width-xs solid $color-primary;
    padding: $padding-sm 0;
    background: $color-primary-transparent;
    backdrop-filter: blur(5px);
    border-radius: $border-radius-sm;
    cursor: pointer;
  }

  &::-webkit-scrollbar {
    display: none;
  }
  .auditArea {
    width: 100%;
    @include row-center;
    gap: $spacing-md;

    .auditText {
      font-size: $font-size-sm;
      // color: $color-primary;
      color: gray;
      font-style: italic;
      @include row-center;
      gap: 0.5rem;
      // border: 1px solid yellow;
    }
  }
}

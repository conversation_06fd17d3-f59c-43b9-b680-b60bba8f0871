import { FC } from "react";
import styles from "./CancelListingModal.module.scss";
import crossIcon from "@/assets/icons/plusModal/crossIcon.png";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { minesDetails } from "@/constants/mineDetails";
import { formatEther } from "viem";
import useCancelListing from "../../hooks/useCancelListing";
import TokenPrice from "@/components/common/TokenPrice";

interface CancelListingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CancelListingModal: FC<CancelListingModalProps> = ({
  isOpen,
  onClose,
}) => {
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const selectedMine = minesDetails.filter(
    (mine) => mine.name === mineCardSnapshot.selectedMine,
  )[0];
  const {
    allActiveListingSortedBySeller,
    formatUnixTimestamp,
    selectedListingId,
    setSelectedListingId,
    cancelListing,
    isCancelingListing,
    isWaitingForCancelListing,
  } = useCancelListing(onClose);

  const handleMouseOver = (index: number) => {
    if (allActiveListingSortedBySeller) {
      setSelectedListingId(allActiveListingSortedBySeller[index].listingId);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalFrame}>
        <div className={styles.closeButton} onClick={onClose}>
          <img src={crossIcon.src} alt="cross icon" />
        </div>

        <div className={styles.contentWrapper}>
          <h2 className={styles.header}>CANCEL LISTING</h2>
          <div className={styles.scrollArea}>
            <div className={styles.contentFrame}>
              <div className={styles.derlordInfo}>
                <h3>{selectedMine.name}</h3>
              </div>
              <div className={styles.listingsTable}>
                <div className={styles.tableHeader}>
                  <div>Price per Token</div>
                  <div>Quantity</div>
                  <div>Expiration</div>
                  <div>Action</div>
                </div>
                <div className={styles.tableBodyContainer}>
                  <div className={styles.tableBody}>
                    {allActiveListingSortedBySeller &&
                    allActiveListingSortedBySeller.length > 0 ? (
                      allActiveListingSortedBySeller.map((listing, index) => (
                        <div className={styles.tableRow} key={index}>
                          <div>
                            <TokenPrice
                              amount={listing.pricePerToken}
                              tokenAddress={listing.paymentToken}
                              showCurrency={true}
                              currencySymbol={selectedMine.currency}
                            />
                          </div>
                          <div>{listing.amount.toString()}</div>
                          <div>
                            {formatUnixTimestamp(
                              Number(listing.expirationTime),
                            )}
                          </div>
                          <div>
                            <button
                              className={styles.cancelButton}
                              onMouseEnter={() => handleMouseOver(index)}
                              onClick={cancelListing}
                              disabled={
                                isCancelingListing || isWaitingForCancelListing
                              }
                            >
                              {(isCancelingListing ||
                                isWaitingForCancelListing) &&
                              selectedListingId === listing.listingId
                                ? "Processing..."
                                : "CANCEL"}
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div style={{ paddingLeft: "1rem" }}>
                        No active listings available
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CancelListingModal;

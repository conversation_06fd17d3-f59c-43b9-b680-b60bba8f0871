import useProgressBar from "./hooks/useProgressBar";
import styles from "./Navigation.module.scss";
import { useEffect, useState } from "react";

const Navigation = () => {
  const { progressPercentage, targetFund, avaliableNativeToken } =
    useProgressBar();
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    setTimeout(() => {
      setAnimate(true);
    }, 300);
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.progressSection}>
        <div className={styles.progressText}>
          <span className={animate ? styles.countUp : ""}>
            {progressPercentage}%
          </span>
        </div>
        <div className={styles.progressBar}>
          <div
            className={`${styles.progressFill} ${
              animate ? styles.animate : ""
            }`}
            style={{ width: `${progressPercentage}%` }}
          ></div>
          <div className={styles.progressGlow}></div>
        </div>
        <div className={styles.progressText} style={{ color: "white" }}>
          <span style={{ fontWeight: "100" }}>
            Only{" "}
            <span className={styles.highlight}>{avaliableNativeToken}</span> HSK
            left until the next price increase
            {/* <span className={styles.highlight}>2.85 DrillX</span> */}
          </span>
          {/* <span>{targetFund} HSK</span> */}
        </div>
      </div>

      <div className={styles.navLinks}>
        <a
          href="https://asteroid-x-1.gitbook.io/asteroid-x-product-book"
          target="_blank"
          className={styles.navLink}
        >
          Product Book
        </a>
        <a href="#" className={styles.navLink}>
          TOKENOMICS
        </a>
        <a href="#" className={styles.navLink}>
          DISCLAIMER
        </a>
      </div>
    </div>
  );
};

export default Navigation;

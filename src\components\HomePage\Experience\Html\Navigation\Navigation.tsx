import { use<PERSON>rame, useThree } from "@react-three/fiber";
import styles from "./Navigation.module.scss";
import { CameraControls, useScroll } from "@react-three/drei";
import { useElementSize, useEventListener } from "usehooks-ts";
import {
  Dispatch,
  MutableRefObject,
  SetStateAction,
  useEffect,
  useState,
} from "react";
import { useSnapshot } from "valtio";
import { scrollContainerStore } from "@/stores/scrollContainer";
import twitterIcon from "@/assets/icons/landingPage/twitterIcon.png";
import gitbookIcon from "@/assets/icons/landingPage/gitbookIcon.png";
import { isIOS, isSafari } from "react-device-detect";
import installIcon from "@/assets/icons/landingPage/installIcon.png";
import exportIcon from "@/assets/icons/landingPage/exportIcon.png";
import closeIcon from "@/assets/icons/landingPage/closeIcon.png";

type NavigationProps = {
  cameraControlsRef: MutableRefObject<CameraControls | undefined>;
  setScaleFactor: Dispatch<SetStateAction<number>>;
  INITIAL_SCALE_FACTOR: number;
};

const Navigation = ({
  cameraControlsRef,
  setScaleFactor,
  INITIAL_SCALE_FACTOR,
}: NavigationProps) => {
  const scrollContainerSnapshot = useSnapshot(scrollContainerStore);
  const scrollData = useScroll();
  const [containerPosition, setContainerPosition] = useState(0);
  const [showInstallMessage, setShowInstallMessage] = useState(false);

  useEffect(() => {
    const isInStandaloneMode = () =>
      "standalone" in window.navigator && window.navigator.standalone;

    setShowInstallMessage(isIOS && isSafari && !isInStandaloneMode());
  }, []);

  // console.log(showInstallMessage);

  useFrame(() => {
    const newPosition =
      scrollContainerSnapshot.containerHeight * scrollData.offset;
    setContainerPosition(newPosition);

    // setScaleFactor(INITIAL_SCALE_FACTOR * (1 + scrollData.offset));
  });

  return (
    <>
      <div
        className={styles.navigation}
        style={{ transform: `translate(0, ${containerPosition}px)` }}
      >
        <img
          src={twitterIcon.src}
          alt="twitter icon"
          onClick={() => window.open("https://twitter.com/Asteroid_AU")}
        />
        <span style={{ margin: "0 10px" }} />
        <img
          src={gitbookIcon.src}
          alt="github icon"
          onClick={() =>
            window.open(
              "https://asteroid-x-1.gitbook.io/asteroid-x-product-book",
            )
          }
        />
      </div>
      {showInstallMessage ? (
        <div
          className={styles.installPrompt}
          style={{ transform: `translate(-50%, ${containerPosition}px)` }}
        >
          <img
            src={installIcon.src}
            alt="install icon"
            className={styles.icon}
          />
          <h3 className={styles.instruction}>
            Install this app on your iPhone. Tap{" "}
            <img
              className={styles.icon}
              src={exportIcon.src}
              alt="export icon"
            />{" "}
            and then ADD to HOMESCREEN
          </h3>
          <img
            src={closeIcon.src}
            alt="close icon"
            className={styles.icon}
            onClick={() => setShowInstallMessage(false)}
          />
        </div>
      ) : null}
    </>
  );
};

export default Navigation;

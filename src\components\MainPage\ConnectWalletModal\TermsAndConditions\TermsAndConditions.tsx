import { useSnapshot } from "valtio";
import styles from "./TermsAndConditions.module.scss";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import { Dispatch, SetStateAction } from "react";
import { termsAndConditions } from "@/constants/termsAndConditions-v2";

type TermsAndConditionsProps = {
  setHasUserReadTandC: Dispatch<SetStateAction<boolean>>;
};

const TermsAndConditions = ({
  setHasUserReadTandC,
}: TermsAndConditionsProps) => {
  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.statement}>
          <h2 className={styles.title}>{termsAndConditions.title}</h2>
          <h5 className={styles.lastUpdated}>
            {termsAndConditions.lastUpdated}
          </h5>
          <br />
          <p>{termsAndConditions.notice}</p>
          {termsAndConditions.sections.map((section, index) => (
            <div key={index}>
              <br />
              <h3>{section.header}</h3>
              <p>{section.contents}</p>
            </div>
          ))}
        </div>
        <div className={styles.buttonGroup}>
          <button
            className={styles.cancelButton}
            onClick={() =>
              connectWalletModalSnapshot.setIsOpenConnectWalletModal(false)
            }
          >
            Decline
          </button>
          <button
            className={styles.confirmButton}
            onClick={() => setHasUserReadTandC(true)}
          >
            Accept
          </button>
        </div>
      </div>
    </>
  );
};

export default TermsAndConditions;

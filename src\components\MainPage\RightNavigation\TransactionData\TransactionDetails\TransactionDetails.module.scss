@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  @include row-between;
  // margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.filter {
  width: 30%;
  // border: 1px solid yellow;
  @include row-between;
  margin: $margin-sm 0;

  .list {
    font-size: $font-size-xs;
    color: #848e9c;
    cursor: pointer;
  }
}

.scrollArea {
  position: absolute;
  width: 87%;
  height: 78%;
  overflow: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .commoditiesDetailsWrapper {
    width: 100%;
    height: 90px;
    padding: $padding-md 0;
    border-bottom: 1px solid #e7e7e70f;
    @include row-between;

    .commoditiesNameWrapper {
      line-height: 135%;

      .commoditiesName {
        font-size: $font-size-xl;
        text-transform: uppercase;
      }

      .commoditiesPrice {
        color: $color-primary;
      }
    }

    .commoditiesPriceWrapper {
      line-height: 60%;
      @include col-center;
      margin-top: -25px;
      // align-items: flex-start;
    }
  }

  .bodyWrapper {
    padding: $padding-sm 0;
    border-bottom: 1px solid #e7e7e70f;

    .transactionTitleWrapper {
      @include row-between;

      .transactionTitle {
        font-size: $font-size-xs;
        color: $color-primary;
        // font-weight: $font-weight-medium;

        span {
          font-size: $font-size-2xs;
        }
      }
    }
    .transactionSubTitleWrapper {
      @include row-between;
      .transactionSubtitle {
        font-size: $font-size-xs;
        font-weight: $font-weight-light;
        color: $color-primary-transparent-contrast;
        // text-transform: uppercase;
      }
    }
  }
}

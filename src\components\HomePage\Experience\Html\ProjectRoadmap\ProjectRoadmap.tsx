import {
  VerticalTimeline,
  VerticalTimelineElement,
} from "react-vertical-timeline-component";
import styles from "./ProjectRoadmap.module.scss";
import { CSSProperties } from "react";
import variables from "@/styles/variables.module.scss";
import AsteroidIcon from "@/components/MainPage/PlusModal/PlusModalMainContents/Mineralinformation/Roadmap/AsteroidIcon/AsteroidIcon";
import { projectRoadmap } from "@/constants/projectRoadmap";
import "react-vertical-timeline-component/style.min.css";

interface TimelineStyle {
  lineColor: string;
  contentStyle: CSSProperties;
  contentArrowStyle: CSSProperties;
  contentIconStyle: CSSProperties;
  contentStyleCurrent: CSSProperties;
}

const timelineStyle: TimelineStyle = {
  lineColor: variables.colorPrimaryTransparent,
  contentStyle: {
    background: variables.colorBlackTransparentMedium,
    border: `1px solid ${variables.colorPrimaryContrast}`,
  },
  contentStyleCurrent: {
    background: variables.colorPrimaryContrast,
    border: `1px solid ${variables.colorPrimaryContrast}`,
    color: "white",
  },
  contentArrowStyle: {
    borderRight: `12px solid ${variables.colorPrimaryContrast}`,
  },
  contentIconStyle: {
    background: variables.colorBlackTransparentMedium,
  },
};

const ProjectRoadmap = () => {
  return (
    <>
      <h1 className={styles.title}>roadmap</h1>
      <div className={styles.roadmapWrapper}>
        <VerticalTimeline lineColor={timelineStyle.lineColor}>
          {projectRoadmap.map((stage, index) => (
            <VerticalTimelineElement
              key={index}
              contentStyle={
                stage.status === "current"
                  ? timelineStyle.contentStyleCurrent
                  : timelineStyle.contentStyle
              }
              contentArrowStyle={timelineStyle.contentArrowStyle}
              date={`phase ${index + 1}`}
              dateClassName={styles.dateClassName}
              iconStyle={timelineStyle.contentIconStyle}
              icon={<AsteroidIcon />}
            >
              <h4
                style={{
                  color: stage.status === "current" ? "white" : undefined,
                }}
              >
                {stage.description}{" "}
                {stage.status === "next" ? <span>(pending...)</span> : null}
                {stage.status === "current" ? <span>(current)</span> : null}
              </h4>
            </VerticalTimelineElement>
          ))}
        </VerticalTimeline>
      </div>
    </>
  );
};

export default ProjectRoadmap;

import TokenPrice from "@/components/common/TokenPrice";
import styles from "./CurrentPrice.module.scss";
import useActiveOffer from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useActiveOffer";
import { useNetwork } from "wagmi";
import { networkConfigs } from "@/constants/networkConfigs";
import useTokenActivities from "@/components/MainPage/TradeModal/TradeModalMainContents/Mineralinformation/TradeDashboard/hooks/useTokenActivities";
import { useState } from "react";
const trimWalletAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

const Activities = () => {
  const ZERO_ADDRESS = "******************************************";
  const { formatUnixTimestamp } = useActiveOffer();
  const { chain } = useNetwork();
  const {
    tokenActivitiesFilteredByType,
    activityTypes,
    marketplaceVaultAddress,
  } = useTokenActivities();
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded((prevState) => !prevState);
  };
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // 计算分页数据
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems =
    tokenActivitiesFilteredByType?.slice(startIndex, endIndex) || [];
  const totalPages = tokenActivitiesFilteredByType
    ? Math.ceil(tokenActivitiesFilteredByType.length / itemsPerPage)
    : 0;

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const ReplaceZeroAddress = () => (
    <a
      href={
        chain &&
        marketplaceVaultAddress &&
        networkConfigs[chain.id].etherscanAddress + marketplaceVaultAddress
      }
      target="_blank"
      style={{ color: "#4895ef" }}
    >
      {marketplaceVaultAddress && trimWalletAddress(marketplaceVaultAddress)}
    </a>
  );

  return (
    <div className={styles.container}>
      <div className={styles.listings}>
        <div
          className={`${styles.mainHeader} ${
            isExpanded ? styles.expanded : styles.collapsed
          }`}
          onClick={handleToggle}
        >
          <span className={styles.glowText}>EVENTS</span>
          <span className={styles.buttonHighlight}></span>
        </div>
        {isExpanded && (
          <>
            <div className={styles.listingsTable}>
              <div className={styles.tableHeader}>
                <div>Event</div>
                <div>Price per Token</div>
                <div>Quantity</div>
                <div>From</div>
                <div>To</div>
                <div>Date</div>
              </div>

              <div className={styles.tableBody}>
                {tokenActivitiesFilteredByType &&
                tokenActivitiesFilteredByType.length > 0 ? (
                  currentItems.map((token, index) => (
                    <div className={styles.tableRow} key={index}>
                      <div className={styles.price}>
                        {activityTypes[token.activityType]}
                      </div>
                      <div className={styles.price}>
                        <TokenPrice
                          amount={token.price}
                          tokenAddress={token.paymentToken}
                        />
                      </div>
                      <div className={styles.price}>
                        {token.amount.toString()}
                      </div>
                      <div>
                        {token.from === ZERO_ADDRESS ? (
                          <ReplaceZeroAddress />
                        ) : (
                          <a
                            href={
                              chain &&
                              networkConfigs[chain.id].etherscanAddress +
                                token.from
                            }
                            target="_blank"
                            style={{ color: "#4895ef" }}
                          >
                            {trimWalletAddress(token.from)}
                          </a>
                        )}
                      </div>
                      <div>
                        {token.to === ZERO_ADDRESS ? (
                          <ReplaceZeroAddress />
                        ) : (
                          <a
                            href={
                              chain &&
                              networkConfigs[chain.id].etherscanAddress +
                                token.to
                            }
                            target="_blank"
                            style={{ color: "#4895ef" }}
                          >
                            {trimWalletAddress(token.to)}
                          </a>
                        )}
                      </div>
                      <div className={styles.expiration}>
                        {formatUnixTimestamp(Number(token.timestamp))}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className={styles.noListings}>
                    No active activities available
                  </div>
                )}
              </div>
            </div>
            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={styles.pageButton}
                >
                  ← Previous
                </button>

                <span className={styles.pageInfo}>
                  Page {currentPage} of {totalPages}
                </span>

                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={styles.pageButton}
                >
                  Next →
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Activities;

import { fadeIn } from "@/animations/animations";
import styles from "./PrivateSale.module.scss";
import { motion } from "framer-motion";
import { useLayoutEffect, useState } from "react";
import Sidebar from "./components/Sidebar/Sidebar";
import MainContent from "./components/MainContent/MainContent";
import RewardsModal from "./components/RewardsModal/RewardsModal";
import JoinNowModal from "./components/JoinNowModal/JoinNowModal";
import { useAccount } from "wagmi";
import { showError } from "@/lib/notification";
import { navbarButtonStore } from "@/stores/navbarButton";
import { useSnapshot } from "valtio";
import { navbarButtonDetails } from "../TopNavigation/NavbarContent/NavbarContent";

const PrivateSale = () => {
  const [isRewardsModalOpen, setIsRewardsModalOpen] = useState(false);
  const [isJoinNowModalOpen, setIsJoinNowModalOpen] = useState(false);
  const { address } = useAccount();
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);

  useLayoutEffect(() => {
    if (!address) {
      showError("Please connect your wallet");
      navbarButtonSnapshot.setSelectedButton(navbarButtonDetails[0].title);
    }
  }, [address]);

  const openRewardsModal = () => {
    setIsRewardsModalOpen(true);
  };

  const closeRewardsModal = () => {
    setIsRewardsModalOpen(false);
  };

  const openJoinNowModal = () => {
    setIsJoinNowModalOpen(true);
  };

  const closeJoinNowModal = () => {
    setIsJoinNowModalOpen(false);
  };

  return (
    <motion.div
      className={styles.container}
      variants={fadeIn(1)}
      initial="hidden"
      animate="visible"
      exit="hidden"
      transition={{ duration: 1 }}
    >
      <motion.div className={styles.modalFrame} variants={fadeIn(0.5)}>
        <div className={styles.comingSoonOverlay}>
          <span>Coming Soon</span>
        </div>
        <div className={styles.scrollArea}>
          <div className={styles.mainContent}>
            <div className={styles.backgroundLayer1} />
            <div className={styles.backgroundLayer2} />
            <div className={styles.spotlightBeam} />
            <div className={styles.mainLayout}>
              <Sidebar />
              <MainContent
                openRewardsModal={openRewardsModal}
                openJoinNowModal={openJoinNowModal}
              />
            </div>
          </div>
        </div>
      </motion.div>

      <RewardsModal isOpen={isRewardsModalOpen} onClose={closeRewardsModal} />
      <JoinNowModal
        isOpen={isJoinNowModalOpen}
        onClose={closeJoinNowModal}
        openRewardsModal={openRewardsModal}
      />
    </motion.div>
  );
};

export default PrivateSale;

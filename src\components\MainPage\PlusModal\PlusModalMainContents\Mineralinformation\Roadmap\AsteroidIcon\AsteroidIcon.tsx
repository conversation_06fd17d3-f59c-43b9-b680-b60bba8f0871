const AsteroidIcon = () => {
  return (
    <svg
      width="57"
      height="60"
      viewBox="0 0 57 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_i_17_1464)">
        <path
          d="M50.5 47.3857L28.5 59.5L6.5 47.3857L6.5 6.5L28.5 36.7857L50.5 6.5L50.5 47.3857Z"
          fill="url(#paint0_radial_17_1464)"
        />
      </g>
      <path
        d="M28.5 47.5L12.5 20.5L28.5 34.2368L44.5 20.5L28.5 47.5Z"
        fill="#00C4D0"
      />
      <path
        d="M54.5178 2.85035L28.5 46.5231L2.4822 2.85034L28.1753 24.7925L28.5 25.0698L28.8247 24.7925L54.5178 2.85035Z"
        fill="url(#paint1_angular_17_1464)"
        fillOpacity="0.42"
        stroke="url(#paint2_linear_17_1464)"
      />
      <defs>
        <filter
          id="filter0_i_17_1464"
          x="6.5"
          y="6.5"
          width="44"
          height="57"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="93.5" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_17_1464"
          />
        </filter>
        <radialGradient
          id="paint0_radial_17_1464"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(28.5 37.9214) rotate(-90) scale(21.5786 22)"
        >
          <stop stopColor="#00C4D0" />
          <stop offset="1" stopColor="#00C4D0" stopOpacity="0" />
        </radialGradient>
        <radialGradient
          id="paint1_angular_17_1464"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(28.5 24) rotate(-90) scale(23.5 28)"
        >
          <stop stopColor="#00C4D0" />
          <stop offset="0.505208" stopColor="#00C4D0" stopOpacity="0.49" />
        </radialGradient>
        <linearGradient
          id="paint2_linear_17_1464"
          x1="28.5"
          y1="47.5"
          x2="89.1451"
          y2="-13.0404"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" />
          <stop offset="0.5" stopColor="#00C4D0" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default AsteroidIcon;

import { formatUnits, parseUnits } from "viem";
import { networkConfigs } from "@/constants/networkConfigs";

/**
 * Get token decimals from token address
 * @param tokenAddress Token contract address
 * @param chainId Chain ID
 * @returns decimals
 */
export const getTokenDecimals = (
  tokenAddress: `0x${string}` | undefined,
  chainId?: number,
): number => {
  // If ETH (zero address), return 18
  if (
    !tokenAddress ||
    tokenAddress === "******************************************"
  ) {
    return 18;
  }

  // Get known token decimals by chainId and tokenAddress
  if (chainId && networkConfigs[chainId]) {
    // Check if it is USDT on the current network
    if (
      tokenAddress.toLowerCase() ===
      networkConfigs[chainId].usdtAddress?.toLowerCase()
    ) {
      return 6; // USDT usually has 6 decimals
    }

    // You can add more token decimals logic as needed
  }

  // Default return 18, most ERC20 tokens use 18 decimals
  return 18;
};

/**
 * Format token price for display
 * @param amount Amount in BigInt format
 * @param tokenAddress Token address
 * @param decimals Optional decimals override
 * @param chainId Optional chain ID
 * @returns Formatted price string
 */
export const formatTokenPrice = (
  amount: bigint | undefined,
  tokenAddress?: `0x${string}`,
  decimals?: number,
  chainId?: number,
): string => {
  if (amount === undefined || amount === null) {
    return "0";
  }

  // Use the provided decimals or infer from tokenAddress
  const tokenDecimals =
    decimals !== undefined ? decimals : getTokenDecimals(tokenAddress, chainId);

  try {
    return formatUnits(amount, tokenDecimals);
  } catch (error) {
    console.error("Error formatting token price:", error);
    return "0";
  }
};

/**
 * Convert a string or number amount to BigInt
 * @param amount Amount as a string or number
 * @param tokenAddress Token address
 * @param decimals Optional decimals override
 * @param chainId Optional chain ID
 * @returns Amount in BigInt format
 */
export const parseTokenAmount = (
  amount: string | number,
  tokenAddress?: `0x${string}`,
  decimals?: number,
  chainId?: number,
): bigint => {
  if (!amount || amount === "0") {
    return BigInt(0);
  }

  // Ensure amount is a string
  const amountStr = typeof amount === "number" ? amount.toString() : amount;

  // Use the provided decimals or infer from tokenAddress
  const tokenDecimals =
    decimals !== undefined ? decimals : getTokenDecimals(tokenAddress, chainId);

  try {
    return parseUnits(amountStr, tokenDecimals);
  } catch (error) {
    console.error("Error parsing token amount:", error);
    // Fallback solution to handle precision issues
    return BigInt(Math.floor(Number(amountStr) * 10 ** tokenDecimals));
  }
};

// Provide formatEther and parseEther alternatives for compatibility with existing code
export const formatEther = (amount: bigint | undefined): string => {
  return formatTokenPrice(amount, "******************************************");
};

export const parseEther = (amount: string | number): bigint => {
  return parseTokenAmount(amount, "******************************************");
};

@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

$displayWidth: 100vw;
$displayHeight: 100vh;

.container {
  position: relative;
  top: 0;
  left: 0;
  width: $displayWidth;
  height: $displayHeight;

  @media screen and (min-width: 1280px) {
    width: $displayWidth * $zoom-level-1280-offset;
    height: $displayHeight * $zoom-level-1280-offset;
  }

  @media screen and (min-width: 1920px) {
    width: $displayWidth * $zoom-level-1920-offset;
    height: $displayHeight * $zoom-level-1920-offset;
  }

  .backgroundImage {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
    object-position: center;
    opacity: 0.2;
  }

  .mainContent {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-y: scroll;

    .mainContentContainer {
      width: 90%;
      margin: 0 auto;
      padding: 1rem;
      margin-top: 4rem;
    }
  }
}

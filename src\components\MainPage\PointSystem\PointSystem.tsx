import { fadeIn } from "@/animations/animations";
import styles from "./PointSystem.module.scss";
import { motion } from "framer-motion";
import PointSystemModalHeader from "./PointSystemModalHeader/PointSystemModalHeader";
import PointSystemModalMainContents from "./PointSystemModalMainContents/PointSystemModalMainContents";
import downArrowIcon from "./assets/icons/downArrowIcon.png";
import { useEffect, useState } from "react";
import { useAccount } from "wagmi";
import {
  checkinUserServer,
  getUserCheckinStatusServer,
} from "@/actions/getPointsDataServer";
import { showCustom, showError, showSuccess } from "@/lib/notification";
import { pointsDataStore } from "@/stores/pointsData";
import CountdownTimer from "./CountdownTimer/CountdownTimer";
import { Half1Icon } from "@radix-ui/react-icons";

const PointSystem = () => {
  const { address } = useAccount();
  const [isCheckinAvaliable, setIsCheckinAvaliable] = useState(false);
  const [isCheckingin, setIsCheckingin] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);

  const countdownTimerProps = {
    remainingTime,
    setIsCheckinAvaliable,
  };

  useEffect(() => {
    if (address) {
      getUserCheckinStatusServer(address).then((data) => {
        if (data.canCheckin === true) {
          setIsCheckinAvaliable(true);
        } else {
          setIsCheckinAvaliable(false);
        }
        setRemainingTime(data.nextCheckinSeconds);
      });
    }
  }, [address]);

  const checkinUser = async () => {
    const isTestnet = process.env.NEXT_PUBLIC_MODE === "development";
    if (isTestnet) {
      showError("Visit our mainnet to check in");
      return;
    }

    if (address) {
      setIsCheckingin(true);
      const { code, data } = await checkinUserServer(address);
      if (code === 0) {
        showSuccess("Checkin successful");
        setIsCheckinAvaliable(false);
      } else {
        showError("Checkin failed, contact support");
      }
      setRemainingTime(data.nextCheckinSeconds);
      setIsCheckingin(false);

      await Promise.all([
        pointsDataStore.fetchPointsData(address),
        pointsDataStore.fetchUserTasks(address),
      ]);
    }
  };

  return (
    <motion.div
      className={styles.container}
      variants={fadeIn(1)}
      initial="hidden"
      animate="visible"
      exit="hidden"
      transition={{ duration: 1 }}
    >
      <motion.div className={styles.modalFrame} variants={fadeIn(0.5)}>
        <div className={styles.sellMineOnline}>
          <h1>Questing, Testing, Earning and Claiming</h1>
          <h1>2024</h1>
        </div>
        <div
          className={`${styles.downloadButton} ${
            isCheckinAvaliable ? styles.clickable : ""
          }`}
          onClick={checkinUser}
        >
          {isCheckinAvaliable ? (
            <>
              {!isCheckingin ? (
                <>
                  <div className={styles.buttonText}>
                    <h3>asteroidx daily checkin</h3>
                    <img src={downArrowIcon.src} alt="down arrow" />
                  </div>
                  <h1>Check In</h1>
                </>
              ) : (
                <h1>Checking In...</h1>
              )}
            </>
          ) : (
            <>
              <div className={styles.buttonText}>
                <h3>next daily checkin</h3>
                <img src={downArrowIcon.src} alt="down arrow" />
              </div>
              <h1>
                <CountdownTimer {...countdownTimerProps} />
              </h1>
            </>
          )}
        </div>

        <div className={styles.contentWrapper}>
          <PointSystemModalHeader />
          <PointSystemModalMainContents />
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PointSystem;

import styles from "./RedPocket.module.scss";
import { delay, motion } from "framer-motion";
import flashingDot from "@/assets/icons/minesNavigation/ripple.svg";
import { scaleUp } from "@/animations/animations";
import { useState } from "react";
import redpacketIcon from "./assets/redpacketIcon.png";
import RedpacketClaimPage from "./RedpacketClaimPage/RedpacketClaimPage";
import { useAccount, useNetwork } from "wagmi";
import { showError } from "@/lib/notification";
import useRedpacket from "./hooks/useRedpacket";
import { formatEther } from "viem";

const RedPocket = () => {
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const {
    participated,
    isClaimAvaliable,
    avaliableHskBalance,
    avaliableNftBalance,
  } = useRedpacket();

  const [isButtonClicked, setIsButtonClicked] = useState(false);

  const handleClick = () => {
    if (!isConnected) {
      showError("Please connect your wallet");
      return;
    }

    if (chain?.id && !redPocketDetails.supportedNetwork.includes(chain.id)) {
      showError("Please Switch to HSK Network");
      return;
    }

    if (!isClaimAvaliable) {
      showError("No more red packet available");
      return;
    }

    setIsButtonClicked(true);
  };

  return (
    <>
      <motion.img
        className={styles.rippleContainer}
        variants={scaleUp(0.8, redPocketDetails.delay)}
        src={flashingDot.src}
        width={72}
        height={72}
        alt="flashing dot"
        style={{
          top: redPocketDetails.position.rippleTop,
          left: redPocketDetails.position.rippleLeft,
        }}
      />
      <motion.div
        className={styles.container}
        variants={scaleUp(0.8, redPocketDetails.delay + 1)}
        style={{
          top: redPocketDetails.position.top,
          left: redPocketDetails.position.left,
        }}
        onClick={handleClick}
      >
        <motion.img
          src={redpacketIcon.src}
          alt="red packet icon"
          width={64}
          height={64}
          animate={{
            rotate: [0, -5, 5, -5, 0],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
        <div>
          <h3>Claim your red packet</h3>
          {/* <h5>
            <span>{formatEther(avaliableHskBalance)}</span> HSK &{" "}
            <span>{avaliableNftBalance.toString()}</span> NFT
          </h5> */}
        </div>
      </motion.div>
      {isButtonClicked ? (
        <div className={styles.overlay}>
          <RedpacketClaimPage
            setIsButtonClicked={setIsButtonClicked}
            participated={participated}
          />
        </div>
      ) : null}
    </>
  );
};

export default RedPocket;

export const redPocketDetails = {
  delay: 10,
  position: {
    top: "18%",
    left: "60%",
    rippleTop: "18%",
    rippleLeft: "56.5%",
  },
  supportedNetwork: [133, 177],
};

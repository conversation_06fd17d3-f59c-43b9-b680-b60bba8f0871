"use client";

import { fadeIn } from "@/animations/animations";
import styles from "./MainPage.module.scss";
import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next/navigation";
import TopNavigation from "./TopNavigation/TopNavigation";
import BottomNavigation from "./BottomNavigation/BottomNavigation";
import LeftNavigation from "./LeftNavigation/LeftNavigation";
import RightNavigation from "./RightNavigation/RightNavigation";
import { getMarketData } from "@/actions/getMarketData";
import { useSnapshot } from "valtio";
import { marketDataStore } from "@/stores/marketData";
import { navbarButtonStore } from "@/stores/navbarButton";
import { navbarButtonDetails } from "./TopNavigation/NavbarContent/NavbarContent";
import DepositsSection from "./DepositsSection/DepositsSection";
import PurchaserSection from "./PurchaserSection/PurchaserSection";
import LaunchSection from "./LaunchSection/LaunchSection";
import { getNewsData } from "@/actions/getNewsData";
import { newsDataStore } from "@/stores/newsData";
import MinesNavigation from "./MinesNavigation/MinesNavigation";
import { connectWalletModalStore } from "@/stores/connectWalletModal";
import ConnectWalletModal from "./ConnectWalletModal/ConnectWalletModal";
import { plusModalStore } from "@/stores/plusModal";
import PlusModal from "./PlusModal/PlusModal";
import { downloadModalStore } from "@/stores/downloadModal";
import FileDownloadModal from "../FileDownloadModal/FileDownloadModal";
import { Toaster } from "react-hot-toast";
import Confetti from "react-confetti";
import { confettiStore } from "@/stores/confetti";
import { useMediaQuery } from "usehooks-ts";
import MobilePage from "../MobilePage/MobilePage";
import { purchaseDetailsModal } from "@/stores/purchaseDetailsModal";
import PurchaseDetailsModal from "./PurchaseDetailsModal/PurchaseDetailsModal";
import { mineCardStore } from "@/stores/mineCard";
import { useLayoutEffect } from "react";
import { useAccount } from "wagmi";
import { getNewsDataFromOwnServer } from "@/actions/getNewsDataFromOwnServer";
import { getMetalsDataFromOwnServer } from "@/actions/getMetalsDataFromOwnServer";
import { metalsDataStore } from "@/stores/metalsData";
import RegisterMine from "./RegisterMine/RegisterMine";
import Statistics from "../Statistics/Statistics";
import PointSystem from "./PointSystem/PointSystem";
import BlockchainInfo from "./BlockchainInfo/BlockchainInfo";
import PrivateSale from "./PrivateSale/PrivateSale";

interface MainPageProps {
  metalsData: Awaited<ReturnType<typeof getMetalsDataFromOwnServer>>;
  newsData: Awaited<ReturnType<typeof getNewsDataFromOwnServer>>;
}

const MainPage = ({ metalsData, newsData }: MainPageProps) => {
  // pre-fetch all data

  // const marketDataSnapshot = useSnapshot(marketDataStore);
  // marketDataSnapshot.setMarketData(marketData);
  const navbarButtonSnapshot = useSnapshot(navbarButtonStore);
  const isInitialAnimationDone = navbarButtonSnapshot.isInitialAnimationDone;
  const newsDataSnapshot = useSnapshot(newsDataStore);
  newsDataSnapshot.setNewsData(newsData);
  const metalsDataSnapshot = useSnapshot(metalsDataStore);
  metalsDataSnapshot.setMetalsData(metalsData);

  const connectWalletModalSnapshot = useSnapshot(connectWalletModalStore);
  const purchaseDetailsModalSnapshot = useSnapshot(purchaseDetailsModal);
  const mineCardSnapshot = useSnapshot(mineCardStore);
  const plusModalSnapshot = useSnapshot(plusModalStore);
  const downloadModalSnapshot = useSnapshot(downloadModalStore);
  const confettiSnapshot = useSnapshot(confettiStore);

  const isMobileOrTablet = useMediaQuery("(max-width: 1279px)");

  const { isConnected } = useAccount();

  const selectedSection = (selection: string) => {
    const defaultSelection = (
      <>
        <BottomNavigation delay={isInitialAnimationDone ? 0 : 2} />
        <LeftNavigation delay={isInitialAnimationDone ? 0 : 3.5} />
        <RightNavigation delay={isInitialAnimationDone ? 0 : 3.5} />
        {mineCardSnapshot.isShowOverlay ? (
          <div className={styles.mineNavigationOverlay} />
        ) : null}
        <MinesNavigation delay={isInitialAnimationDone ? 2.5 : 5.5} />
        <AnimatePresence>
          {connectWalletModalSnapshot.isOpenConnectWalletModal ? (
            <ConnectWalletModal />
          ) : null}
        </AnimatePresence>
      </>
    );

    switch (selection) {
      case navbarButtonDetails[0].title:
        return defaultSelection;
      case navbarButtonDetails[1].title:
        return (
          <>
            <PrivateSale />
          </>
        );
      case navbarButtonDetails[2].title:
        return (
          <>
            <PointSystem />
          </>
        );
      case navbarButtonDetails[3].title:
        return (
          <>
            <PurchaserSection />
          </>
        );
      case navbarButtonDetails[4].title:
        return (
          <>
            <LaunchSection />
          </>
        );
      default:
        return defaultSelection;
    }
  };

  useLayoutEffect(() => {
    if (window.localStorage.getItem("asteroidx-user-visited") === null) {
      window.localStorage.setItem("asteroidx-user-visited", "visited");
    }
  }, []);

  // useLayoutEffect(() => {
  //   let timer: NodeJS.Timeout;

  //   if (!isConnected) {
  //     timer = setTimeout(() => {
  //       connectWalletModalSnapshot.setIsOpenConnectWalletModal(true);
  //     }, 12000);
  //   }

  //   return () => {
  //     clearTimeout(timer);
  //   };
  // }, [isConnected]);

  return (
    <>
      {isMobileOrTablet ? (
        <MobilePage />
      ) : (
        <>
          <motion.div
            className={styles.container}
            variants={fadeIn(2)}
            initial="hidden"
            animate="visible"
          >
            <BlockchainInfo />
            <TopNavigation delay={2} />
            {selectedSection(navbarButtonSnapshot.selectedButton)}
            <AnimatePresence>
              {plusModalSnapshot.isOpenPlusModal ? <PlusModal /> : null}
            </AnimatePresence>
            <AnimatePresence>
              {downloadModalSnapshot.isOpen ? <FileDownloadModal /> : null}
            </AnimatePresence>
            {confettiSnapshot.isShowConfetti ? (
              <Confetti
                recycle={false}
                numberOfPieces={500}
                gravity={0.5}
                className={styles.confetti}
              />
            ) : null}
          </motion.div>
          <Toaster toastOptions={{ duration: 4500 }} />
        </>
      )}
    </>
  );
};

export default MainPage;

import useProgressBar from "../../HeroSection/Navigation/hooks/useProgressBar";
import styles from "./SaleDetails.module.scss";

const SaleDetails = () => {
  const { raisedAmount } = useProgressBar();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>$Drillx Details</h2>
      </div>
      <div className={styles.detailsTable}>
        <div className={styles.column}>
          <h3 className={styles.columnTitle}>Price</h3>
          <p className={styles.columnValue}>0.3 HSK</p>
        </div>

        <div className={styles.column}>
          <h3 className={styles.columnTitle}>Limit</h3>
          <p className={styles.columnValue}>5 per wallet</p>
        </div>

        <div className={styles.column}>
          <h3 className={styles.columnTitle}>Next Round Price</h3>
          <p className={styles.columnValue}>1 HSK ≈ 2.86 DrillX</p>
        </div>

        <div className={styles.column}>
          <h3 className={styles.columnTitle}>Next Round Quantity</h3>
          <p className={styles.columnValue}>1.5M DrillX</p>
        </div>

        <div className={styles.column}>
          <h3 className={styles.columnTitle}>Total Raised</h3>
          <p className={styles.columnValue}>{raisedAmount} HSK</p>
        </div>

        {/* <div className={styles.column}>
          <h3 className={styles.columnTitle}>Mint Date</h3>
          <p className={styles.columnValue}>5th March 2022</p>
        </div> */}
      </div>
    </div>
  );
};

export default SaleDetails;

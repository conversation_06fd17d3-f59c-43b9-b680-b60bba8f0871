import { Dispatch, SetStateAction, useEffect } from "react";
import styles from "./RedpacketClaimPage.module.scss";
import claimImage from "@/components/MainPage/RedPocket/assets/redpacketClaim.png";
import { useAccount, useNetwork } from "wagmi";
import { showError } from "@/lib/notification";
import { redPocketDetails } from "../RedPocket";
import { set } from "zod";
import useRedpacket from "@/components/MainPage/RedPocket/hooks/useRedpacket";
import ClaimSummary from "./ClaimSummary/ClaimSummary";
import { toast } from "react-toastify";

type RedpacketClaimPageProps = {
  setIsButtonClicked: Dispatch<SetStateAction<boolean>>;
  participated: boolean;
};

const RedpacketClaimPage = ({
  setIsButtonClicked,
  participated,
}: RedpacketClaimPageProps) => {
  const { chain } = useNetwork();
  const { isConnected } = useAccount();
  const { handleClaim, isClaiming, isWaitingForClaim } = useRedpacket();

  useEffect(() => {
    if (!isConnected) {
      toast.error("Please connect your wallet");
      setIsButtonClicked(false);
    }

    if (chain?.id && !redPocketDetails.supportedNetwork.includes(chain.id)) {
      setIsButtonClicked(false);
    }
  }, [chain, isConnected]);

  return (
    <>
      {!participated ? (
        <ClaimSummary setIsButtonClicked={setIsButtonClicked} />
      ) : (
        <div className={styles.container}>
          <img
            src={claimImage.src}
            alt="claim image"
            width={"100%"}
            height={"100%"}
          />
          <button
            className={styles.claimButton}
            onClick={handleClaim}
            disabled={isClaiming || isWaitingForClaim}
          >
            Claim
          </button>
          <button
            className={styles.closeButton}
            disabled={isClaiming || isWaitingForClaim}
            onClick={() => setIsButtonClicked(false)}
          >
            Close
          </button>
        </div>
      )}
    </>
  );
};

export default RedpacketClaimPage;

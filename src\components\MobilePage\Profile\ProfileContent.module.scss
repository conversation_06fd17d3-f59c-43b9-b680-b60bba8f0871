@import "@/styles/mixins.scss";
@import "@/styles/variables.module.scss";

.headerWrapper {
  @include row-between;
  margin-bottom: $margin-sm;
  .header {
    line-height: 108%;
    text-transform: uppercase;

    .title {
      font-size: $font-size-lg;
      color: $color-primary;
      font-weight: $font-weight-semibold;
    }
    .subTitle {
      font-size: $font-size-xs;
      color: $color-primary-transparent-contrast;
      font-weight: $font-weight-light;
    }
  }
}

.scrollArea {
  position: absolute;
  width: 85%;
  height: 87%;
  overflow: scroll;
  padding-bottom: $padding-lg;
  display: flex;
  flex-direction: column;
  // border: 1px solid yellow;
  &::-webkit-scrollbar {
    display: none;
  }

  .loginPrompt {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
}

.loginButton {
  position: relative;
  width: 100%;
  text-align: center;
  background: transparent;
  padding: $padding-md;
  color: $color-primary;
  font-size: $font-size-md;
  border: none;
  cursor: pointer;
  z-index: 1;
}
.loginButton::before {
  content: "";
  position: absolute;
  top: -0.5px;
  left: -0.5px;
  right: -0.5px;
  bottom: -0.5px;
  background: linear-gradient(90deg, #000000 0%, $color-primary 100%);
  border-radius: 6px;
  z-index: -1;
}
.loginButton::after {
  content: "";
  position: absolute;
  top: 0.5px;
  left: 0.5px;
  right: 0.5px;
  bottom: 0.5px;
  background: #000;
  border-radius: 4px;
  z-index: -1;
}

.loginButton:hover::before {
  box-shadow: 0 0 15px $color-primary-transparent-contrast;
}

.pointSystemWrapper {
  position: relative;
  width: 100%;
  height: 100%;

  .closeButton {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 45px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    img {
      width: 20px;
      height: 20px;
    }

    &:hover {
      background: rgba(0, 255, 255, 0.2);
    }
  }
}

.actionButton {
  width: 100%;
  padding: $padding-md;
  margin: 1rem 0;
  background: rgba(0, 255, 255, 0.2);
  border: 1px solid #00ffff;
  color: #00ffff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.2);
  }
}

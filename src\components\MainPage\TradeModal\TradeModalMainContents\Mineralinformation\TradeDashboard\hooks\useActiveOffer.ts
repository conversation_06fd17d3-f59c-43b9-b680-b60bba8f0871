import { useEffect, useLayoutEffect, useState } from "react";
import { useAccount, useContractRead, useNetwork } from "wagmi";
import useMineTokenId from "./useMineTokenId";
import { useSnapshot } from "valtio";
import { mineCardStore } from "@/stores/mineCard";
import { networkConfigs } from "@/constants/networkConfigs";
import { asteroidXMarketplaceAbi } from "@/constants/abis/AsteroidXMarketplaceABI";

type Offer = {
  offerId: bigint;
  buyer: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
  pricePerToken: bigint;
  expirationTime: bigint;
  active: boolean;
  timestamp: bigint;
  paymentToken: `0x${string}`;
};

type PaginatedOffersResponse = [Offer[], bigint];

const useActiveOffer = () => {
  const [marketpaceAddress, setMarketpaceAddress] =
    useState<`0x${string}`>("0x");
  const [asteroidMineNumber, setAsteroidMineNumber] = useState(0);
  const [allActiveOfferSortedByPrice, setAllActiveOfferSortedByPrice] =
    useState<Offer[]>([]);
  const [limit, setLimit] = useState(50n);
  const [totalOffers, setTotalOffers] = useState(0n);

  const { chain } = useNetwork();
  const { isConnected, address } = useAccount();
  const { getSelectedMineId } = useMineTokenId();
  const mineCardSnapshot = useSnapshot(mineCardStore);

  useLayoutEffect(() => {
    if (chain && !chain.unsupported) {
      setMarketpaceAddress(networkConfigs[chain.id].marketplaceAddress);
      setAsteroidMineNumber(getSelectedMineId(chain.id) ?? 0);
    } else {
      setMarketpaceAddress("0x");
      setAsteroidMineNumber(0);
    }
  }, [chain, mineCardSnapshot.selectedMine]);

  const formatUnixTimestamp = (timestamp: number): string => {
    if (!timestamp) return "";

    const date = new Date(timestamp * 1000);
    return date.toLocaleString("en-AU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const { data: paginatedOfferData } = useContractRead({
    address: marketpaceAddress,
    abi: asteroidXMarketplaceAbi,
    functionName: "getActiveOffersPaginated",
    args: [BigInt(asteroidMineNumber), 0n, limit],
    enabled:
      isConnected && marketpaceAddress !== "0x" && asteroidMineNumber !== 0,
    watch: true,
    onSuccess: (data: PaginatedOffersResponse) => {
      if (data && data[1]) {
        setTotalOffers(data[1]);
      }
    },
  }) as { data?: PaginatedOffersResponse };

  useEffect(() => {
    if (paginatedOfferData && paginatedOfferData[0] && address) {
      const offersToShow: Offer[] = paginatedOfferData[0];

      // const filteredOffers = offersToShow.filter(
      //   (offer: Offer) => offer.buyer.toLowerCase() !== address?.toLowerCase(),
      // );

      // Sort by price (highest first for offers).
      const sortedOffers = [...offersToShow].sort((a: Offer, b: Offer) =>
        Number(b.pricePerToken - a.pricePerToken),
      );

      setAllActiveOfferSortedByPrice(sortedOffers);
    } else if (!paginatedOfferData && address) {
      setAllActiveOfferSortedByPrice([]);
    }
  }, [paginatedOfferData, address]);

  return {
    allActiveOfferSortedByPrice,
    formatUnixTimestamp,
    totalOffers,
    setLimit,
  };
};

export default useActiveOffer;
